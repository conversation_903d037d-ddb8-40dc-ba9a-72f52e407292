package com.yijiupi.himalaya.supplychain.batchinventory.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProductionDateDTO implements Serializable {

    /**
     * 规格id
     */
    private Long productSpecificationId;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 批次库存小数量
     */
    private BigDecimal batchStoreCount;

    /**
     * 二级货主
     */
    private Long secOwnerId;

    /**
     * 销售库存
     */
    private BigDecimal saleCount;

    /**
     * 货位类别
     */
    private Integer locationSubcategory;

    public Integer getLocationSubcategory() {
        return locationSubcategory;
    }

    public void setLocationSubcategory(Integer locationSubcategory) {
        this.locationSubcategory = locationSubcategory;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public BigDecimal getBatchStoreCount() {
        return batchStoreCount;
    }

    public void setBatchStoreCount(BigDecimal batchStoreCount) {
        this.batchStoreCount = batchStoreCount;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public String getInventorySign() {
        return String.format("%s-%s-%s-%s", warehouseId, productSpecificationId, ownerId, secOwnerId);
    }

    public String getSkuSign() {
        return String.format("%s-%s-%s", productSpecificationId, ownerId, secOwnerId);
    }

    public BigDecimal getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(BigDecimal saleCount) {
        this.saleCount = saleCount;
    }
}
