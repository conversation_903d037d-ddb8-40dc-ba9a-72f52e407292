package com.yijiupi.himalaya.supplychain.dto.easysell;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 根据条码，经销商id,仓库查询商品信息返回DTO
 * 
 * @author: lidengfeng
 * @date 2018/8/30 17:45
 */
public class ProductDetailsDTO implements Serializable {

    /**
     * 产品信息规格id
     */
    private Long productSpecificationId;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 包装规格大单位
     */
    private String packageName;

    /**
     * 包装规格小单位
     */
    private String unitName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 库存数量
     */
    private BigDecimal totalCountMinUnit;

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public BigDecimal getTotalCountMinUnit() {
        return totalCountMinUnit;
    }

    public void setTotalCountMinUnit(BigDecimal totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }
}
