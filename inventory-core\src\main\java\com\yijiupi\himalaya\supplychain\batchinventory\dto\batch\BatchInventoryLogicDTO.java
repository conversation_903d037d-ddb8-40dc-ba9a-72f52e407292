package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 货位库存处理逻辑日志
 *
 * <AUTHOR>
 * @date 2020-05-21 10:36
 */
public class BatchInventoryLogicDTO implements Serializable {
    private static final long serialVersionUID = 3326241292227864746L;

    /**
     * 货位库存id
     */
    private String id;
    /**
     * store主键
     */
    private String productStoreId;
    /**
     * 库存小数量
     */
    private BigDecimal totalCount;
    /**
     * 批次入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;
    /**
     * 过期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * '货区或货位类型：0:货位，1:货区',
     */
    private Integer locationCategory;
    /**
     * 货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    private Integer subcategory;
    /**
     * 批属性编号
     */
    private String batchAttributeInfoNo;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * skuId
     */
    private Long productSkuId;
    /**
     * 商品规格id
     */
    private Long productSpecificationId;
    /**
     * 货主id
     * 
     * @return
     */
    private Long ownerId;
    /**
     * 包装规格系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 包装规格名称
     */
    private String specificationName;

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Integer getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    public Integer getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(Integer subcategory) {
        this.subcategory = subcategory;
    }

    public String getBatchAttributeInfoNo() {
        return batchAttributeInfoNo;
    }

    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }
}
