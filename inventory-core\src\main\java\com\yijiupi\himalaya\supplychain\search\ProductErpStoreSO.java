/**
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.search;

/**
 * 库存搜索对象
 */
public class ProductErpStoreSO extends ProductCityStoreSO {
    /**
     * 库存状态类型
     */
    private Integer storeStateType;

    @Override
    public Integer getStoreStateType() {
        return storeStateType;
    }

    @Override
    public void setStoreStateType(Integer storeStateType) {
        this.storeStateType = storeStateType;
    }
}
