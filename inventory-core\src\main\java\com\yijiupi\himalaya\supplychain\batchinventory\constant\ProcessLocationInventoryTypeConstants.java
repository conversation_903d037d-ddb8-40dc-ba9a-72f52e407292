package com.yijiupi.himalaya.supplychain.batchinventory.constant;

import java.util.Objects;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
public class ProcessLocationInventoryTypeConstants {
    /**
     *
     */
    public static final Integer DEFAULT = 1;

    /**
     * 残次品转正品
     */
    public static final Integer DEFECTIVE_TO_NORMAL = 100;

    public static boolean isDTN(Integer type) {
        if (Objects.isNull(type)) {
            return Boolean.FALSE;
        }

        return DEFECTIVE_TO_NORMAL.equals(type);
    }
}
