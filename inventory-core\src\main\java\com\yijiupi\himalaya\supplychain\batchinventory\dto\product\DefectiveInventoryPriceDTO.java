package com.yijiupi.himalaya.supplychain.batchinventory.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

public class DefectiveInventoryPriceDTO implements Serializable {

    private static final long serialVersionUID = 860440870656801118L;
    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 上限金额
     */
    private BigDecimal maxAmount;

    /**
     * 剩余金额
     */
    private BigDecimal remainingAmount;

    public DefectiveInventoryPriceDTO(BigDecimal maxAmount, BigDecimal totalAmount, BigDecimal remainingAmount) {
        this.maxAmount = maxAmount;
        this.totalAmount = totalAmount;
        this.remainingAmount = remainingAmount;
    }

    public DefectiveInventoryPriceDTO() {}

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getRemainingAmount() {
        return remainingAmount;
    }

    public void setRemainingAmount(BigDecimal remainingAmount) {
        this.remainingAmount = remainingAmount;
    }

    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }
}
