package com.yijiupi.himalaya.supplychain.inventory.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.product.*;
import com.yijiupi.himalaya.supplychain.dto.standard.ProductSpecCityStoreDTO;
import com.yijiupi.himalaya.supplychain.dto.standard.ProductWarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.search.WarehouseInventoryReportSO;
import com.yijiupi.himalaya.supplychain.search.standard.ProductSpecCityListStoreSO;
import com.yijiupi.himalaya.supplychain.search.standard.ProductWarehouseListStoreItemSO;

/**
 * 仓库库存查询服务.
 *
 * <AUTHOR>
 */
public interface IWarehouseInventoryQueryService {

    /**
     * 查询单个商品仓库库存数
     *
     * @param productSkuId
     * @param warehouseId
     * @param channel 渠道
     * @param secOwnerId 二级货主Id 可以为null
     * @return
     */
    BigDecimal getWarehouseInventory(Long productSkuId, Integer warehouseId, Integer channel, Long secOwnerId);

    /**
     * * 查询多个商品仓库库存数
     *
     * @param productSkuIds
     * @param warehouseId
     * @param channel 渠道
     * @param secOwnerId 二级货主Id 可以为null
     * @return
     */
    Map<Long, BigDecimal> getWarehouseInventoryList(List<Long> productSkuIds, Integer warehouseId, Integer channel,
        Long secOwnerId);

    // /**
    // * 查询一个产品在多个仓库下的库存数量 k(仓库id)-v(库存数量)
    // *
    // * @param productSkuId
    // * @param warehouseIdList
    // * @param channel
    // * @return
    // */
    // Map<Integer, BigDecimal> getWarehouseInventoryMap(Long productSkuId, List<Integer> warehouseIdList, Integer
    // channel, Long secOwnerId);

    // /**
    // * 查询单个商品在对应城市下所有仓库库存数总和
    // *
    // * @param productSkuId
    // * @param cityId
    // * @param channel 渠道
    // * @param secOwnerId 二级货主Id 可以为null
    // * @return
    // */
    // BigDecimal getSumWarehouseInventory(Long productSkuId, Integer cityId, Integer channel, Long secOwnerId);
    //
    // /**
    // * 查询多个商品在对应城市下的仓库库存数总和(sku就是在某一个城市下)
    // *
    // * @param productSkuIds
    // * @param channel 渠道
    // * @param secOwnerId 二级货主Id 可以为null
    // * @return
    // */
    // Map<Long, BigDecimal> getCityWarehouseInventory(Set<Long> productSkuIds, Integer channel, Long secOwnerId);

    /**
     * 获取产品信息对应城市库存
     */
    List<ProductSpecCityStoreDTO> findProductSpecCityListStore(ProductSpecCityListStoreSO so);

    /**
     * 获取产品SKU信息对应仓库库存(productSkuId,warehouseId) 问题接口 需要删除
     */
    List<ProductWarehouseStoreDTO> findProductWarehouseListStore(List<ProductWarehouseListStoreItemSO> so);

    // /**
    // * 查询配送中数量 skuId->配送中数量
    // */
    // @Deprecated
    // Map<Long, BigDecimal> getdeliveryCountList(List<Long> productSkuIds, Integer warehouseId, Integer channel);

    // /**
    // * 根据skuId、仓库Id、渠道、二级货主id、产品来源获取库存信息
    // *
    // * @param productSkuId (必填)
    // * @param warehouseId (必填)
    // * @param channel 渠道(必填)
    // * @param secOwnerId 二级货主Id 可以为null
    // * @return
    // */
    // WarehouseStoreDTO getProductInventoryDetail(Long productSkuId, Integer warehouseId, Integer channel, Long
    // secOwnerId);

    /**
     * 根据skuId（必填）、cityId(必填)、产品名称、渠道、二级货主id、产品来源获取库存信息
     */
    List<InventoryBySkuIdCityIdDTO> getProductInventoryBySkuIdCityId(List<ProductSkuForInventoryDTO> dtos);

    /**
     * 将普通skuId转换成招商skuId
     *
     * @param productSkuIds 发货城市skuid
     * @param CityId 招商城市id
     * @return k(转换前)->v(转换后)
     */
    Map<Long, Long> process2ZhaoShangSku(List<Long> productSkuIds, Integer CityId);

    // /**
    // * 查找经销商库存信息
    // *
    // * @return
    // */
    // Map<Long, ProductShopStoreReturnDTO> getProductInventoryStoreId(ProductSkuForInventoryShopDTO
    // productSkuForInventoryShopDTO);

    // /**
    // * 查找经销商库存信息
    // *
    // * @return
    // */
    // Map<String, Object> getProductInventoryStoreIdNew (ProductSkuForInventoryShopDTO productSkuForInventoryShopDTO);

    /**
     * 查询实际发货skuId
     * 
     * @param productSkuIds
     * @param warehouseId
     * @param deliveryCityId
     * @return
     */
    Map<Long, Long> getActualDeliverySkuIdMap(List<Long> productSkuIds, Integer warehouseId, Integer deliveryCityId);

    /**
     * 根据规格Id+OwnerId查询仓库库存-关联SKU
     *
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    List<WarehouseStoreDTO> getProductInventorys(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO);

    /**
     * 分页查询仓库库存-关联SKU
     * 
     * @return
     */
    PageList<WarehouseStoreDTO> getProductInventorysByPager(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO);

    /**
     * 根据规格货主分页ID-不关联SKU
     * 
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    PageList<WarehouseStoreDTO> getProductInventoryBySpecIds(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO);

    /**
     * 批量查询库存
     * 
     * @param productSkuIdList
     * @param warehouseId
     * @return
     */
    Map<Long, ProductStoreDTO> listProductInventoryBySkuIds(List<Long> productSkuIdList, Integer warehouseId);

    /**
     * 批量查询库存（包含供应商） key:-产品skuid value - 自己与供应商库存实体
     *
     * @return
     */
    Map<Long, List<ProductInventoryDTO>> findProductInventoryIncludeSupplierBySkuIds(List<Long> productSkuIdList,
        Integer warehouseId);

    /**
     * 产品查询
     * 
     * @param productSkuInfoSO
     * @return
     */
    PageList<ProductSkuInfoDTO> listProductSkuInfo(ProductSkuInfoSO productSkuInfoSO);

    /**
     * 查询产品SKU基本信息
     */
    PageList<ProductSkuInfoDTO> findProductBaseInfo(ProductSkuInfoSO productSkuInfoSO);

    /**
     * 查询产品SKU基本信息
     */
    PageList<ProductSkuInfoDTO> findProductBaseInfoByStoreCheck(ProductSkuInfoSO productSkuInfoSO);

    /**
     * 查找仓库库存（易经销）
     * 
     * @param warehouseInventoryYJXQueryDTO
     * @return
     */
    PageList<WarehouseInventoryYJXDTO>
        listWarehouseInventoryYJX(WarehouseInventoryYJXQueryDTO warehouseInventoryYJXQueryDTO);

    /**
     * 获取已下单未发货的数量（待出库数量） key: %s-%s（规格id + ownerId） value: 待出库数量
     */
    Map<String, BigDecimal> getWaitDeliveryCountMap(Integer cityId, Integer warehouseId,
        List<ProductSpecAndOwnerIdDTO> specAndOwnerIds);

    /**
     * 获取待出库产品列表
     */
    PageList<ProductWaitDeliveryDTO> listProductWaitDelivery(ProductWaitDeliverySO productWaitDeliverySO);

    /**
     * 仓库库存报表
     * 
     * @return
     */
    PageList<WarehouseInventoryReportDTO>
        listWarehouseInventoryReport(WarehouseInventoryReportSO warehouseInventoryReportSO);

    /**
     * 动销产品查询
     * 
     * @param queryDTO
     * @return
     */
    PageList<DynamicProductDTO> findDynamicProductList(DynamicProductQueryDTO queryDTO);

    /**
     * 根据 cityId 和 skuIds 查出真实发货SKUID
     * 
     * @param productSkuIds
     * @param cityId
     * @return
     */
    Map<Long, Long> getActualDeliverySkuIdS(List<Long> productSkuIds, Integer cityId);

    /**
     * 根据 skuIds 查出正常状态的SKUID
     * 
     * @return
     */
    Map<Long, Long> getNormalProductStateSkuIdS(List<Long> productSkuIds);

    /**
     * 获取产品关联的合并产品的库存总和（不包括当前查询产品本身的库存）
     * 
     * @return
     */
    Map<Long, BigDecimal> getRefProductInventoryMap(Integer cityId, Integer warehouseId, List<Long> productSkuIds);

    /**
     * 获取产品的可用库存（仓库库存-已分波次数量）
     * 
     * @return
     */
    Map<Long, BigDecimal> getEnableStoreCountMap(Integer cityId, Integer warehouseId, List<Long> skuIds);

    /**
     * 根据库存ID获取库存信息
     * 
     * @return
     */
    ProductStoreBaseDTO getProductStoreById(String id);

    /**
     * 查看产品及关联产品的销售库存
     * 
     * @return
     */
    List<ProductRelateSaleStoreDTO> getProductRelateSaleStoreMap(ProductRelateSaleStoreSO productRelateStoreSO);

    /**
     * 根据sku获取产品和其关联产品总库存数量(包含产品和关联产品库存数)
     *
     * @param productSkuIdList 产品skuId
     * @param warehouseId 仓库ID
     * @return 产品对应的库存（库存 = 产品自身库存 + 关联产品库存）
     */
    Map<Long, BigDecimal> genProductAndRefProductTotalStore(List<Long> productSkuIdList, Integer warehouseId);

    /**
     * 根据规格+货主查询仓库库存
     */
    List<ProductStoreBaseDTO> findProductStoreBySpec(ProductStoreQueryDTO productStoreQueryDTO);

    /**
     * 查询经销商在指定仓库是否有库存
     * 
     * @return
     */
    Boolean isExistProductStoreByOwner(Integer warehouseId, Long ownerId);

    /**
     * 分页查出动盘产品
     * 
     * @return
     */
    PageList<DynamicProductDTO> findDynamicProductPageList(DynamicProductQueryDTO queryDTO);

    /**
     * 查询未开启货位库存动盘产品
     */
    List<NotOpenStockDailyPickingProductDTO>
        findNotOpenStockDynamicProductList(NotOpenStockDailyPickingProductQueryDTO queryDTO);

    /**
     * 查询仓库残次品库存
     */
    List<WarehouseStoreDTO> getDisposedProductInventories(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO);

    /**
     * 根据规格+货主查找对应的二级货主
     * 
     * @return
     */
    Map<String, List<Long>> getSecOwnerIdMap(ProductSecOwnerIdQueryDTO productSecOwnerIdQueryDTO);

    /**
     * 查询预占库存
     */
    List<WarehouseStoreDTO> getPreemptProductInventories(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO);

    /**
     * 根据库存ID批量获取库存信息
     */
    List<ProductStoreBaseDTO> findProductStoreByIds(List<String> ids);

    /**
     * 在城市或全国中，根据产品信息，查询仓库库存中的存在的产品
     *
     * 查询条件：产品名称、品牌名称、城市id、一级类目id、二级类目id 查询结果：在仓库库存中存在的产品信息
     */
    PageList<WarehouseInventoryReportDTO>
        findProductInWarehouseInventory(WarehouseInventoryReportSO warehouseInventoryReportSO);

    /**
     * 根据规格、货主、城市（或仓库）查询仓库库存
     */
    List<ProductStoreBaseDTO> findStoreBySpecOwner(ProductStoreQueryDTO productStoreQueryDTO);

    /**
     * 根据中台Sku+仓库查询仓库库存
     * 
     * @return
     */
    List<ProductStoreBaseDTO> findProductStoreByUnifySkuId(ProductStoreQueryDTO productStoreQueryDTO);

    /**
     * 根据货主仓库库存下限配置，判断仓库是否有库存
     * 
     * @param warehouseId
     * @return 1：有库存，0：无库存
     */
    Boolean isHaveWarehouseInventory(Integer warehouseId);

    /**
     * 根据sku信息，查找仓库库存
     */
    PageList<WarehouseStoreDTO> listProductStoreBySkuInfo(WarehouseInventoryReportSO queryDTO);

}
