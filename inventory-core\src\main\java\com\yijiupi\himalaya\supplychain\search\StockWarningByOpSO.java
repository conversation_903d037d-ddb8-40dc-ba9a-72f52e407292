/**
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.search;

import java.io.Serializable;

/**
 * 库存预警搜索对象
 */
public class StockWarningByOpSO implements Serializable {
    /**
     * 产品名称
     */
    private String productname;
    /**
     * 产品品牌
     */
    private String productbrand;
    /**
     * 销售类型
     */
    private Integer salemode;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 预售状态
     */
    private Integer presaleState;
    /**
     * 库存状态
     */
    private Integer stockWarningState;
    /**
     * 类目Id
     */
    private Integer categoryId;
    /**
     * 产品状态
     */
    private Integer productState;
    /**
     * 库存数量范围
     */
    private Integer maxCount;
    private Integer minCount;

    public Integer getMaxCount() {
        return maxCount;
    }

    public void setMaxCount(Integer maxCount) {
        this.maxCount = maxCount;
    }

    public Integer getMinCount() {
        return minCount;
    }

    public void setMinCount(Integer minCount) {
        this.minCount = minCount;
    }

    public Integer getProductState() {
        return productState;
    }

    public void setProductState(Integer productState) {
        this.productState = productState;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getPresaleState() {
        return presaleState;
    }

    public void setPresaleState(Integer presaleState) {
        this.presaleState = presaleState;
    }

    public Integer getStockWarningState() {
        return stockWarningState;
    }

    public void setStockWarningState(Integer stockWarningState) {
        this.stockWarningState = stockWarningState;
    }

    public String getProductname() {
        return productname;
    }

    public void setProductname(String productname) {
        this.productname = productname;
    }

    public String getProductbrand() {
        return productbrand;
    }

    public void setProductbrand(String productbrand) {
        this.productbrand = productbrand;
    }

    public Integer getSalemode() {
        return salemode;
    }

    public void setSalemode(Integer salemode) {
        this.salemode = salemode;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
}
