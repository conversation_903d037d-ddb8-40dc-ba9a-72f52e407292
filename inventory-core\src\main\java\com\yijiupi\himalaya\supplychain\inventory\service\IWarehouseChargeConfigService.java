package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockWareHouseDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockWarehouseQuery;

/**
 * 仓库标准费率
 * 
 * @author: lidengfeng
 * @date 2018/9/15 11:43
 */
public interface IWarehouseChargeConfigService {

    /**
     * 新增或修改仓库标准费率
     * 
     * @param dto
     * @return: void
     */
    void saveOrUpdateChargeConfig(WarehouseChargeConfigDTO dto);

    /**
     * 仓库标准费率明细查询
     * 
     * @param warehouseId
     * @return
     */
    WarehouseChargeConfigDTO selectWarehouseChargeConfigById(Integer warehouseId);

    /**
     * 启用停用仓库标准费率
     * 
     * @param dto
     * @return
     */
    void updateChargeConfigStatus(WarehouseChargeConfigDTO dto);

    /**
     * 根据仓库id集合查询标准费率
     * 
     * @param
     * @return
     */
    Map<Integer, WarehouseChargeConfigDTO> selectWarehouseChargeList(List<Integer> list);

    /**
     * 根据服务商id，城市查询仓库信息及费用
     * 
     * @param agencyStockWarehouseQuery
     * @return
     */
    PageList<AgencyStockWareHouseDTO> findWarehouseChargeList(AgencyStockWarehouseQuery agencyStockWarehouseQuery);

    /**
     * 根据仓库id,经销商Id,商品信息查询入库单费用
     * 
     * @param shopChargeDTO
     * @return
     */
    InStockCharge findInStockTotalCharge(ShopChargeDTO shopChargeDTO);

    /**
     * 根据仓库id,经销商Id,商品信息查询出库单或委托配送费用
     * 
     * @param shopChargeDTO
     * @return
     */
    OutStockCharge findOutStockTotalCharge(ShopChargeDTO shopChargeDTO);

    /**
     * 查询自有托管仓库
     */
    PageList<WarehouseChooseReturnDTO> findWarehouseChooseList(WarehouseChooseDTO warehouseChooseDTO);

    /**
     * 城市下服务商的仓库查询
     * 
     * @param cityWarehouseQuery
     * @return
     */
    PageList<WarehouseChargeConfigDTO> findWarehouseChargeDetailList(CityWarehouseQuery cityWarehouseQuery);

    /**
     * 校验校验仓库是否可停用
     * 
     * @param productWarehouseDTO
     * @return
     */
    Boolean warehouseStock(ProductWarehouseDTO productWarehouseDTO);

    /**
     * 查询仓库信息及经销商信息 仓库管理仓库信息
     * 
     * @param cityWarehouseQuery
     * @return
     */
    WarehouseDealerDetailDTO getWarehouseDealerDetail(CityWarehouseQuery cityWarehouseQuery);

    /**
     * 仓库管理 仓库库存
     * 
     * @param warehouseServiceStoreQuery
     * @return
     */
    PageList<WarehouseServiceStoreDTO>
        findWarehouseServiceStoreList(WarehouseServiceStoreQuery warehouseServiceStoreQuery);

    /**
     * 根据仓库id查询仓库信息
     */
    WarehouseReturnDTO findByWarehouseId(CityWarehouseQuery cityWarehouseQuery);

    /**
     * 根据仓库id集合查询仓库信息
     */
    PageList<WarehouseReturnDTO> findByWarehouseList(List<Integer> warehouseIdList);

}
