package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/12
 */
public class ProductStoreBatchChangeInfoResultDTO implements Serializable {
    /**
     * 就是storeBatchChangeRecordId
     */
    private String id;

    /**
     * 批次库存变更记录id
     */
    private String storeBatchChangeRecordId;
    /**
     * 库存变更记录id
     */
    private String storeChangeRecordId;
    /**
     * 库存id
     */
    private String productStoreId;
    /**
     * 批次库存id
     */
    private String productStoreBatchId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 产品信息规格id
     */
    private Integer productSpecificationId;
    /**
     * 批次号
     */
    private String batchAttributeInfoNo;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 货主id
     */
    private Long ownerId;
    /**
     * 二级货主id
     */
    private Long secOwnerId;
    /**
     * 渠道信息
     */
    private Integer channel;

    /**
     * 移库数量
     */
    private BigDecimal totalCountMinUnit;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 生产日期
     */
    private Date productiondate;
    /**
     * 批次日期
     */
    private Date batchTime;
    /**
     * 产品skuid
     */
    private Long productSkuId;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;

    private String orderId;

    /**
     * 获取 库存id
     *
     * @return productStoreId 库存id
     */
    public String getProductStoreId() {
        return this.productStoreId;
    }

    /**
     * 设置 库存id
     *
     * @param productStoreId 库存id
     */
    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    /**
     * 获取 批次库存id
     *
     * @return productStoreBatchId 批次库存id
     */
    public String getProductStoreBatchId() {
        return this.productStoreBatchId;
    }

    /**
     * 设置 批次库存id
     *
     * @param productStoreBatchId 批次库存id
     */
    public void setProductStoreBatchId(String productStoreBatchId) {
        this.productStoreBatchId = productStoreBatchId;
    }

    /**
     * 获取 订单号
     *
     * @return orderNo 订单号
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 订单号
     *
     * @param orderNo 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 产品信息规格id
     *
     * @return productSpecificationId 产品信息规格id
     */
    public Integer getProductSpecificationId() {
        return this.productSpecificationId;
    }

    /**
     * 设置 产品信息规格id
     *
     * @param productSpecificationId 产品信息规格id
     */
    public void setProductSpecificationId(Integer productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 批次号
     *
     * @return batchAttributeInfoNo 批次号
     */
    public String getBatchAttributeInfoNo() {
        return this.batchAttributeInfoNo;
    }

    /**
     * 设置 批次号
     *
     * @param batchAttributeInfoNo 批次号
     */
    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    /**
     * 获取 货位id
     *
     * @return locationId 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     *
     * @param locationId 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 货位名称
     *
     * @return locationName 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     *
     * @param locationName 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 货主id
     *
     * @return ownerId 货主id
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置 货主id
     *
     * @param ownerId 货主id
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 二级货主id
     *
     * @return secOwnerId 二级货主id
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主id
     *
     * @param secOwnerId 二级货主id
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取 渠道信息
     *
     * @return channel 渠道信息
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 渠道信息
     *
     * @param channel 渠道信息
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 批次库存变更记录id
     *
     * @return storeBatchChangeRecordId 批次库存变更记录id
     */
    public String getStoreBatchChangeRecordId() {
        return this.storeBatchChangeRecordId;
    }

    /**
     * 设置 批次库存变更记录id
     *
     * @param storeBatchChangeRecordId 批次库存变更记录id
     */
    public void setStoreBatchChangeRecordId(String storeBatchChangeRecordId) {
        this.storeBatchChangeRecordId = storeBatchChangeRecordId;
    }

    /**
     * 获取 库存变更记录id
     *
     * @return storeChangeRecordId 库存变更记录id
     */
    public String getStoreChangeRecordId() {
        return this.storeChangeRecordId;
    }

    /**
     * 设置 库存变更记录id
     *
     * @param storeChangeRecordId 库存变更记录id
     */
    public void setStoreChangeRecordId(String storeChangeRecordId) {
        this.storeChangeRecordId = storeChangeRecordId;
    }

    /**
     * 获取 移库数量
     *
     * @return totalCountMinUnit 移库数量
     */
    public BigDecimal getTotalCountMinUnit() {
        return this.totalCountMinUnit;
    }

    /**
     * 设置 移库数量
     *
     * @param totalCountMinUnit 移库数量
     */
    public void setTotalCountMinUnit(BigDecimal totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }

    /**
     * 获取 创建时间
     *
     * @return createTime 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 生产日期
     *
     * @return productiondate 生产日期
     */
    public Date getProductiondate() {
        return this.productiondate;
    }

    /**
     * 设置 生产日期
     *
     * @param productiondate 生产日期
     */
    public void setProductiondate(Date productiondate) {
        this.productiondate = productiondate;
    }

    /**
     * 获取 批次日期
     *
     * @return batchTime 批次日期
     */
    public Date getBatchTime() {
        return this.batchTime;
    }

    /**
     * 设置 批次日期
     *
     * @param batchTime 批次日期
     */
    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    /**
     * 获取 产品skuid
     *
     * @return productSkuId 产品skuid
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品skuid
     *
     * @param productSkuId 产品skuid
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @return source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @param source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 就是storeBatchChangeRecordId
     *
     * @return id 就是storeBatchChangeRecordId
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 就是storeBatchChangeRecordId
     *
     * @param id 就是storeBatchChangeRecordId
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取
     *
     * @return orderId
     */
    public String getOrderId() {
        return this.orderId;
    }

    /**
     * 设置
     *
     * @param orderId
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
