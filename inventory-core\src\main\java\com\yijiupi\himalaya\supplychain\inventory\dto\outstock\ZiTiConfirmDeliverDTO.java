package com.yijiupi.himalaya.supplychain.inventory.dto.outstock;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: ZiTiConfirmDeliverDTO
 * @description: 自提订单出库
 * @date 2023-05-09 10:15
 */
public class ZiTiConfirmDeliverDTO implements Serializable {
    /**
     * oms订单id列表
     */
    private List<Long> businessIdList;
    /**
     * 溯源码
     */
    private List<Long> productSourceCodeIdList;
    /**
     * 操作人id
     */
    private Integer userId;
    /**
     * 备注
     */
    private String remark;

    public ZiTiConfirmDeliverDTO() {
    }

    public ZiTiConfirmDeliverDTO(List<Long> businessIdList, List<Long> productSourceCodeIdList, Integer userId) {
        this.businessIdList = businessIdList;
        this.productSourceCodeIdList = productSourceCodeIdList;
        this.userId = userId;
    }

    /**
     * 获取 oms订单id列表
     *
     * @return businessIdList oms订单id列表
     */
    public List<Long> getBusinessIdList() {
        return this.businessIdList;
    }

    /**
     * 设置 oms订单id列表
     *
     * @param businessIdList oms订单id列表
     */
    public void setBusinessIdList(List<Long> businessIdList) {
        this.businessIdList = businessIdList;
    }

    /**
     * 获取 溯源码
     *
     * @return productSourceCodeIdList 溯源码
     */
    public List<Long> getProductSourceCodeIdList() {
        return this.productSourceCodeIdList;
    }

    /**
     * 设置 溯源码
     *
     * @param productSourceCodeIdList 溯源码
     */
    public void setProductSourceCodeIdList(List<Long> productSourceCodeIdList) {
        this.productSourceCodeIdList = productSourceCodeIdList;
    }

    /**
     * 获取 操作人id
     *
     * @return userId 操作人id
     */
    public Integer getUserId() {
        return this.userId;
    }

    /**
     * 设置 操作人id
     *
     * @param userId 操作人id
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取 备注
     *
     * @return remark 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置 备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }
}
