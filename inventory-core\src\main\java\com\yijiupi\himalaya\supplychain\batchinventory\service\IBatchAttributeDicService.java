package com.yijiupi.himalaya.supplychain.batchinventory.service;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeDicDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeDicQueryDTO;

/**
 * 自定义批属性管理(字典)
 *
 * <AUTHOR> 2018/4/9
 */
public interface IBatchAttributeDicService {

    /**
     * 新增自定义批属性管理字典
     */
    void addBatchAttributeDic(BatchAttributeDicDTO batchAttributeDicDTO);

    /**
     * 停用,启用
     *
     * @param batchAttributeDicDTO
     */
    void updateBatchAttributeDicState(BatchAttributeDicDTO batchAttributeDicDTO);

    /**
     * 编辑
     *
     * @param batchAttributeDicDTO
     */
    void updateBatchAttributeDic(BatchAttributeDicDTO batchAttributeDicDTO);

    /**
     * 删除
     */
    int deleteById(Long id);

    /**
     * 列表(分页)
     *
     * @param batchAttributeDicQueryDTO
     * @return
     */
    PageList<BatchAttributeDicDTO> findBatchAttributeDicList(BatchAttributeDicQueryDTO batchAttributeDicQueryDTO);
}
