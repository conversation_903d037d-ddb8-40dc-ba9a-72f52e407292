/**
 * Copyright © 2020 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate;

import java.io.Serializable;

/**
 * @author: yanpin
 * @date: 2020年9月23日 下午2:15:57
 */
public class ProductSpecInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long productSpecId;
    // 久批的就是null，非久批的有值
    private Long ownerId;

    @Override
    public String toString() {
        return "SpecInfoDTO [productSpecId=" + productSpecId + ", ownerId=" + ownerId + "]";
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }
}
