package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 虚仓实配出库
 *
 */
public class ConfirmOutStockDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库Id
     */
    private Integer warehouseId;

    /**
     * 订单号
     */
    private List<String> orderNos;

    /**
     * 任务Id
     */
    private Long fetchTaskId;

    /**
     * 任务类型
     */
    private Byte FetchTaskType;

    /**
     * 更新人Id
     */
    private Integer updateUserId;

    public Integer getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Integer updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    public Long getFetchTaskId() {
        return fetchTaskId;
    }

    public void setFetchTaskId(Long fetchTaskId) {
        this.fetchTaskId = fetchTaskId;
    }

    public Byte getFetchTaskType() {
        return FetchTaskType;
    }

    public void setFetchTaskType(Byte fetchTaskType) {
        FetchTaskType = fetchTaskType;
    }
}
