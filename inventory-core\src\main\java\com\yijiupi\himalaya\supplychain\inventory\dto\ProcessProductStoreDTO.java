package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

public class ProcessProductStoreDTO implements Serializable {

    /**
     * 库存更改数据
     */
    private List<WarehouseInventoryChangeDTO> warehouseInventoryChangeDTOS;

    /**
     * 是否检查仓库库存
     */
    private Boolean checkWarehouseInventory;

    /**
     * 是否修改发货数量
     */
    private Boolean isUpdateDeliveryCount;

    /**
     * 是否修改仓库库存
     */
    private Boolean isUpdateProductStore;

    /**
     * 是否修改批次库存
     */
    private Boolean isUpdateProductBatchStore;

    /**
     * 是否处理不存在的sku
     */
    private Boolean isSkipNotExitsSku;

    public List<WarehouseInventoryChangeDTO> getWarehouseInventoryChangeDTOS() {
        return warehouseInventoryChangeDTOS;
    }

    public void setWarehouseInventoryChangeDTOS(List<WarehouseInventoryChangeDTO> warehouseInventoryChangeDTOS) {
        this.warehouseInventoryChangeDTOS = warehouseInventoryChangeDTOS;
    }

    public Boolean getCheckWarehouseInventory() {
        return checkWarehouseInventory;
    }

    public void setCheckWarehouseInventory(Boolean checkWarehouseInventory) {
        this.checkWarehouseInventory = checkWarehouseInventory;
    }

    public Boolean getUpdateDeliveryCount() {
        return isUpdateDeliveryCount;
    }

    public void setUpdateDeliveryCount(Boolean updateDeliveryCount) {
        isUpdateDeliveryCount = updateDeliveryCount;
    }

    public Boolean getUpdateProductStore() {
        return isUpdateProductStore;
    }

    public void setUpdateProductStore(Boolean updateProductStore) {
        isUpdateProductStore = updateProductStore;
    }

    public Boolean getUpdateProductBatchStore() {
        return isUpdateProductBatchStore;
    }

    public void setUpdateProductBatchStore(Boolean updateProductBatchStore) {
        isUpdateProductBatchStore = updateProductBatchStore;
    }

    public Boolean getSkipNotExitsSku() {
        return isSkipNotExitsSku;
    }

    public void setSkipNotExitsSku(Boolean skipNotExitsSku) {
        isSkipNotExitsSku = skipNotExitsSku;
    }
}
