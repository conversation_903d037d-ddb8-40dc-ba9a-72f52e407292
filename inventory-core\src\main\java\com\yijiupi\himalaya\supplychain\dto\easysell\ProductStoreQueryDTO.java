package com.yijiupi.himalaya.supplychain.dto.easysell;

import java.io.Serializable;

/**
 * 易经销库存查询 根据仓库编号、经销商编号查询商品库存信息参数DTO
 * 
 * @author: lid<PERSON>feng
 * @date: 2018/8/13 14:35
 */
public class ProductStoreQueryDTO implements Serializable {

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 经销商编号
     */
    private Long shopId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页数
     */
    private Integer pageSize;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
