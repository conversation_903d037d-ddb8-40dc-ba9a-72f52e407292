package com.yijiupi.himalaya.supplychain.inventory.dto.instock;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单、退货单同步新增
 *
 * @author: tangkun
 * @date: 2017年10月11日 上午10:30:47
 */
public class InventoryOrderItemDTO implements Serializable {
    /**
     * item的主键
     */
    private Long id;
    /**
     * 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    private Integer orgId;
    /**
     * 关联订单项id
     */
    private Long orderId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * skuId（赠品SKUId可能为null）
     */
    private Long skuId;
    /**
     * 品牌
     */
    private String productBrand;
    /**
     * 类目
     */
    private String categoryName;
    /**
     * 包装规格
     */
    private String specName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specQuantity;
    /**
     * 销售规格名称
     */
    private String saleSpec;
    /**
     * 销售规格系数
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 大单位名称
     */
    private String packageName;
    /**
     * 小单位名称
     */
    private String unitName;
    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;
    /**
     * 销售模式 代营(0),自营(1),合作(2),寄售(3),大商转自营(4),大商转配送(5),入驻(6),总部寄售(7),独家包销(8)
     */
    private Byte saleMode;
    /**
     * 退货数量 - 供应商需求【SCM2-9938】之后为小单位总数量
     */
    private BigDecimal returnCount;
    /**
     * 销售数量 - 出库表加字段, 供应商需求【SCM2-9938】之后为小单位总数量
     */
    private BigDecimal saleCount;
    /**
     * 销售单位 出库表加字段
     */
    private String sellUnit;
    /**
     * 产品总金额
     */
    private BigDecimal totalAmount;
    /**
     * 应付金额
     */
    private BigDecimal payAmount;

    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source = 0;

    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel = 0;

    /**
     * 货主Id
     */
    private Long ownerId;

    /**
     * 规格Id
     */
    private Long productSpecification_Id;

    /**
     * 入库周转区id
     */
    private Long locationId;

    /**
     * 入库周转区名称
     */
    private String locationName;

    /**
     * 关联订单项id
     */
    private Long businessItemId;

    /**
     * 入库关联数据
     */
    private List<InventoryOrderRelatedItemDTO> inventoryOrderRelatedItemDTOS;

    /**
     * 二级货主
     */
    private Long secOwnerId;

    /**
     * oms订单项id
     */
    private Long omsOrderItemId;

    /**
     * 残次品小单位总数量
     */
    private BigDecimal defectiveTotalCount;

    /**
     * 残次品货位
     */
    private Long defectiveLocationId;

    /**
     * 残次品货位
     */
    private String defectiveLocationName;

    /**
     * 生产日期
     */
    private Date productionDate;

    public Long getProductSpecification_Id() {
        return productSpecification_Id;
    }

    public void setProductSpecification_Id(Long productSpecification_Id) {
        this.productSpecification_Id = productSpecification_Id;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 item的主键(oms的businessItemId)
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 item的主键(oms的businessItemId)
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 关联订单项id
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 关联订单项id
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 商品名称
     */
    public String getProductName() {
        return this.productName;
    }

    /**
     * 设置 商品名称
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 skuId（赠品SKUId可能为null）
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 skuId（赠品SKUId可能为null）
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    /**
     * 获取 品牌
     */
    public String getProductBrand() {
        return this.productBrand;
    }

    /**
     * 设置 品牌
     */
    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    /**
     * 获取 类目
     */
    public String getCategoryName() {
        return this.categoryName;
    }

    /**
     * 设置 类目
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     * 获取 包装规格
     */
    public String getSpecName() {
        return this.specName;
    }

    /**
     * 设置 包装规格
     */
    public void setSpecName(String specName) {
        this.specName = specName;
    }

    /**
     * 获取 包装规格大小单位转换系数
     */
    public BigDecimal getSpecQuantity() {
        return this.specQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取 销售规格名称
     */
    public String getSaleSpec() {
        return this.saleSpec;
    }

    /**
     * 设置 销售规格名称
     */
    public void setSaleSpec(String saleSpec) {
        this.saleSpec = saleSpec;
    }

    /**
     * 获取 销售规格系数
     */
    public BigDecimal getSaleSpecQuantity() {
        return this.saleSpecQuantity;
    }

    /**
     * 设置 销售规格系数
     */
    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    /**
     * 获取 大单位名称
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 大单位名称
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 小单位名称
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 小单位名称
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 小单位总数量
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置 小单位总数量
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取 销售模式 代营(0),自营(1),合作(2),寄售(3),大商转自营(4),大商转配送(5),入驻(6),总部寄售(7),独家包销(8)
     */
    public Byte getSaleMode() {
        return this.saleMode;
    }

    /**
     * 设置 销售模式 代营(0),自营(1),合作(2),寄售(3),大商转自营(4),大商转配送(5),入驻(6),总部寄售(7),独家包销(8)
     */
    public void setSaleMode(Byte saleMode) {
        this.saleMode = saleMode;
    }

    /**
     * 获取 退货数量 - 供应商需求【SCM2-9938】之后为小单位总数量
     */
    public BigDecimal getReturnCount() {
        return this.returnCount;
    }

    /**
     * 设置 退货数量
     */
    public void setReturnCount(BigDecimal returnCount) {
        this.returnCount = returnCount;
    }

    /**
     * 获取 销售数量 出库表加字段
     */
    public BigDecimal getSaleCount() {
        return this.saleCount;
    }

    /**
     * 设置 销售数量 出库表加字段
     */
    public void setSaleCount(BigDecimal saleCount) {
        this.saleCount = saleCount;
    }

    /**
     * 获取 销售单位 出库表加字段
     */
    public String getSellUnit() {
        return this.sellUnit;
    }

    /**
     * 设置 销售单位 出库表加字段
     */
    public void setSellUnit(String sellUnit) {
        this.sellUnit = sellUnit;
    }

    /**
     * 获取 产品总金额
     */
    public BigDecimal getTotalAmount() {
        return this.totalAmount;
    }

    /**
     * 设置 产品总金额
     */
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 获取 应付金额
     */
    public BigDecimal getPayAmount() {
        return this.payAmount;
    }

    /**
     * 设置 应付金额
     */
    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    @Override
    public String toString() {
        return "InventoryOrderItemDTO{" + "id=" + id + ", orgId=" + orgId + ", orderId=" + orderId + ", productName='"
            + productName + '\'' + ", skuId=" + skuId + ", productBrand='" + productBrand + '\'' + ", categoryName='"
            + categoryName + '\'' + ", specName='" + specName + '\'' + ", specQuantity=" + specQuantity + ", saleSpec='"
            + saleSpec + '\'' + ", saleSpecQuantity=" + saleSpecQuantity + ", packageName='" + packageName + '\''
            + ", unitName='" + unitName + '\'' + ", unitTotalCount=" + unitTotalCount + ", saleMode=" + saleMode
            + ", returnCount=" + returnCount + ", saleCount=" + saleCount + ", sellUnit='" + sellUnit + '\''
            + ", totalAmount=" + totalAmount + ", payAmount=" + payAmount + ", source=" + source + ", channel="
            + channel + '}';
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long getBusinessItemId() {
        return businessItemId;
    }

    public void setBusinessItemId(Long businessItemId) {
        this.businessItemId = businessItemId;
    }

    public List<InventoryOrderRelatedItemDTO> getInventoryOrderRelatedItemDTOS() {
        return inventoryOrderRelatedItemDTOS;
    }

    public void setInventoryOrderRelatedItemDTOS(List<InventoryOrderRelatedItemDTO> inventoryOrderRelatedItemDTOS) {
        this.inventoryOrderRelatedItemDTOS = inventoryOrderRelatedItemDTOS;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getOmsOrderItemId() {
        return omsOrderItemId;
    }

    public void setOmsOrderItemId(Long omsOrderItemId) {
        this.omsOrderItemId = omsOrderItemId;
    }

    public BigDecimal getDefectiveTotalCount() {
        return defectiveTotalCount;
    }

    public void setDefectiveTotalCount(BigDecimal defectiveTotalCount) {
        this.defectiveTotalCount = defectiveTotalCount;
    }

    public Long getDefectiveLocationId() {
        return defectiveLocationId;
    }

    public void setDefectiveLocationId(Long defectiveLocationId) {
        this.defectiveLocationId = defectiveLocationId;
    }

    public String getDefectiveLocationName() {
        return defectiveLocationName;
    }

    public void setDefectiveLocationName(String defectiveLocationName) {
        this.defectiveLocationName = defectiveLocationName;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }
}
