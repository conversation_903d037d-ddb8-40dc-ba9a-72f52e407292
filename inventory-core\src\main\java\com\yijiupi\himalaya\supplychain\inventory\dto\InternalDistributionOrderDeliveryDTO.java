package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 内配单-配送单.
 *
 * <AUTHOR>
 */
public class InternalDistributionOrderDeliveryDTO implements Serializable {

    /**
     * 发货城市
     */
    private Integer deliveryCity;

    /**
     * 发货仓库ID.
     */
    private Integer deliveryWarehouseId;

    /**
     * 收货城市
     */
    private Integer receiptCity;

    /**
     * 收货仓库ID.
     */
    private Integer receiptWarehouseId;

    /**
     * 配送单信息
     */
    private List<InventoryDeliveryJiupiOrder> deliveryJiupiOrders;

    public Integer getDeliveryCity() {
        return deliveryCity;
    }

    public void setDeliveryCity(Integer deliveryCity) {
        this.deliveryCity = deliveryCity;
    }

    public Integer getDeliveryWarehouseId() {
        return deliveryWarehouseId;
    }

    public void setDeliveryWarehouseId(Integer deliveryWarehouseId) {
        this.deliveryWarehouseId = deliveryWarehouseId;
    }

    public Integer getReceiptCity() {
        return receiptCity;
    }

    public void setReceiptCity(Integer receiptCity) {
        this.receiptCity = receiptCity;
    }

    public Integer getReceiptWarehouseId() {
        return receiptWarehouseId;
    }

    public void setReceiptWarehouseId(Integer receiptWarehouseId) {
        this.receiptWarehouseId = receiptWarehouseId;
    }

    public List<InventoryDeliveryJiupiOrder> getDeliveryJiupiOrders() {
        return deliveryJiupiOrders;
    }

    public void setDeliveryJiupiOrders(List<InventoryDeliveryJiupiOrder> deliveryJiupiOrders) {
        this.deliveryJiupiOrders = deliveryJiupiOrders;
    }
}