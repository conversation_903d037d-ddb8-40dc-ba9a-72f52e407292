package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import java.io.Serializable;

/**
 * 批属性适用类型配置属性查询
 */
public class BatchAttributeTemplateRuleTypeQueryDTO implements Serializable {

    private static final long serialVersionUID = -8253324134040835040L;
    /**
     * 批次编号
     */
    private String batchAttributeInfoNo;

    /**
     * 配置类型 适用仓库(1),适用货主(2),适用类名(3),适用品牌(4)
     */
    private Byte ruleType;

    /**
     * 配置类型属性值ID
     */
    private String attributeValueId;

    public String getBatchAttributeInfoNo() {
        return batchAttributeInfoNo;
    }

    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    public Byte getRuleType() {
        return ruleType;
    }

    public void setRuleType(Byte ruleType) {
        this.ruleType = ruleType;
    }

    public String getAttributeValueId() {
        return attributeValueId;
    }

    public void setAttributeValueId(String attributeValueId) {
        this.attributeValueId = attributeValueId;
    }
}
