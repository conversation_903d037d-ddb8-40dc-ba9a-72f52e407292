/**
 * Copyright © 2019 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 销售库存信息
 * 
 * @author: yanpin
 * @date: 2019年12月6日 上午11:23:19
 */
public class OmsInventoryInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    // 城市id
    private Integer orgId;
    private Integer warehouseId;
    private Long specificationId;
    private Long ownerId;
    private String ownerName;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 小单位销售数量
     */
    private BigDecimal saleInventoryCount;
    /**
     * 产品名称ID
     */
    private Long productSkuId;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSpecificationId() {
        return specificationId;
    }

    public void setSpecificationId(Long specificationId) {
        this.specificationId = specificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getSaleInventoryCount() {
        return saleInventoryCount;
    }

    public void setSaleInventoryCount(BigDecimal saleInventoryCount) {
        this.saleInventoryCount = saleInventoryCount;
    }

    public String getInternalKey() {
        return String.format("%s-%s", this.ownerId == null ? "null" : this.ownerId, this.specificationId);
    }
}
