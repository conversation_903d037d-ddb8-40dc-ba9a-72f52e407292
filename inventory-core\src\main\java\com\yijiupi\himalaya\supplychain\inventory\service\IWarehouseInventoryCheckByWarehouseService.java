package com.yijiupi.himalaya.supplychain.inventory.service;

import com.yijiupi.himalaya.supplychain.inventory.dto.check.CheckStoreInventoryByWarehouseInfoDTO;

/**
 * 库存对账服务
 *
 * <AUTHOR>
 * @date 2019/1/8 15:22
 */
public interface IWarehouseInventoryCheckByWarehouseService {

    /**
     * 根据ERP库存，存库存对账
     */
    void checkStoreInventoryByWarehouseId(Integer warehouseId, Integer opUserId, boolean isSnap);
    /**
     * 根据ERP库存，存库存对账
     */
    void checkStoreInventoryByOpenCenterWarehouse(CheckStoreInventoryByWarehouseInfoDTO dto);

    /**
     * 根据易款店仓库存，存库存对账
     */
    void checkStoreInventoryByEasyChain(Integer warehouseId, Integer opUserId);

    /**
     * 仓库停用校验
     */
    Boolean checkWarehouseDisable(Integer cityId, Integer warehouseId);
}
