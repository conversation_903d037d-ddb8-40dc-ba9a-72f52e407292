package com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/23
 */
public class OrderCenterSaleInventoryQueryDTO implements Serializable {
    /**
     * 城市ID
     */
    private Integer cityId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 商品规格ID
     */
    private Long productSpecId;
    /**
     * 一级货主ID
     */
    private Long ownerId;
    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    /**
     * 获取 城市ID
     *
     * @return cityId 城市ID
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市ID
     *
     * @param cityId 城市ID
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库ID
     *
     * @return warehouseId 仓库ID
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库ID
     *
     * @param warehouseId 仓库ID
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 商品规格ID
     *
     * @return productSpecId 商品规格ID
     */
    public Long getProductSpecId() {
        return this.productSpecId;
    }

    /**
     * 设置 商品规格ID
     *
     * @param productSpecId 商品规格ID
     */
    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    /**
     * 获取 一级货主ID
     *
     * @return ownerId 一级货主ID
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置 一级货主ID
     *
     * @param ownerId 一级货主ID
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 二级货主ID
     *
     * @return secOwnerId 二级货主ID
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主ID
     *
     * @param secOwnerId 二级货主ID
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }
}
