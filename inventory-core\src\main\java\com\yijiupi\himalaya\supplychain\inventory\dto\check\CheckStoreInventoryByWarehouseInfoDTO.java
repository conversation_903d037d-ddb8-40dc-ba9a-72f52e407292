package com.yijiupi.himalaya.supplychain.inventory.dto.check;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/7/11
 */
public class CheckStoreInventoryByWarehouseInfoDTO implements Serializable {

    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 操作人
     */
    private Integer opUserId;
    /**
     * 是否是快照
     */
    private boolean isSnap;
    /**
     * 1 是老版本；2 是新版本
     */
    private Integer version = OLD_VERSION;

    public static Integer OLD_VERSION = 1;

    public static Integer NEW_VERSION = 2;

    public CheckStoreInventoryByWarehouseInfoDTO(Integer warehouseId, Integer opUserId, boolean isSnap, Integer version) {
        this.warehouseId = warehouseId;
        this.opUserId = opUserId;
        this.isSnap = isSnap;
        this.version = version;
    }

    public CheckStoreInventoryByWarehouseInfoDTO() {
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 操作人
     *
     * @return opUserId 操作人
     */
    public Integer getOpUserId() {
        return this.opUserId;
    }

    /**
     * 设置 操作人
     *
     * @param opUserId 操作人
     */
    public void setOpUserId(Integer opUserId) {
        this.opUserId = opUserId;
    }

    /**
     * 获取 是否是快照
     *
     * @return isSnap 是否是快照
     */
    public boolean getIsIsSnap() {
        return this.isSnap;
    }

    /**
     * 设置 是否是快照
     *
     * @param isSnap 是否是快照
     */
    public void setIsSnap(boolean isSnap) {
        this.isSnap = isSnap;
    }

    /**
     * 获取 1 是老版本；2 是新版本
     *
     * @return version 1 是老版本；2 是新版本
     */
    public Integer getVersion() {
        return this.version;
    }

    /**
     * 设置 1 是老版本；2 是新版本
     *
     * @param version 1 是老版本；2 是新版本
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    public static boolean isNewVersion(Integer version) {
        if (Objects.isNull(version)) {
            return Boolean.FALSE;
        }
        if (NEW_VERSION.equals(version)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }
}
