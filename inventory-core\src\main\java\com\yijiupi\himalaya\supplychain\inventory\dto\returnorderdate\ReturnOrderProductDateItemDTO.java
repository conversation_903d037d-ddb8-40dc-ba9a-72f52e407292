package com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate;

import java.io.Serializable;
import java.util.Date;

public class ReturnOrderProductDateItemDTO implements Serializable {

    private static final long serialVersionUID = 348634973500595554L;

    /**
     * 规格ID
     */
    private Long specId;

    /**
     * 货主ID
     */
    private Long ownerId;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    /**
     * 生产日期
     */
    private Date productDate;

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Date getProductDate() {
        return productDate;
    }

    public void setProductDate(Date productDate) {
        this.productDate = productDate;
    }
}
