package com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion;

import java.io.Serializable;
import java.util.List;

/**
 * 促销批次库存通知
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
public class PromotionStoreBatchMessage implements Serializable {
    /**
     * 仓库
     */
    private Integer orgId;
    /**
     * 仓库
     */
    private Integer warehouseId;

    /**
     * skuId集合
     */
    private List<Long> productSkuIds;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<Long> getProductSkuIds() {
        return productSkuIds;
    }

    public void setProductSkuIds(List<Long> productSkuIds) {
        this.productSkuIds = productSkuIds;
    }
}
