package com.yijiupi.himalaya.supplychain.inventory.dto.agency;

import java.io.Serializable;
import java.util.List;

/**
 * 微酒出入库操作dto
 *
 */
public class WJStockOperateDTO implements Serializable {
    /**
     * 经销商事件类型
     */
    private Integer eventType;
    /**
     * 仓库iD
     */
    private Integer warehouseId;
    /**
     * 城市iD
     */
    private Integer cityId;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 出入单项
     */
    private List<WJStockProductDTO> items;

    /**
     * 是否更新交易平台库存.
     */
    private Boolean hasUpdateOPInventory = true;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Boolean getHasUpdateOPInventory() {
        return hasUpdateOPInventory;
    }

    public void setHasUpdateOPInventory(Boolean hasUpdateOPInventory) {
        this.hasUpdateOPInventory = hasUpdateOPInventory;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    /**
     * 获取 仓库iD
     *
     * @return warehouseId 仓库iD
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库iD
     *
     * @param warehouseId 仓库iD
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 创建人
     *
     * @return createUser 创建人
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置 创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 出入单项
     *
     * @return items 出入单项
     */
    public List<WJStockProductDTO> getItems() {
        return this.items;
    }

    /**
     * 设置 出入单项
     *
     * @param items 出入单项
     */
    public void setItems(List<WJStockProductDTO> items) {
        this.items = items;
    }

    /**
     * 获取 订单类型
     *
     * @return orderType 订单类型
     */
    public Integer getOrderType() {
        return this.orderType;
    }

    /**
     * 设置 订单类型
     *
     * @param orderType 订单类型
     */
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }
}
