package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.List;

import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryChangeRecordDTO;

/**
 * 库存变更记录操作(仓库库存)
 *
 * @author: yupan
 * @date: 2018年12月20日
 */
public interface IProductInventoryRecordManagerService {

    /**
     * 批量新增仓库库存变更记录
     * 
     * @param productInventoryChangeRecordDTOList
     */
    void saveProductStoreChangeRecord(List<ProductInventoryChangeRecordDTO> productInventoryChangeRecordDTOList);

    /**
     * 更新仓库库存变更记录
     */
    void updateProductInventoryChangeRecord(ProductInventoryChangeRecordDTO dto);
}
