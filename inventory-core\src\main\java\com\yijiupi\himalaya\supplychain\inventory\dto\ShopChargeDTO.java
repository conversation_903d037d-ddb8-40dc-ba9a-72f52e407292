package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 经销商费用配置
 * 
 * @author: lidengfeng
 * @date 2018/9/25 15:37
 */
public class ShopChargeDTO implements Serializable {

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 经销商id
     */
    private Long shopId;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 提货时间
     */
    private Date deliveryTime;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 产品信息
     */
    private List<ProductChargeDTO> items;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public List<ProductChargeDTO> getItems() {
        return items;
    }

    public void setItems(List<ProductChargeDTO> items) {
        this.items = items;
    }

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    /**
     * 获取 提货时间
     * 
     * @return
     */
    public Date getDeliveryTime() {
        return deliveryTime;
    }

    /**
     * 设置 提货时间
     * 
     * @param deliveryTime
     */
    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * 获取 订单总金额
     * 
     * @return
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 设置 订单总金额
     * 
     * @param totalAmount
     */
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
}
