package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * 批次库存变更记录查询
 *
 * <AUTHOR>
 * @date 2018/12/14 15:26
 */
public class ProductStoreBatchChangeRecordQueryDTO extends PageCondition {

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 批次库存id
     */
    private String storeBatchId;

    /**
     * 产品库存ID
     */
    private String productStoreId;

    /**
     * 仓库库存变更记录ID
     */
    private String changeRecordId;

    /**
     * 变更时间 - 开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 变更时间 - 结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    private String orderNo;

    private Long productSkuId;

    private List<String> orderNos;

    /**
     * 查询是否删除的sku：0 否，1 是
     */
    private List<Byte> deleted = Collections.singletonList((byte)0);

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getStoreBatchId() {
        return storeBatchId;
    }

    public void setStoreBatchId(String storeBatchId) {
        this.storeBatchId = storeBatchId;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public String getChangeRecordId() {
        return changeRecordId;
    }

    public void setChangeRecordId(String changeRecordId) {
        this.changeRecordId = changeRecordId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    public List<Byte> getDeleted() {
        return deleted;
    }

    public void setDeleted(List<Byte> deleted) {
        this.deleted = deleted;
    }
}
