package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 产品及关联产品的销售库存
 *
 * <AUTHOR>
 */
public class ProductRelateSaleStoreDTO implements Serializable {

    private static final long serialVersionUID = 8015450481542374917L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * 产品信息规格id
     */
    private Long productSpecId;

    /**
     * 货主ID
     */
    private Long ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 销售库存小单位总数量
     */
    private BigDecimal saleStoreTotalCount;

    /**
     * 关联的产品库存
     */
    private List<ProductRelateSaleStoreDTO> refProductStoreList;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public BigDecimal getSaleStoreTotalCount() {
        return saleStoreTotalCount;
    }

    public void setSaleStoreTotalCount(BigDecimal saleStoreTotalCount) {
        this.saleStoreTotalCount = saleStoreTotalCount;
    }

    public List<ProductRelateSaleStoreDTO> getRefProductStoreList() {
        return refProductStoreList;
    }

    public void setRefProductStoreList(List<ProductRelateSaleStoreDTO> refProductStoreList) {
        this.refProductStoreList = refProductStoreList;
    }
}
