package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import java.io.Serializable;
import java.util.List;

/**
 * 批属性适用类型配置属性查询
 */
public class BatchAttributeValueQueryDTO implements Serializable {

    private static final long serialVersionUID = -1747772952169265623L;
    /**
     * 批次编号
     */
    private List<String> batchNoList;

    /**
     * 配置类型 适用服务商(0), 适用仓库(1),适用货主(2),适用类名(3),适用品牌(4)
     */
    private Byte ruleType;

    /**
     * 配置类型属性值ID
     */
    private String attributeValueId;

    public List<String> getBatchNoList() {
        return batchNoList;
    }

    public void setBatchNoList(List<String> batchNoList) {
        this.batchNoList = batchNoList;
    }

    public Byte getRuleType() {
        return ruleType;
    }

    public void setRuleType(Byte ruleType) {
        this.ruleType = ruleType;
    }

    public String getAttributeValueId() {
        return attributeValueId;
    }

    public void setAttributeValueId(String attributeValueId) {
        this.attributeValueId = attributeValueId;
    }
}
