<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductSkuMapper">
    <resultMap id="BaseSkuResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.productSku.ProductSkuPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Name" property="name" jdbcType="VARCHAR"/>
        <result column="City_id" property="cityId" jdbcType="INTEGER"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="ProductSku_Id" property="productSkuId" jdbcType="BIGINT"/>
        <result column="specificationName" property="specificationName" jdbcType="INTEGER"/>
        <result column="packageName" property="packageName" jdbcType="INTEGER"/>
        <result column="unitName" property="unitName" jdbcType="INTEGER"/>
        <result column="packageQuantity" property="packageQuantity" jdbcType="DECIMAL"/>
        <result column="Source" property="source" jdbcType="INTEGER"/>
        <result column="Company_Id" property="companyId" jdbcType="BIGINT"/>
        <result column="OwnerType" property="ownerType" jdbcType="INTEGER"/>
        <result column="OwnerName" property="ownerName" jdbcType="VARCHAR"/>
        <result column="MonthOfShelfLife" property="monthOfShelfLife" jdbcType="VARCHAR"/>
        <result column="ShelfLifeUnit" property="shelfLifeUnit" jdbcType="VARCHAR"/>
        <result column="ShelfLifeLongTime" jdbcType="BIT" property="shelfLifeLongTime"/>
    </resultMap>

    <select id="findBatchAllDTOBySkuAndCityId"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO">
        select
        psb.id as storeBatchId,
        psku.ProductSku_Id as productSkuId,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.location_id as locationId,
        psb.location_name as locationName,
        loc.category as locationCategory,
        psb.subcategory as subcategory,
        psb.expiretime as expireTime,
        psb.batchtime as batchTime,
        psb.productionDate as productionDate,
        loc.area_Id as areaId,
        loc.area as areaName,
        psb.BatchAttributeInfoNo,
        ps.SecOwner_Id as secOwnerId,
        ps.id as storeId
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id = ps.Id
        INNER JOIN location loc on loc.Id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where psku.ProductSku_Id = #{dto.productSkuId,jdbcType=BIGINT}
        AND ps.channel = #{dto.channel,jdbcType=TINYINT}
        AND ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        <if test="dto.productionDate!=null">
            and psb.productionDate = #{dto.productionDate}
        </if>
        <if test="dto.batchTime!=null">
            and psb.batchtime = #{dto.batchTime}
        </if>
        <if test="dto.secOwnerId!=null">AND ps.SecOwner_Id = #{dto.secOwnerId,jdbcType=INTEGER}</if>
        <if test="dto.locationId!=null">AND psb.location_id = #{dto.locationId,jdbcType=BIGINT}</if>
        <if test="dto.excludeNegativeFlag != null and dto.excludeNegativeFlag == true">
            and psb.totalcount_minunit >= 0
        </if>
        order by psb.subcategory,psb.batchtime ASC
    </select>
    <select id="findBatchAllDTOBySku"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO">
        select
        psb.id as storeBatchId,
        psku.ProductSku_Id as productSkuId,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.location_id as locationId,
        psb.location_name as locationName,
        loc.category as locationCategory,
        psb.subcategory as subcategory,
        psb.expiretime as expireTime,
        psb.batchtime as batchTime,
        loc.area_Id as areaId,
        loc.area as areaName,
        psb.BatchAttributeInfoNo,
        psb.productiondate as productionDate
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id = ps.Id
        INNER JOIN location loc on loc.Id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where psb.totalcount_minunit>0
        AND psku.ProductSku_Id = #{dto.productSkuId,jdbcType=BIGINT}
        <if test="dto.channel!=null">AND ps.channel = #{dto.channel,jdbcType=TINYINT}</if>
        AND ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        <if test="dto.source!=null">AND psku.Source = #{dto.source,jdbcType=TINYINT}</if>
        <if test="dto.secOwnerId!=null">AND ps.SecOwner_Id = #{dto.secOwnerId,jdbcType=INTEGER}</if>
        <if test="dto.locationId!=null">AND psb.location_id = #{dto.locationId,jdbcType=BIGINT}</if>
        <if test="dto.locationSubcategory!=null">AND loc.subcategory = #{dto.locationSubcategory,jdbcType=TINYINT}</if>
        order by psb.subcategory,psb.batchtime ASC
    </select>
    <select id="findBatchHuoQuDTOBySkuAndLocation"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO">
        select loc.Id as locationId,loc.Name as locationName,loc.category locationCategory,loc.subcategory,loc.sequence
        as sequence,
        tmp.storeBatchId,tmp.productSkuId,tmp.productStoreId,tmp.totalCount,
        tmp.expireTime,tmp.batchTime,tmp.BatchAttributeInfoNo, tmp.secOwnerId
        from location loc
        left join
        (select
        psb.id as storeBatchId,
        psku.ProductSku_Id as productSkuId,
        ps.SecOwner_Id as secOwnerId,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.location_id as locationId,
        psb.location_name as locationName,
        psb.locationCategory as locationCategory,
        psb.subcategory as subcategory,
        psb.expiretime as expireTime,
        psb.batchtime as batchTime,
        psb.BatchAttributeInfoNo
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id = ps.Id
        INNER JOIN location loc on loc.warehouse_id = ps.warehouse_id and loc.id = psb.location_id
        where psb.totalcount_minunit>0
        AND psku.ProductSku_Id = #{dto.productSkuId,jdbcType=BIGINT}
        AND ps.channel = #{dto.channel,jdbcType=TINYINT}
        AND ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        AND psku.Source = #{dto.source,jdbcType=TINYINT}
        AND psb.location_id = #{dto.locationId,jdbcType=BIGINT}
        <if test="dto.secOwnerId!=null">AND ps.SecOwner_Id = #{dto.secOwnerId,jdbcType=INTEGER}</if>
        <if test="dto.locationSubcategory != null">
            AND psb.subcategory = #{dto.locationSubcategory,jdbcType=TINYINT}
        </if>
        ) tmp on loc.id = tmp.locationId
        where loc.id = #{dto.locationId,jdbcType=BIGINT}
        order by loc.subcategory,tmp.batchtime ASC
    </select>
    <select id="findBatchHuoWeiDTOBySkuAndArea"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO">
        select loc.id as locationId,
        loc.name as locationName,
        loc.category as locationCategory,
        loc.subcategory,
        tmpLoc.storeBatchId,
        tmpLoc.productSkuId,
        tmpLoc.secOwnerId,
        tmpLoc.productStoreId,
        tmpLoc.totalCount,
        tmpLoc.expireTime,
        tmpLoc.batchTime,
        tmpLoc.BatchAttributeInfoNo,
        loc.area_Id as areaId,
        loc.area as areaName,
        loc.sequence as sequence
        from location loc
        LEFT JOIN
        (
        select
        psb.id as storeBatchId,
        psku.ProductSku_Id as productSkuId,
        ps.SecOwner_Id as secOwnerId,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.location_id as locationId,
        psb.location_name as locationName,
        loc.category as locationCategory,
        loc.subcategory as subcategory,
        psb.expiretime as expireTime,
        psb.batchtime as batchTime,
        psb.BatchAttributeInfoNo
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id = ps.Id
        INNER JOIN location loc on loc.category = 0 and loc.Id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where psb.totalcount_minunit > 0
        AND psku.ProductSku_Id = #{dto.productSkuId,jdbcType=BIGINT}
        AND ps.channel = #{dto.channel,jdbcType=TINYINT}
        AND ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        AND psku.Source = #{dto.source,jdbcType=TINYINT}
        AND loc.area_Id = #{dto.areaId,jdbcType=BIGINT}
        <if test="dto.locationSubcategory != null">
            AND loc.subcategory = #{dto.locationSubcategory,jdbcType=TINYINT}
        </if>
        <if test="dto.secOwnerId!=null">AND ps.SecOwner_Id = #{dto.secOwnerId,jdbcType=INTEGER}</if>
        <if test="dto.locationId!=null">AND psb.location_Id = #{dto.locationId,jdbcType=BIGINT}</if>
        ) tmpLoc on tmpLoc.locationId = loc.id
        where loc.Category = 0 and loc.area_id = #{dto.areaId,jdbcType=BIGINT}
        <if test="dto.locationSubcategory != null">
            AND loc.subcategory = #{dto.locationSubcategory,jdbcType=TINYINT}
        </if>
        order by subcategory,batchtime ASC
    </select>
    <select id="findBatchHuoWeiDTOBySku"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO">
        select
        psb.id as storeBatchId,
        psku.ProductSku_Id as productSkuId,
        ps.SecOwner_Id as secOwnerId,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.location_id as locationId,
        psb.location_name as locationName,
        loc.category as locationCategory,
        psb.subcategory as subcategory,
        psb.expiretime as expireTime,
        psb.batchtime as batchTime,
        loc.area_Id as areaId,
        loc.area as areaName,
        loc.sequence as sequence,
        psb.BatchAttributeInfoNo
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id = ps.Id
        INNER JOIN location loc on loc.category = 0 and loc.Id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where psb.totalcount_minunit>0
        AND psku.ProductSku_Id = #{dto.productSkuId,jdbcType=BIGINT}
        AND ps.channel = #{dto.channel,jdbcType=TINYINT}
        AND ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        AND psku.Source = #{dto.source,jdbcType=TINYINT}
        <if test="dto.secOwnerId!=null">AND ps.SecOwner_Id = #{dto.secOwnerId,jdbcType=INTEGER}</if>
        <if test="dto.areaId!=null">AND loc.area_Id = #{dto.areaId,jdbcType=BIGINT}</if>
        <if test="dto.locationId!=null">AND psb.location_Id = #{dto.locationId,jdbcType=BIGINT}</if>
        <if test="dto.locationSubcategory != null">
            AND psb.subcategory = #{dto.locationSubcategory,jdbcType=TINYINT}
        </if>
        order by psb.subcategory,psb.batchtime ASC
    </select>
    <select id="findBatchDTOBySku"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO">
        select
        psb.id as storeBatchId,
        psku.ProductSku_Id,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.location_id as locationId,
        psb.location_name as locationName,
        loc.category as locationCategory,
        psb.subcategory as subcategory,
        psb.expiretime as expireTime,
        psb.batchtime as batchTime,
        loc.area_Id as areaId,
        loc.area as areaName,
        psb.BatchAttributeInfoNo
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id = ps.Id
        INNER JOIN location loc on loc.Id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where
        psb.totalcount_minunit>0
        AND psku.ProductSku_Id = #{po.productSkuId,jdbcType=BIGINT}
        AND ps.channel = #{po.channel,jdbcType=TINYINT}
        AND ps.Warehouse_Id = #{po.warehouseId,jdbcType=INTEGER}
        AND psku.Source = #{po.source,jdbcType=TINYINT}
        <if test="po.secOwnerId!=null">AND ps.SecOwner_Id = #{po.secOwnerId,jdbcType=INTEGER}</if>
        <if test="po.locationAssignType!=null">AND psb.subcategory = #{po.locationAssignType,jdbcType=INTEGER}</if>
        <if test="po.locationAssignLimitType!=null">AND psb.subcategory !=
            #{po.locationAssignLimitType,jdbcType=INTEGER}
        </if>
    </select>
    <select id="findBatchDTOBySkuList"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO">
        select
        psb.id as storeBatchId,
        psku.ProductSku_Id as productSkuId,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.location_id as locationId,
        psb.location_name as locationName,
        loc.category as locationCategory,
        psb.expiretime as expireTime,
        psb.batchtime as batchTime,
        psb.productiondate,
        ps.channel,
        loc.subcategory as subcategory,
        loc.area_Id as areaId,
        loc.area as areaName,
        psb.BatchAttributeInfoNo,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId,
        ps.SecOwner_Id as secOwnerId
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id = ps.Id
        INNER JOIN location loc on loc.id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where psb.totalcount_minunit > 0
        AND psku.ProductSku_Id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.productSkuId,jdbcType=BIGINT}
        </foreach>
        AND ps.channel in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.channel,jdbcType=TINYINT}
        </foreach>
        AND ps.Warehouse_Id = #{warehouseId}
        AND psku.Source in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.source,jdbcType=TINYINT}
        </foreach>
    </select>
    <select id="findBatchLocationDTOByLocationId"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO">
        select
        psb.id as storeBatchId,
        psb.productstore_id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        psb.totalcount_minunit as totalCount,
        psb.location_id as locationId,
        psb.location_name as locationName,
        loc.category as locationCategory,
        psb.subcategory as subcategory,
        psb.expiretime as expireTime,
        psb.batchtime as batchTime,
        loc.area_Id as areaId,
        loc.area as areaName,
        psb.BatchAttributeInfoNo, psb.batchProperty, psb.businessType
        from productstorebatch psb
        inner JOIN productstore ps on ps.id = psb.productstore_id
        INNER JOIN location loc on loc.Id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where
        psb.totalcount_minunit>0
        <if test="locationId!=null">AND psb.location_id = #{locationId,jdbcType=BIGINT}</if>
        <if test="channel!=null">AND ps.channel = #{channel,jdbcType=TINYINT}</if>
        order by psb.subcategory,psb.batchtime ASC
    </select>
    <select id="findBatchDTOBySkuListAndCategory"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO">
        select
        psb.id as storeBatchId,
        psku.ProductSku_Id as productSkuId,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.location_id as locationId,
        psb.location_name as locationName,
        loc.category as locationCategory,
        psb.expiretime as expireTime,
        psb.productiondate,
        psb.batchtime as batchTime,
        loc.subcategory as subcategory,
        loc.area_Id as areaId,
        loc.area as areaName,
        loc.sequence,
        ps.channel,
        psb.BatchAttributeInfoNo,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId,
        ps.SecOwner_Id as secOwnerId
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id = ps.Id
        INNER JOIN (select locL.id,locL.name,locL.area_Id,locL.area,locL.category,locL.subcategory,locL.warehouse_id,
        locL.sequence
        from location locL
        inner join location locArea on locL.area_id = locArea.id
        where locArea.subcategory = #{subcategory}
        union all
        select
        locArea.id,locArea.name,locArea.area_Id,locArea.area,locArea.category,locArea.subcategory,locArea.warehouse_id,
        locArea.sequence
        from location locArea
        where locArea.subcategory = #{subcategory}
        ) loc on loc.id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where psb.totalcount_minunit>0
        AND psku.ProductSku_Id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.productSkuId,jdbcType=BIGINT}
        </foreach>
        AND ps.channel in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.channel,jdbcType=TINYINT}
        </foreach>
        AND ps.Warehouse_Id = #{item.warehouseId,jdbcType=INTEGER}
        AND psku.Source in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.source,jdbcType=TINYINT}
        </foreach>
    </select>
    <select id="getProductSkuListByIds" resultMap="BaseSkuResultMap">
        SELECT
        sku.id,
        sku.Name,
        sku.City_id,
        sku.ProductSpecification_Id,
        sku.ProductSku_Id,
        sku.specificationName,
        sku.packageName,
        sku.unitName,
        sku.packageQuantity,
        sku.Source,
        sku.Company_Id,
        sku.OwnerName,
        IFNULL(own.ownerType,0) as OwnerType,
        IFNULL(info.MonthOfShelfLife, sku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(info.ShelfLifeUnit, sku.ShelfLifeUnit) as shelfLifeUnit,
        info.ShelfLifeLongTime
        FROM
        productsku sku
        LEFT JOIN owner own on sku.company_id is not null and sku.company_Id = own.id
        INNER JOIN productinfo info on sku.ProductInfo_Id = info.Id
        WHERE
        ProductSku_Id in
        <foreach collection="productSkuIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findBatchBySkuListAndCategory"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO">
        select
        psb.id as storeBatchId,
        psku.ProductSku_Id as productSkuId,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.location_id as locationId,
        psb.location_name as locationName,
        loc.category as locationCategory,
        psb.expiretime as expireTime,
        psb.productiondate,
        psb.batchtime as batchTime,
        loc.subcategory as subcategory,
        loc.area_Id as areaId,
        loc.area as areaName,
        loc.sequence,
        ps.channel,
        psb.BatchAttributeInfoNo,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId,
        ps.SecOwner_Id as secOwnerId
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id = ps.Id
        INNER JOIN (select locL.id,locL.name,locL.area_Id,locL.area,locL.category,locL.subcategory,locL.warehouse_id,
        locL.sequence
        from location locL
        inner join location locArea on locL.area_id = locArea.id
        where locArea.subcategory in
        <foreach collection="subcategorys" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        union all
        select
        locArea.id,locArea.name,locArea.area_Id,locArea.area,locArea.category,locArea.subcategory,locArea.warehouse_id,
        locArea.sequence
        from location locArea
        where locArea.subcategory in
        <foreach collection="subcategorys" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ) loc on loc.id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where psb.totalcount_minunit>0
        and psku.ProductSku_Id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.productSkuId,jdbcType=BIGINT}
        </foreach>
        AND ps.channel in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.channel,jdbcType=TINYINT}
        </foreach>
        AND ps.Warehouse_Id = #{item.warehouseId,jdbcType=INTEGER}
        AND psku.Source in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.source,jdbcType=TINYINT}
        </foreach>
    </select>
</mapper>