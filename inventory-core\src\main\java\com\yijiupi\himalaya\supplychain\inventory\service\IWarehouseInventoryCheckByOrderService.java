package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.List;

import com.yijiupi.himalaya.supplychain.dto.product.BatchProductStoreChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.BatchInventoryQueryRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryCheckByOrderDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate.ReturnOrderProductDateDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate.ReturnOrderProductDateQueryDTO;

/**
 * 根据订单矫正仓库库存
 *
 * <AUTHOR>
 * @date 11/28/20 3:05 PM
 */
public interface IWarehouseInventoryCheckByOrderService {

    /**
     * 根据订单矫正仓库库存
     */
    void checkInventoryByOrder(List<InventoryCheckByOrderDTO> orderDTOList);

    /**
     * 根据单号还原仓库库存
     */
    void restoreInventoryByOrderNo(List<String> orderNos, Integer warehouseId);

    /**
     * 根据参数过滤出有库存变更的单号
     *
     * @return
     */
    List<String> findStoreChangeOrderNos(List<String> orderNos, String excludeUser);

    /**
     * 根据单号还原仓库批次库存
     */
    void restoreBatchInventoryByOrderNo(List<String> orderNos);

    /**
     * 根据单号查询批次变更明细
     */
    List<BatchProductStoreChangeRecordDTO> queryBatchInventoryByOrderNo(List<String> orderNos);

    /**
     * 根据单号查询批次变更明细
     */
    List<BatchProductStoreChangeRecordDTO> queryBatchInventory(BatchInventoryQueryRecordDTO queryRecordDTO);

    /**
     * 根据单号查询明细数量
     *
     * @param orderNos
     * @return
     */
    Long queryCountBatchInventoryByOrderNo(List<String> orderNos);

    /**
     * 查询生产日期
     * 
     * @param queryDTO
     * @return
     */
    List<ReturnOrderProductDateDTO> queryProductDate(ReturnOrderProductDateQueryDTO queryDTO);

}
