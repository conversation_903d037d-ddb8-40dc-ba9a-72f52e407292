package com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.base.search.PageCondition;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductSpecIdAndOwnerIdDTO;

/**
 * 促销批次库存查询dto
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
public class PromotionStoreBatchQueryDTO extends PageCondition {
    /**
     * 仓库
     */
    private Integer warehouseId;

    /**
     * skuId集合
     */
    private List<Long> skuIdList;

    /**
     * 规格Id+OwnerId
     */
    private List<ProductSpecIdAndOwnerIdDTO> specIdAndOwnerIdList;

    /**
     * 批属性编号
     */
    private List<String> batchAttributeInfoNos;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 货未id集合
     */
    private List<Long> locationIdList;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<Long> getSkuIdList() {
        return skuIdList;
    }

    public void setSkuIdList(List<Long> skuIdList) {
        this.skuIdList = skuIdList;
    }

    public List<ProductSpecIdAndOwnerIdDTO> getSpecIdAndOwnerIdList() {
        return specIdAndOwnerIdList;
    }

    public void setSpecIdAndOwnerIdList(List<ProductSpecIdAndOwnerIdDTO> specIdAndOwnerIdList) {
        this.specIdAndOwnerIdList = specIdAndOwnerIdList;
    }

    public List<String> getBatchAttributeInfoNos() {
        return batchAttributeInfoNos;
    }

    public void setBatchAttributeInfoNos(List<String> batchAttributeInfoNos) {
        this.batchAttributeInfoNos = batchAttributeInfoNos;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public List<Long> getLocationIdList() {
        return locationIdList;
    }

    public void setLocationIdList(List<Long> locationIdList) {
        this.locationIdList = locationIdList;
    }
}
