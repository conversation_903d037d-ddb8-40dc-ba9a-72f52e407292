package com.yijiupi.himalaya.supplychain.search.standard;

import java.io.Serializable;

public class ProductWarehouseListStoreItemSO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer warehouseId;

    private Long productSkuId;

    private Integer channel;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 产品来源
     */
    private Integer source;

    public String getGroupKey() {
        return String.format("%s%s%s%s", warehouseId, channel, secOwnerId, source);
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }
}
