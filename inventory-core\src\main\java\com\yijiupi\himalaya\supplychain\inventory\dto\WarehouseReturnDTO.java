/*
 * @ClassName WarehousePODTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-01-21 17:28:23
 */
package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class WarehouseReturnDTO implements Serializable {
    /**
     * @Fields id
     */
    private Integer id;
    /**
     * @Fields name
     */
    private String name;
    /**
     * @Fields remark
     */
    private String remark;
    /**
     * @Fields state
     */
    private Integer state;
    /**
     * @Fields province
     */
    private String province;
    /**
     * @Fields city
     */
    private String city;
    /**
     * @Fields county
     */
    private String county;
    /**
     * @Fields street
     */
    private String street;
    /**
     * @Fields detailAddress
     */
    private String detailAddress;
    /**
     * @Fields createTime
     */
    private Date createTime;
    /**
     * @Fields createUserId
     */
    private Integer createUserId;
    /**
     * @Fields lastUpdateTime
     */
    private Date lastUpdateTime;
    /**
     * @Fields lastUpdateUserId
     */
    private Integer lastUpdateUserId;
    /**
     * @Fields warehouseType 仓库类型：城市仓库(0), 总部仓库(1), 轻加盟仓库(2),经销商仓库(3),服务商仓库(4)
     */
    private Byte warehouseType;
    /**
     * @Fields warehouseClass 仓库种类：实仓(0)，虚仓(1)
     */
    private Byte warehouseClass;
    /**
     * @Fields shopId 仓库所属人：店铺ID
     */
    private Long shopId;
    /**
     * @Fields cityId
     */
    private Integer cityId;
    /**
     * @Fields longitude 下单经度
     */
    private BigDecimal longitude;
    /**
     * @Fields latitude 下单纬度
     */
    private BigDecimal latitude;

    /**
     * 获取
     */
    public Integer getId() {
        return id;
    }

    /**
     * 设置
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取
     */
    public String getName() {
        return name;
    }

    /**
     * 设置
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 获取
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * 获取
     */
    public Integer getState() {
        return state;
    }

    /**
     * 设置
     */
    public void setState(Integer state) {
        this.state = state;
    }

    /**
     * 获取
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * 获取
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * 获取
     */
    public String getCounty() {
        return county;
    }

    /**
     * 设置
     */
    public void setCounty(String county) {
        this.county = county == null ? null : county.trim();
    }

    /**
     * 获取
     */
    public String getStreet() {
        return street;
    }

    /**
     * 设置
     */
    public void setStreet(String street) {
        this.street = street == null ? null : street.trim();
    }

    /**
     * 获取
     */
    public String getDetailAddress() {
        return detailAddress;
    }

    /**
     * 设置
     */
    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress == null ? null : detailAddress.trim();
    }

    /**
     * 获取
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取
     */
    public Integer getCreateUserId() {
        return createUserId;
    }

    /**
     * 设置
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * 获取
     */
    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * 设置
     */
    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    /**
     * 获取 仓库类型：城市仓库(0), 总部仓库(1), 轻加盟仓库(2),经销商仓库(3),服务商仓库(4)
     */
    public Byte getWarehouseType() {
        return warehouseType;
    }

    /**
     * 设置 仓库类型：城市仓库(0), 总部仓库(1), 轻加盟仓库(2),经销商仓库(3),服务商仓库(4)
     */
    public void setWarehouseType(Byte warehouseType) {
        this.warehouseType = warehouseType;
    }

    /**
     * 获取 仓库种类：实仓(0)，虚仓(1)
     */
    public Byte getWarehouseClass() {
        return warehouseClass;
    }

    /**
     * 设置 仓库种类：实仓(0)，虚仓(1)
     */
    public void setWarehouseClass(Byte warehouseClass) {
        this.warehouseClass = warehouseClass;
    }

    /**
     * 获取 仓库所属人：店铺ID
     */
    public Long getShopId() {
        return shopId;
    }

    /**
     * 设置 仓库所属人：店铺ID
     */
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    /**
     * 获取
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 下单经度
     */
    public BigDecimal getLongitude() {
        return longitude;
    }

    /**
     * 设置 下单经度
     */
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    /**
     * 获取 下单纬度
     */
    public BigDecimal getLatitude() {
        return latitude;
    }

    /**
     * 设置 下单纬度
     */
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
}