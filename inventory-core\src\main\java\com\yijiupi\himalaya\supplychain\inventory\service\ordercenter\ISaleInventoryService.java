package com.yijiupi.himalaya.supplychain.inventory.service.ordercenter;

import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInventoryQueryParam;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.SaleInventoryInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.SaleInventoryQueryParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-12 14:10
 **/
public interface ISaleInventoryService {

    /**
     * 根据城市id和规格查询销售库存接口
     *
     * @param param 查询条件
     * @return 查询结果
     */
    List<SaleInventoryInfoDTO> findSaleInventoryByCityIdAndProductSpecId(SaleInventoryQueryParam param);

    /**
     * 根据仓库和商品规格集合查询销售库存信息
     *
     * @param param 查询条件
     * @return 查询结果
     */
    List<SaleInventoryInfoDTO> findInventoryByProductOwners(ProductOwnerInventoryQueryParam param);

}
