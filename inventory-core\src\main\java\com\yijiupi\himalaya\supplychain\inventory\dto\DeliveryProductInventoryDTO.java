package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class DeliveryProductInventoryDTO implements Serializable {

    /**
     * 产品SKUID.
     */
    private Long productSkuId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 规格系数
     */
    private BigDecimal specQuantity;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 大件名称
     */
    private String packageName;

    /**
     * 小件名称
     */
    private String unitName;

    /**
     * 产品销售规格系数.
     */
    private BigDecimal saleSpecQuantity;

    /**
     * 货主Id
     */
    private Long ownerId;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    /**
     * 规格Id
     */
    private Long productSpecificationId;

    /**
     * 配送出库数量.
     */
    private BigDecimal deliverCount;

    /**
     * 缺货数量.
     */
    private BigDecimal lakeCount;

    /**
     * 库存数量
     */
    private BigDecimal warehouseInventory;

    /**
     * 订单数量
     */
    private Integer orderCount;

    /**
     * 订单编号
     */
    private List<String> orderNos;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public BigDecimal getDeliverCount() {
        return deliverCount;
    }

    public void setDeliverCount(BigDecimal deliverCount) {
        this.deliverCount = deliverCount;
    }

    public BigDecimal getLakeCount() {
        return lakeCount;
    }

    public void setLakeCount(BigDecimal lakeCount) {
        this.lakeCount = lakeCount;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public BigDecimal getWarehouseInventory() {
        return warehouseInventory;
    }

    public void setWarehouseInventory(BigDecimal warehouseInventory) {
        this.warehouseInventory = warehouseInventory;
    }

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }
}
