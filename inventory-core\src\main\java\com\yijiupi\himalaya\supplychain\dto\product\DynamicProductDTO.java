package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;

public class DynamicProductDTO implements Serializable {

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * 产品SKUid
     */
    private Long productSkuId;

    /**
     * 品牌
     */
    private String productBrand;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 货主ID
     */
    private Long ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    /**
     * 二级货主名称
     */
    private String secOwnerName;

    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 包装规格小单位
     */
    private String unitName;

    /**
     * 小单位数量
     */
    private BigDecimal unitCount;

    /**
     * 包装规格大单位
     */
    private String packageName;

    /**
     * 大单位数量
     */
    private BigDecimal packageCount;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 货位id
     */
    private Long locationId;

    /**
     * 货位名称
     */
    private String locationName;

    /**
     * 一级类目ID
     */
    private Long statisticsClass;

    /**
     * 一级类目名称
     */
    private String statisticsClassName;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public String getSecOwnerName() {
        return secOwnerName;
    }

    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long getStatisticsClass() {
        return statisticsClass;
    }

    public void setStatisticsClass(Long statisticsClass) {
        this.statisticsClass = statisticsClass;
    }

    public String getStatisticsClassName() {
        return statisticsClassName;
    }

    public void setStatisticsClassName(String statisticsClassName) {
        this.statisticsClassName = statisticsClassName;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }
}
