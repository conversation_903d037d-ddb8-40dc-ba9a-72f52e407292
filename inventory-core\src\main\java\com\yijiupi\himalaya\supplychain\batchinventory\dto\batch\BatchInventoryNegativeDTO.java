package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;

/**
 * 负库存仓库对象
 *
 * <AUTHOR>
 * @date 2020-04-14 15:22
 */
public class BatchInventoryNegativeDTO implements Serializable {
    private static final long serialVersionUID = -5580665581157005670L;

    /**
     * 省
     */
    private String province;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 仓库
     */
    private String warehouse;

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouse() {
        return warehouse;
    }

    public void setWarehouse(String warehouse) {
        this.warehouse = warehouse;
    }
}
