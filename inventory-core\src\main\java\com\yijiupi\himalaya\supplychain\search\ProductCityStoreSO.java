/**
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.search;

import java.io.Serializable;

/**
 * 库存搜索对象
 */
public class ProductCityStoreSO implements Serializable {
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品品牌
     */
    private String productBrand;
    /**
     * 类目Id
     */
    private Integer categoryId;
    /**
     * 城市Id
     */
    private Integer cityId;
    /**
     * 城市
     */
    private String city;
    /**
     * 县
     */
    private String county;
    /**
     * 省
     */
    private String province;
    /**
     * 所属类型
     */
    private Integer ownerType;
    /**
     * 所属人名称
     */
    private String ownerName;
    /**
     * 库存状态类型
     */
    private Integer storeStateType;
    /**
     * sku状态
     */
    private Integer skuState;
    /**
     * 销售模式
     */
    private Integer saleMode;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Integer getStoreStateType() {
        return storeStateType;
    }

    public void setStoreStateType(Integer storeStateType) {
        this.storeStateType = storeStateType;
    }

    public Integer getSkuState() {
        return skuState;
    }

    public void setSkuState(Integer skuState) {
        this.skuState = skuState;
    }

    public Integer getSaleMode() {
        return saleMode;
    }

    public void setSaleMode(Integer saleMode) {
        this.saleMode = saleMode;
    }
}
