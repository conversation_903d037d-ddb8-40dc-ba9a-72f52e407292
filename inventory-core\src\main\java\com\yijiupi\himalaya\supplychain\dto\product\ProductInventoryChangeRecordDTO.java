package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品库存变更记录
 *
 * <AUTHOR>
 */
public class ProductInventoryChangeRecordDTO implements Serializable {

    private static final long serialVersionUID = 8398964960481901311L;
    /**
     * 主键ID
     */
    private String id;
    /**
     * 库存ID
     */
    private String productStoreId;
    /**
     * 城市ID
     */
    private Integer cityId;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 酒批事件类型
     */
    private Integer jiupiEventType;
    /**
     * ERP事件类型
     */
    private Integer erpEventType;
    /**
     * 库存变更数量(大单位,设置为0)
     */
    private BigDecimal countMaxUnit;
    /**
     * 库存变更数量(小单位)
     */
    private BigDecimal countMinUnit;
    /**
     * 库存变更数量总计
     */
    private BigDecimal totalCount;
    /**
     * 变更前原库存
     */
    private BigDecimal sourceTotalCount;
    /**
     * 描述
     */
    private String description;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 变更类型(1:销售库存 2:仓库库存)
     */
    private Integer storeType;

    /**
     * 操作来源,0:供应链，1:ERP,2:易经销
     */
    private Integer systemSource;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    public Integer getErpEventType() {
        return erpEventType;
    }

    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public BigDecimal getCountMaxUnit() {
        return countMaxUnit;
    }

    public void setCountMaxUnit(BigDecimal countMaxUnit) {
        this.countMaxUnit = countMaxUnit;
    }

    public BigDecimal getCountMinUnit() {
        return countMinUnit;
    }

    public void setCountMinUnit(BigDecimal countMinUnit) {
        this.countMinUnit = countMinUnit;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getSourceTotalCount() {
        return sourceTotalCount;
    }

    public void setSourceTotalCount(BigDecimal sourceTotalCount) {
        this.sourceTotalCount = sourceTotalCount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public Integer getSystemSource() {
        return systemSource;
    }

    public void setSystemSource(Integer systemSource) {
        this.systemSource = systemSource;
    }
}
