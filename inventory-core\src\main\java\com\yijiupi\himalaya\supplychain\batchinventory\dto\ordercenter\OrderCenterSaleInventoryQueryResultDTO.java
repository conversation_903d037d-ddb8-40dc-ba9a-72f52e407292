package com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/23
 */
public class OrderCenterSaleInventoryQueryResultDTO implements Serializable {

    private Long id;
    /**
     * cityid+仓库id+规格id+一级货主id+二级货主id <br />
     * 704-7041-481531-null-1
     */
    private String rowKey;

    private Integer cityId;

    private Integer warehouseId;

    private Long productSpecId;

    private Long ownerId;

    private Long secOwnerId;

    private BigDecimal saleInventoryCount;

    private Date manufactureTime;
    /**
     * 481531-null-1 <br />
     * 规格id-一级货主id-二级货主id
     */
    private String internalKey;

    /**
     * 获取
     *
     * @return id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置
     *
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取
     *
     * @return rowKey
     */
    public String getRowKey() {
        return this.rowKey;
    }

    /**
     * 设置
     *
     * @param rowKey
     */
    public void setRowKey(String rowKey) {
        this.rowKey = rowKey;
    }

    /**
     * 获取
     *
     * @return cityId
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置
     *
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取
     *
     * @return productSpecId
     */
    public Long getProductSpecId() {
        return this.productSpecId;
    }

    /**
     * 设置
     *
     * @param productSpecId
     */
    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    /**
     * 获取
     *
     * @return ownerId
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置
     *
     * @param ownerId
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取
     *
     * @return secOwnerId
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置
     *
     * @param secOwnerId
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取
     *
     * @return saleInventoryCount
     */
    public BigDecimal getSaleInventoryCount() {
        return this.saleInventoryCount;
    }

    /**
     * 设置
     *
     * @param saleInventoryCount
     */
    public void setSaleInventoryCount(BigDecimal saleInventoryCount) {
        this.saleInventoryCount = saleInventoryCount;
    }

    /**
     * 获取
     *
     * @return manufactureTime
     */
    public Date getManufactureTime() {
        return this.manufactureTime;
    }

    /**
     * 设置
     *
     * @param manufactureTime
     */
    public void setManufactureTime(Date manufactureTime) {
        this.manufactureTime = manufactureTime;
    }

    /**
     * 获取
     *
     * @return internalKey
     */
    public String getInternalKey() {
        return String.format("%s-%s-%s", this.getProductSpecId(), getOwnerId(), getSecOwnerId());
    }

}
