package com.yijiupi.himalaya.supplychain.inventory.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.erp.ERPStoreVO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.check.CheckStoreInventoryByCityInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.DisposedProductInventorDTO;

/**
 * 库存对账服务
 * 
 * <AUTHOR>
 * @date 2019/1/8 15:22
 */
public interface IWarehouseInventoryCheckService {

    /**
     * 判断是否是店仓仓库
     */
    boolean isShopWarehouse(Integer warehouseId);

    /**
     * 查询销售库存
     */
    List<WarehouseStoreDTO> getSaleInventoryList(ProductStoreQueryDTO productStoreQueryDTO);

    /**
     * 查询仓库库存
     */
    List<WarehouseStoreDTO> getInventoryList(ProductStoreQueryDTO productStoreQueryDTO);

    /**
     * 查询仓库库存
     * 
     * @param productStoreQueryDTO
     * @return
     */
    PageList<WarehouseStoreDTO> getWarehouseInventoryList(WarehouseProductStoreQueryDTO productStoreQueryDTO);

    /**
     * 根据城市id，查询仓库库存
     */
    List<WarehouseStoreDTO> getInventoryListByCityId(Integer cityId);

    /**
     * 校正销售库存（指定产品）
     */
    void checkSellInventory(List<Long> productSkuIds, Integer warehouseId, Integer cityId, Integer opUserId);

    /**
     * 获取处理品及陈列品库存（指定产品）
     */
    BigDecimal getDisposeProductCountBySkuId(Integer cityId, Integer warehouseId, Long productSkuId);

    /**
     * 批量获取处理品及陈列品库存（指定产品）
     */
    Map<Long, BigDecimal> getBatchDisposeProductCountBySkuId(Integer cityId, Integer warehouseId,
        List<Long> productSkuIdList, Map<Long, List<ProductSkuDTO>> relationMap);

    /**
     * 获取处理品及陈列品库存（指定产品）详情信息
     */
    List<DisposedProductInventorDTO> findDisposedProductInventorBySkuId(Integer cityId, Integer warehouseId,
        List<Long> productSkuIdList);

    /**
     * 根据城市id，库存对账
     */
    void checkStoreInventoryByCityId(Integer cityId, Integer opUserId, boolean isSnap);

    /**
     * 根据城市id，库存对账 checkStoreInventoryByCityId
     */
    void checkStoreInventoryByCityInfo(CheckStoreInventoryByCityInfoDTO dto);

    /**
     * 有货主的产品，增加库存快照
     */
    void checkStoreInventoryByOwner(Integer opUserId);

    /**
     * 查看库存矫正记录
     */
    PageList<InventorySyncRecordDTO> listInventorySyncRecord(InventorySyncRecordSO inventorySyncRecordSO);

    /**
     * 库存矫正记录标记为已处理
     */
    void markInventorySyncRecord(InventorySyncRecordDTO dto);

    /**
     * 库存对账记录批量标记为已处理
     */
    void batchMarkInventorySyncRecord(InventorySyncRecordSO inventorySyncRecordSO);

    /**
     * 根据城市id，查询ERP库存
     */
    Map<String, ERPStoreVO> getERPStoreVOMap(Integer cityId);

    /**
     * 根据仓库数据计算销售库存
     */
    List<WarehouseStoreDTO> calculationSaleInventory(WarehouseProductStoreQueryDTO queryDTO);
}
