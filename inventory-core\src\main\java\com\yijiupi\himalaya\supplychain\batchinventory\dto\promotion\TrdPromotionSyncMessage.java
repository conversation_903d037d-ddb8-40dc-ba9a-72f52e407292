package com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved. </br>
 * 交易大日期限时惠发布
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
public class TrdPromotionSyncMessage implements Serializable {
    /**
     * id
     */
    private Integer id;
    /**
     * name
     */
    private String name;
    /**
     * 促销活动
     */
    private List<TrdPromotionSyncItemMessage> timelimitPromotionItems;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<TrdPromotionSyncItemMessage> getTimelimitPromotionItems() {
        return timelimitPromotionItems;
    }

    public void setTimelimitPromotionItems(List<TrdPromotionSyncItemMessage> timelimitPromotionItems) {
        this.timelimitPromotionItems = timelimitPromotionItems;
    }
}
