package com.yijiupi.himalaya.supplychain.dto.ordercenter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 待发货模型
 *
 */
public class SaleInventoryWaitDeliveryDTO implements Serializable {

    private static final long serialVersionUID = 6149301055065171229L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 库存Id
     */
    /**
     * 产品规格id
     */
    private Long inventoryId;

    /**
     * 城市ID - 分片字段
     */
    private Long cityId;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 商品规格ID
     */
    private Long productSpecId;

    /**
     * 一级货主ID
     */
    private Long ownerId;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    /**
     * 变更来源ID（可以是oms系统id，或其他异构系统内部id）
     */
    private Long sourceId;

    /**
     * 变更来源编号（可以是oms系统编号，或其他异构系统内部编号）
     */
    private String sourceNo;

    /**
     * 变更来源项ID（可以是oms系统itemId，或其他异构系统内部itemId）
     */
    private Long sourceItemId;

    /**
     * 单据来源类型
     */
    private String orderSource;

    /**
     * 当前预占，待发货数量
     */
    private BigDecimal currentCount;

    /**
     * 备注
     */
    private String remark;

    private String createTime;

    private String lastUpdateTime;

    private Boolean deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceNo() {
        return sourceNo;
    }

    public void setSourceNo(String sourceNo) {
        this.sourceNo = sourceNo;
    }

    public Long getSourceItemId() {
        return sourceItemId;
    }

    public void setSourceItemId(Long sourceItemId) {
        this.sourceItemId = sourceItemId;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public BigDecimal getCurrentCount() {
        return currentCount;
    }

    public void setCurrentCount(BigDecimal currentCount) {
        this.currentCount = currentCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(String lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
