package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

public class StockOrderInventoryDTO implements Serializable {

    /** 订单编号 */
    private String refOrderNo;

    /** 城市ID，分库用（第三方订单使用一个新的自定义CityId） */
    private Integer orgId;

    /** 订单类型 1 入库 2 出库 */
    private Integer orderType;

    /** 发货仓库id */
    private Integer warehouseId;

    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderCreateTime;

    private List<StockOrderItemInventoryDTO> stockOrderItemDTOS;

    /**
     * 备注
     */
    private String remark;

    /**
     * 任务Id
     */
    private Long fetchTaskId;

    /**
     * 任务类型
     */
    private Byte fetchTaskType;

    /**
     * 是否调用OMS接口通知TMS,默认需要
     */
    private Boolean needToChangeTms = true;

    /**
     * 用户id
     */
    private Integer operateUserId;

    /**
     * 用户名称
     */
    private String operateUserName;

    /**
     * 仓库订单类型
     */
    private Integer storeOrderType;

    public Boolean getNeedToChangeTms() {
        return needToChangeTms;
    }

    public void setNeedToChangeTms(Boolean needToChangeTms) {
        this.needToChangeTms = needToChangeTms;
    }

    public Long getFetchTaskId() {
        return fetchTaskId;
    }

    public void setFetchTaskId(Long fetchTaskId) {
        this.fetchTaskId = fetchTaskId;
    }

    public Byte getFetchTaskType() {
        return fetchTaskType;
    }

    public void setFetchTaskType(Byte fetchTaskType) {
        this.fetchTaskType = fetchTaskType;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getRefOrderNo() {
        return refOrderNo;
    }

    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Date getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    public List<StockOrderItemInventoryDTO> getStockOrderItemDTOS() {
        return stockOrderItemDTOS;
    }

    public void setStockOrderItemDTOS(List<StockOrderItemInventoryDTO> stockOrderItemDTOS) {
        this.stockOrderItemDTOS = stockOrderItemDTOS;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
    }

    public String getOperateUserName() {
        return operateUserName;
    }

    public void setOperateUserName(String operateUserName) {
        this.operateUserName = operateUserName;
    }

    public Integer getStoreOrderType() {
        return storeOrderType;
    }

    public void setStoreOrderType(Integer storeOrderType) {
        this.storeOrderType = storeOrderType;
    }
}
