package com.yijiupi.himalaya.supplychain.search;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

public class StockReportSO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 商品名称
     */
    private String productSkuName;
    /**
     * 仓库
     */
    private List<Integer> warehouseIds;
    /**
     * 货位
     */
    private Integer goodsPositionId;
    /**
     * 供应商(合作商)
     */
    private Long supplierId;
    /**
     * 经销商(入住商)
     */
    private Long agencyId;
    /**
     * 库存分类
     */
    private Integer storeOwnerType;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 产品skuid集合
     */
    private List<Long> productSkuIds;

    /**
     * 是否查询库存时抹去货主 0 或者 null:不抹去货主，其它则抹去货主
     *
     */
    private Integer eraseOwnerId;

    /**
     * 是否限制产品范围（1：表示只查本仓库的产品）
     */
    private Byte limitSku;

    /**
     * 是否有库存： null: 查所有 1：库存大于零 0：库存等于小于零 -1：库存小于零
     */
    private Byte hasRealStoreType;

    /**
     * 最小库龄
     */
    private Long startStockAge;

    /**
     * 最大库龄
     */
    private Long endStockAge;

    /**
     * ProductTypeEnums 产品类型：1.成品;2.半成品;3.包装材料
     */
    private List<Byte> productTypeList;

    /**
     * 库存动销属性
     */
    private String inventoryPinProperty;
    /**
     *
     */
    private Integer cityId;

    /**
     * 查询是否删除的sku：0 否，1 是
     */
    private List<Byte> deleted = Collections.singletonList((byte)0);

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;

    public String getInventoryPinProperty() {
        return inventoryPinProperty;
    }

    public void setInventoryPinProperty(String inventoryPinProperty) {
        this.inventoryPinProperty = inventoryPinProperty;
    }

    public Byte getHasRealStoreType() {
        return hasRealStoreType;
    }

    public void setHasRealStoreType(Byte hasRealStoreType) {
        this.hasRealStoreType = hasRealStoreType;
    }

    public Byte getLimitSku() {
        return limitSku;
    }

    public void setLimitSku(Byte limitSku) {
        this.limitSku = limitSku;
    }

    public Integer getEraseOwnerId() {
        return eraseOwnerId;
    }

    public void setEraseOwnerId(Integer eraseOwnerId) {
        this.eraseOwnerId = eraseOwnerId;
    }

    public List<Long> getProductSkuIds() {
        return productSkuIds;
    }

    public void setProductSkuIds(List<Long> productSkuIds) {
        this.productSkuIds = productSkuIds;
    }

    public String getProductSkuName() {
        return productSkuName;
    }

    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    public List<Integer> getWarehouseIds() {
        return warehouseIds;
    }

    public void setWarehouseIds(List<Integer> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    public Integer getGoodsPositionId() {
        return goodsPositionId;
    }

    public void setGoodsPositionId(Integer goodsPositionId) {
        this.goodsPositionId = goodsPositionId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getStoreOwnerType() {
        return storeOwnerType;
    }

    public void setStoreOwnerType(Integer storeOwnerType) {
        this.storeOwnerType = storeOwnerType;
    }

    public Long getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(Long agencyId) {
        this.agencyId = agencyId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getStartStockAge() {
        return startStockAge;
    }

    public void setStartStockAge(Long startStockAge) {
        this.startStockAge = startStockAge;
    }

    public Long getEndStockAge() {
        return endStockAge;
    }

    public void setEndStockAge(Long endStockAge) {
        this.endStockAge = endStockAge;
    }

    /**
     * 获取 ProductTypeEnums 产品类型：1.成品;2.半成品;3.包装材料
     *
     * @return productTypeList ProductTypeEnums 产品类型：1.成品;2.半成品;3.包装材料
     */
    public List<Byte> getProductTypeList() {
        return this.productTypeList;
    }

    /**
     * 设置 ProductTypeEnums 产品类型：1.成品;2.半成品;3.包装材料
     *
     * @param productTypeList ProductTypeEnums 产品类型：1.成品;2.半成品;3.包装材料
     */
    public void setProductTypeList(List<Byte> productTypeList) {
        this.productTypeList = productTypeList;
    }

    /**
     * 获取
     *
     * @return cityId
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置
     *
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 查询是否删除的sku：0 否，1 是
     *
     * @return deleted 查询是否删除的sku：0 否，1 是
     */
    public List<Byte> getDeleted() {
        return this.deleted;
    }

    /**
     * 设置 查询是否删除的sku：0 否，1 是
     *
     * @param deleted 查询是否删除的sku：0 否，1 是
     */
    public void setDeleted(List<Byte> deleted) {
        this.deleted = deleted;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }
}
