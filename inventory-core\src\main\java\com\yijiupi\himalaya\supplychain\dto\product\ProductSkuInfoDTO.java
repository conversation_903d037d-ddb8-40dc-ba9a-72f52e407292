package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品查询
 *
 * <AUTHOR>
 * @date 2018/8/23 14:13
 */
public class ProductSkuInfoDTO implements Serializable {
    private static final long serialVersionUID = -7269576918362255005L;

    /**
     * 商品sku
     */
    private String productSkuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 货主供应商
     */
    private String ownerName;

    /**
     * 销售模式类型
     */
    private Byte saleModel;

    /**
     * 销售模式
     */
    private String saleModelName;

    private String packageName;
    private String unitName;
    private String packageQuantity;
    private String ownerId;
    private Long secOwnerId;

    /**
     * 仓库库存小单位总数量
     */
    private BigDecimal unitTotolCount;

    /**
     * 产品的保质期
     */
    private Integer monthOfShelfLife;

    /**
     * 保质期单位(1：年 2：月 3：日）
     */
    private Integer shelfLifeUnit;

    /**
     * 产品状态 下架(0), 作废(1), 上架(2)
     */
    private Integer productState;

    /**
     * 产品来源 : 易酒批 = 0 微酒 = 1 知花知果 = 2 易款连锁 = 3 易经商 = 4
     */
    private Byte source;

    /**
     * 规格ID
     */
    private Long productSpecificationId;

    /**
     * 商品特征:1:大件,2:小件
     */
    private Byte productFeature;

    /**
     * 保质期是否为长期
     */
    private Boolean shelfLifeLongTime;

    public Byte getProductFeature() {
        return productFeature;
    }

    public void setProductFeature(Byte productFeature) {
        this.productFeature = productFeature;
    }

    public Integer getProductState() {
        return productState;
    }

    public void setProductState(Integer productState) {
        this.productState = productState;
    }

    public BigDecimal getUnitTotolCount() {
        return unitTotolCount;
    }

    public void setUnitTotolCount(BigDecimal unitTotolCount) {
        this.unitTotolCount = unitTotolCount;
    }

    public Integer getMonthOfShelfLife() {
        return monthOfShelfLife;
    }

    public void setMonthOfShelfLife(Integer monthOfShelfLife) {
        this.monthOfShelfLife = monthOfShelfLife;
    }

    public Integer getShelfLifeUnit() {
        return shelfLifeUnit;
    }

    public void setShelfLifeUnit(Integer shelfLifeUnit) {
        this.shelfLifeUnit = shelfLifeUnit;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(String packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(String productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Byte getSaleModel() {
        return saleModel;
    }

    public void setSaleModel(Byte saleModel) {
        this.saleModel = saleModel;
    }

    public String getSaleModelName() {
        return saleModelName;
    }

    public void setSaleModelName(String saleModelName) {
        this.saleModelName = saleModelName;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Boolean getShelfLifeLongTime() {
        return shelfLifeLongTime;
    }

    public void setShelfLifeLongTime(Boolean shelfLifeLongTime) {
        this.shelfLifeLongTime = shelfLifeLongTime;
    }
}
