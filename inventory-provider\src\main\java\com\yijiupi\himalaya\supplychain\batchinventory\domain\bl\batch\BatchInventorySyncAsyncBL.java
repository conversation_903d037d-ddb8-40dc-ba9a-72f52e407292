package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpChangeRecordDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
@Service
public class BatchInventorySyncAsyncBL {

    @Autowired
    private BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;
    @Autowired
    private ProductStoreBatchChangeRecordBL productStoreBatchChangeRecordBL;

    @Transactional(rollbackFor = Exception.class)
    public boolean syncBatchInventoryByProductLocation(List<ProductStoreBatchPO> list, List<String> defectiveStoreBatchIdList, PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        List<String> storeIds = list.stream().map(p -> p.getProductStoreId()).collect(Collectors.toList());
        // 删除原货位库存
        // productStoreBatchMapper.deleteBatchInventoryByStoreIds(storeIds);
        // 将原货位库存数量更改为0
        batchInventoryProductStoreBatchMapper.updateBatchInventoryZeroByStoreIds(storeIds, defectiveStoreBatchIdList);
        // 新增货位库存
        batchInventoryProductStoreBatchMapper.insertBatchInventoryPOList(list);
        // 新增批次库存变更记录
        productStoreBatchChangeRecordBL.createStoreBatchChangeRecordByPickUp(pickUpChangeRecordDTO, list);

        return Boolean.TRUE;
    }


}
