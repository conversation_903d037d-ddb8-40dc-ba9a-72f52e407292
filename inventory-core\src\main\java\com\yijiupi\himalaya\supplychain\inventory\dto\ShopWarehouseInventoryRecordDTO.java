package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 经销商调拨发送销售库存变消息DTO
 *
 * <AUTHOR>
 */
public class ShopWarehouseInventoryRecordDTO implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    /**
     * 产品skuId
     */
    private Long productSkuId;
    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 变更小单位
     */
    private BigDecimal changCount;
    /**
     * 是否更新交易平台库存.
     */
    private Integer hasUpdateOPInventory = 1;

    /**
     * 订单渠道
     */
    private Integer channel;

    /**
     * 货主Id
     */
    private Long ownerId;

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 仓库Id
     *
     * @return warehouseId 仓库Id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库Id
     *
     * @param warehouseId 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getChangCount() {
        return changCount;
    }

    public void setChangCount(BigDecimal changCount) {
        this.changCount = changCount;
    }

    /**
     * 获取 产品skuId
     *
     * @return productSkuId 产品skuId
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品skuId
     *
     * @param productSkuId 产品skuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 是否更新交易平台库存.
     *
     * @return hasUpdateOPInventory 是否更新交易平台库存.
     */
    public Integer getHasUpdateOPInventory() {
        return this.hasUpdateOPInventory;
    }

    /**
     * 设置 是否更新交易平台库存.
     *
     * @param hasUpdateOPInventory 是否更新交易平台库存.
     */
    public void setHasUpdateOPInventory(Integer hasUpdateOPInventory) {
        this.hasUpdateOPInventory = hasUpdateOPInventory;
    }
}
