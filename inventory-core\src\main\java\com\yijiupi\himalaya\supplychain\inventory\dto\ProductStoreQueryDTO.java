package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 库存查询条件
 *
 * <AUTHOR>
 * @date 2019/4/3 11:48
 */
public class ProductStoreQueryDTO implements Serializable {

    private static final long serialVersionUID = -6932270394386141360L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 产品skuId集合
     */
    private List<Long> productSkuIds;

    /**
     * 规格id和ownerId集合
     */
    private List<ProductSpecAndOwnerIdDTO> specAndOwnerIds;

    /**
     * ownerId
     */
    private Long ownerId;

    /**
     * 是否打印日志， true:打印 false：不打印
     */
    private Boolean printLog = false;

    /**
     * 产品规格id集合（同规格不同货主的都会被查询出来）
     */
    private List<Long> productSpecIds;

    /**
     * 同规格库存是否合并 true：合并 false：不合并
     */
    private Boolean mergeFlag = false;

    /**
     * 中台skuId集合
     */
    private List<Long> unifySkuIds;

    /**
     * 规格id和ownerId和secOwnerId集合
     */
    private List<ProductSpecAndOwnerIdDTO> specAndOwnerIdAndSecOwnerIds;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;

    public List<Long> getUnifySkuIds() {
        return unifySkuIds;
    }

    public void setUnifySkuIds(List<Long> unifySkuIds) {
        this.unifySkuIds = unifySkuIds;
    }

    public Boolean getMergeFlag() {
        return mergeFlag;
    }

    public void setMergeFlag(Boolean mergeFlag) {
        this.mergeFlag = mergeFlag;
    }

    public List<Long> getProductSpecIds() {
        return productSpecIds;
    }

    public void setProductSpecIds(List<Long> productSpecIds) {
        this.productSpecIds = productSpecIds;
    }

    public Boolean getPrintLog() {
        return printLog;
    }

    public void setPrintLog(Boolean printLog) {
        this.printLog = printLog;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<Long> getProductSkuIds() {
        return productSkuIds;
    }

    public void setProductSkuIds(List<Long> productSkuIds) {
        this.productSkuIds = productSkuIds;
    }

    public List<ProductSpecAndOwnerIdDTO> getSpecAndOwnerIds() {
        return specAndOwnerIds;
    }

    public void setSpecAndOwnerIds(List<ProductSpecAndOwnerIdDTO> specAndOwnerIds) {
        this.specAndOwnerIds = specAndOwnerIds;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public List<ProductSpecAndOwnerIdDTO> getSpecAndOwnerIdAndSecOwnerIds() {
        return specAndOwnerIdAndSecOwnerIds;
    }

    public void setSpecAndOwnerIdAndSecOwnerIds(List<ProductSpecAndOwnerIdDTO> specAndOwnerIdAndSecOwnerIds) {
        this.specAndOwnerIdAndSecOwnerIds = specAndOwnerIdAndSecOwnerIds;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }
}
