package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;

public class ERPDisposedInventoryDTO implements Serializable {

    /**
     * skuId
     */
    private String productSkuId;

    /**
     * 库存数量
     */
    private BigDecimal stockCount;

    /**
     * 库存类型：处理品 = 0, 陈列出库 = 1
     */
    private Integer disposedType;

    public String getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(String productSkuId) {
        this.productSkuId = productSkuId;
    }

    public BigDecimal getStockCount() {
        return stockCount;
    }

    public void setStockCount(BigDecimal stockCount) {
        this.stockCount = stockCount;
    }

    public Integer getDisposedType() {
        return disposedType;
    }

    public void setDisposedType(Integer disposedType) {
        this.disposedType = disposedType;
    }
}
