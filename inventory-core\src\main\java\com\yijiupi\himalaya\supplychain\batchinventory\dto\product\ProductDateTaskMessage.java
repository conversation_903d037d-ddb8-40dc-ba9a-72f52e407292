package com.yijiupi.himalaya.supplychain.batchinventory.dto.product;

import java.io.Serializable;

/**
 * 生产日期治理任务消息
 */
public class ProductDateTaskMessage implements Serializable {
    /**
     * 仓库 id
     */
    private Integer warehouseId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 批次日期 yyyy-MM-dd HH:mm:ss
     */
    private String productionDate;

    /**
     * 任务属性, 0=未指定、1=酒饮、2=休食
     */
    private Integer taskProperty;

    /**
     * 操作人 id
     */
    private Integer userId;

    /**
     * 批次库存id
     */
    private String storeBatchId;

    public String getStoreBatchId() {
        return storeBatchId;
    }

    public void setStoreBatchId(String storeBatchId) {
        this.storeBatchId = storeBatchId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(String productionDate) {
        this.productionDate = productionDate;
    }

    public Integer getTaskProperty() {
        return taskProperty;
    }

    public void setTaskProperty(Integer taskProperty) {
        this.taskProperty = taskProperty;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }
}
