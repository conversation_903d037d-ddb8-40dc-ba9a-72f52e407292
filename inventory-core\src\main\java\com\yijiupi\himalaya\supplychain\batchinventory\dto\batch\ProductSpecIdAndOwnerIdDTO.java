package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;

/**
 * 产品规格id和货主id
 *
 * <AUTHOR>
 * @date 2020/1/19 11:16
 */
public class ProductSpecIdAndOwnerIdDTO implements Serializable {

    private static final long serialVersionUID = -370847400593312179L;

    /**
     * 产品规格id
     */
    private Long productSpecId;

    /**
     * 货主id
     */
    private Long ownerId;

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }
}
