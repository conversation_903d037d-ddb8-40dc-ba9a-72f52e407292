package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 设置仓库库存数量DTO
 *
 * <AUTHOR>
 */
public class WarehouseInventoryModDTO implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    /**
     * 产品skuId
     */
    private Long productSkuId;
    /**
     * 仓库Id
     */
    private Integer warehouseId;

    /**
     * 大单位数量.
     */
    private BigDecimal packageCount;
    /**
     * 小单位数量.
     */
    private BigDecimal unitCount;
    /**
     * 是否更新交易平台库存.
     */
    private Integer hasUpdateOPInventory = 1;

    /**
     * 订单渠道
     */
    private Integer channel;

    /**
     * 库存Id
     */
    private String productStoreId;

    private Long secOwnerId;

    /**
     * 规格Id
     */
    private Long productSpecificationId;

    /**
     * 货主Id
     */
    private Long ownerId;

    /**
     * 城市id
     */
    private Integer orgId;

    private BigDecimal packageQuantity;

    // 新加字段
    private Integer orderType;

    private String orderId;

    private String orderNo;

    private Integer jiupiEventType;

    private Integer erpEventType;

    private String description;

    private String opUser;

    public String getOpUser() {
        return opUser;
    }

    public void setOpUser(String opUser) {
        this.opUser = opUser;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    public Integer getErpEventType() {
        return erpEventType;
    }

    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 仓库Id
     *
     * @return warehouseId 仓库Id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库Id
     *
     * @param warehouseId 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 大单位数量.
     *
     * @return packageCount 大单位数量.
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 大单位数量.
     *
     * @param packageCount 大单位数量.
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 小单位数量.
     *
     * @return unitCount 小单位数量.
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 小单位数量.
     *
     * @param unitCount 小单位数量.
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    /**
     * 获取 产品skuId
     *
     * @return productSkuId 产品skuId
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品skuId
     *
     * @param productSkuId 产品skuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 是否更新交易平台库存.
     *
     * @return hasUpdateOPInventory 是否更新交易平台库存.
     */
    public Integer getHasUpdateOPInventory() {
        return this.hasUpdateOPInventory;
    }

    /**
     * 设置 是否更新交易平台库存.
     *
     * @param hasUpdateOPInventory 是否更新交易平台库存.
     */
    public void setHasUpdateOPInventory(Integer hasUpdateOPInventory) {
        this.hasUpdateOPInventory = hasUpdateOPInventory;
    }
}
