package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.List;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.supplychain.dto.product.StoreDTOBySupplierOp;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.search.StockReportSO;
import com.yijiupi.himalaya.supplychain.search.WarehouseStoreBySupplierOpSO;

/**
 * 库存列表查询接口.<br>
 *
 * <AUTHOR>
 */
public interface IInventoryListQueryService {

    /**
     * 查询仓库库存列表(轻加盟调用)
     */
    PageList<WarehouseInventoryDTO> listWarehouseInventory(WarehouseInventoryQueryDTO warehouseInventoryQuery,
        PagerCondition pager);

    /**
     * 查询城市SKU库存
     * 
     * @param cityId
     * @param channel 渠道
     * @param source 产品来源
     * @param secOwnerId 二级货主
     * @return
     */
    List<ProductSkuInventoryCountDTO> listProductSkuInventoyByCity(Integer cityId, Integer channel, Integer source,
        Long secOwnerId);

    /**
     * 用户范围库存报表(正常库存调用,也可以只查询经销商库存)
     */
    PageList<InventoryReportDTO> findStoreReportPageByAuth(StockReportSO so, PagerCondition pager);

    /**
     * 用户范围库存报表(正常库存调用,也可以只查询经销商库存)
     */
    PageList<InventoryReportDTO> findStoreReportPageByProductSpecification(StockReportSO so, PagerCondition pager);

    /**
     * 调拨系统产品查询库存用的
     */
    PageList<StoreDTOBySupplierOp> findProductWarehouseStoreListForAllocation(
        WarehouseStoreBySupplierOpSO warehouseStoreBySupplierOpSO, PagerCondition pagerCondition);
    //
    // /**
    // * 调拨系统库存信息汇总
    // */
    // StoreDTOBySupplierOp getProductStoreForSupplierOp(WarehouseStoreBySupplierOpSO warehouseStoreBySupplierOpSO);

    /**
     * 查询sku属性
     * 
     * @param productSkuIdList
     * @param warehouseId
     * @return
     */
    String queryAbcAttribute(List<Long> productSkuIdList, Integer warehouseId);

    /**
     * 用户范围库存报表查询(正常库存调用,也可以只查询经销商库存，包含平均库龄)
     */
    PageList<InventoryReportDTO> findStoreReportPageInfoByAuth(StockReportSO so, PagerCondition pager);

    /**
     * 库存报表查询（商户平台）
     */
    PageList<FindStoreInfoDTO> findStorePage(FindStoreDTO findStoreQuery);

}
