package com.yijiupi.himalaya.supplychain.batchinventory.service;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeInfoDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeValueSaveDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeValueSaveReturnDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchNOQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchNOQueryResultDTO;

import java.util.List;

/**
 * <AUTHOR> 2018/4/10
 */
public interface IBatchAttributeInfoService {

    /**
     * 批量新增批属性info
     *
     * @param batchAttributeInfoDTOS
     */
    // void addBatchAttributeInfo(List<BatchAttributeInfoDTO> batchAttributeInfoDTOS);

    /**
     * 根据批属性编号查找详情
     *
     * @param batchAttributeInfoNo
     * @return
     */
    List<BatchAttributeInfoDTO> findAttributeInfoByNo(String batchAttributeInfoNo);

    /**
     * 同步批次号
     *
     * @param ownerType
     */
    void processBatchNo(Integer ownerType);

    /**
     * 根据仓库产品信息获取入库批次编号
     */
    List<BatchNOQueryResultDTO> genOrderBatchInfoNO(BatchNOQueryDTO queryDTO);

    /**
     * 保存批属性并且生成批次编号
     */
    List<BatchAttributeValueSaveReturnDTO> saveBatchAttributeDicInfo(BatchAttributeValueSaveDTO saveDTO);
}
