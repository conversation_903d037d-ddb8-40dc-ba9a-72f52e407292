package com.yijiupi.himalaya.supplychain.dto.easysell;

import java.io.Serializable;
import java.util.List;

/**
 * 根据条码，经销商id,仓库查询商品信息参数DTO
 * 
 * @author: lidengfeng
 * @date 2018/8/30 17:41
 */
public class ProductDetailsQueryDTO implements Serializable {

    /**
     * 条形码
     */
    private String barCode;

    /**
     * 经销商id
     */
    private Long shopId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 商品skuId
     */
    private List<String> productSpecificationIdList;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页数
     */
    private Integer pageSize;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 规格id集合
     * 
     * @return
     */
    public List<String> getProductSpecificationIdList() {
        return productSpecificationIdList;
    }

    /**
     * 设置 规格id集合
     * 
     * @param productSpecificationIdList
     */
    public void setProductSpecificationIdList(List<String> productSpecificationIdList) {
        this.productSpecificationIdList = productSpecificationIdList;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
