package com.yijiupi.himalaya.supplychain.dto.erp;

import java.util.EnumSet;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023-10-18 10:39
 **/
public enum ErpInOutStockTypeEnum {
    /**
     * 出库
     */
    OUT_STOCK(1, "内配批次出库"),
    /**
     * 入库
     */
    IN_STOCK(2, "内配批次入库"),
    ;
    private static final Set<ErpInOutStockTypeEnum> entries = EnumSet.allOf(ErpInOutStockTypeEnum.class);
    private final Byte value;
    private final String name;

    ErpInOutStockTypeEnum(Integer value, String name) {
        this.value = value.byteValue();
        this.name = name;
    }

    public static Optional<ErpInOutStockTypeEnum> valueOf(Number number) {
        return entries.stream().filter(it -> it.valueEquals(number)).findFirst();
    }

    public Byte getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public boolean valueEquals(Number number) {
        return number != null && value.equals(number.byteValue());
    }

}
