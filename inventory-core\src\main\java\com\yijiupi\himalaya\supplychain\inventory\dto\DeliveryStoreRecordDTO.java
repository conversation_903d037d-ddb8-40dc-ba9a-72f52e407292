package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 已发货数量
 *
 * <AUTHOR>
 * @date 2018/8/29 10:51
 */
public class DeliveryStoreRecordDTO implements Serializable {
    private static final long serialVersionUID = -2656210333522078008L;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 已发货总数量
     */
    private BigDecimal deliveryedCount;

    private Long productSpecificationId;

    private Long ownerId;

    private Long secOwnerId;

    private String id;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getDeliveryedCount() {
        return deliveryedCount;
    }

    public void setDeliveryedCount(BigDecimal deliveryedCount) {
        this.deliveryedCount = deliveryedCount;
    }
}
