package com.yijiupi.himalaya.supplychain.batchinventory.service;

import java.util.List;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;

/**
 * 货位库存同步
 *
 * <AUTHOR>
 * @date 2019/2/23 18:19
 */
public interface IBatchInventorySyncService {

    /**
     * 根据产品货位配置同步货位库存
     */
    void syncBatchInventoryByProductLocaion(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);

    /**
     * 清除负货位库存
     */
    BatchInventoryClearNegativeLogicDTO
        clearBatchInventoryByProductLocaion(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);

    /**
     * 清除总库存为0的货位库存不为0的项
     */
    void clearZeroBatchInventoryByWarehouseId(Integer warehouseId);

    /**
     * 校正总库存与货位库存
     */
    void adjustBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);

    /**
     * 转移指定货位或者货区的库存
     */
    void transferBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);

    /**
     * 将所选库区中所有产品移动到产品关联货位
     */
    void batchInventoryTransferByLocationArea(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);

    /**
     * 残次品货位库存校正
     */
    void adjustDisposedProductLocationInventory(BatchInventoryAdjustDTO batchInventoryAdjustDTO);

    /**
     * 残次品校正以erp为准
     */
    void adjustDisposedProductLocationInventoryByERP(BatchInventoryAdjustDTO batchInventoryAdjustDTO);

    /**
     * 根据ERP校正货位库存的生产日期
     */
    void syncBatchInventoryProductionDateByERP(BatchInventoryProductionDateSyncQueryDTO queryDTO);

    /**
     * 校正批次编号
     */
    void syncBatchInventoryBatchNo(BatchInventoryProductionDateSyncQueryDTO queryDTO);

    /**
     * 根据仓库id新增有仓库库存无货位库存产品的批次库存
     */
    void addBatchInventoryByWarehouseId(Integer warehouseId, String locationName);

    /**
     * 根据拣货统计库存计算周转区库存
     */
    void adjustCHKBatchInventoryByPicking(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);

    /**
     * 批量清除负货位库存
     */
    List<BatchInventoryClearNegativeLogicDTO> batchClearBatchInventoryByProductLocaion(List<Integer> warehouseIds);

    /**
     * 转移补货上限库存到指定货位
     */
    void batchInventoryTransferNoOrder(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);

    /**
     * 指定货位/货区间库存转移,目前仅支持2.5+
     */
    void batchInventoryMove(BatchInventoryMoveDTO moveDTO);

    /**
     * 批量清除总库存为0的货位库存不为0的项
     */
    void batchClearZeroBatchInventory(List<Integer> warehouseIds);

    /**
     * 批量校正总库存与货位库存
     */
    void batchAdjustBatchInventory(List<Integer> warehouseIds);

    /**
     * 清除负货位库存
     */
    BatchInventoryClearNegativeLogicDTO clearBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);
}
