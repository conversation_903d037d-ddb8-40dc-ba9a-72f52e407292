package com.yijiupi.himalaya.supplychain.inventory.dto.agency;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 微酒入库操作对象
 */
public class WJStockProductDTO implements Serializable {

    private Long productSkuId;
    /**
     * 产品信息规格id
     */
    private Long productSpecificationId;
    /**
     * 产品来源
     */
    private Integer source;
    /**
     * 产品渠道
     */
    private Integer channel;
    /**
     * 库存变化数量
     */
    private BigDecimal changeCount;

    /**
     * 经销商id
     */
    private Long ownerId;
    /**
     * 二级货主
     */
    private Long secOwnerId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 批属性JSON
     */
    private String attributeList;
    /**
     * 批次时间
     */
    private Date batchTime;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 货位或者货区Id
     */
    private Long locationId;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public void setAttributeList(String attributeList) {
        this.attributeList = attributeList;
    }

    /**
     * 获取 产品信息规格id
     * 
     * @return
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 产品信息规格id
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 产品来源
     *
     * @return source 产品来源
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源
     *
     * @param source 产品来源
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 产品渠道
     *
     * @return channel 产品渠道
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 产品渠道
     *
     * @param channel 产品渠道
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 库存变化数量
     *
     * @return changeCount 库存变化数量
     */
    public BigDecimal getChangeCount() {
        return this.changeCount;
    }

    /**
     * 设置 库存变化数量
     *
     * @param changeCount 库存变化数量
     */
    public void setChangeCount(BigDecimal changeCount) {
        this.changeCount = changeCount;
    }

    /**
     * 获取 二级货主
     *
     * @return secOwnerId 二级货主
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主
     *
     * @param secOwnerId 二级货主
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取 订单编号
     *
     * @return orderNo 订单编号
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 订单编号
     *
     * @param orderNo 订单编号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 订单id
     *
     * @return orderId 订单id
     */
    public String getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 订单id
     *
     * @param orderId 订单id
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getAttributeList() {
        return attributeList;
    }

    /**
     * 获取 批次时间
     */
    public Date getBatchTime() {
        return this.batchTime;
    }

    /**
     * 设置 批次时间
     */
    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    /**
     * 获取 生产日期
     */
    public Date getProductionDate() {
        return this.productionDate;
    }

    /**
     * 设置 生产日期
     */
    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    /**
     * 获取 过期时间
     */
    public Date getExpireTime() {
        return this.expireTime;
    }

    /**
     * 设置 过期时间
     */
    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }
}
