package com.yijiupi.himalaya.supplychain.inventory.service.biz;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.inventory.dto.LightAllianceProductInventoryDTO;

/**
 * 轻加盟库存服务.
 */
public interface ILightAllianceInventoryService {

    /**
     * 获取单个商品轻加盟仓库库存.
     */
    LightAllianceProductInventoryDTO getProductInventoryMap(Long productSkuId, Integer warehouseId);

    /**
     * 获取多个商品轻加盟仓库库存.
     */
    Map<Long, LightAllianceProductInventoryDTO> getProductInventoryMap(List<Long> productSkuId, Integer warehouseId);

    /**
     * 调整产品库存.
     */
    void adjustProductInventory(Long productSkuId, Integer warehouseId, BigDecimal count);

    /**
     * 复制仓库库存到轻加盟仓库
     */
    void copyInventoryForLightAlliance(Integer cityId, Integer cityWarehouseId, Integer lightWarehouseId,
        Integer opUserId, Integer channel);
}
