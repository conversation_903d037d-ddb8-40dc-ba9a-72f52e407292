package com.yijiupi.himalaya.supplychain.inventory.dto.instock;

import java.io.Serializable;
import java.util.List;

/**
 * 批次出库参数
 * 
 * <AUTHOR>
 */
public class BatchInBoundParamDTO implements Serializable {

    private static final long serialVersionUID = 1809099655253918328L;
    /**
     * 批次编号
     */
    private String batchNo;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 操作人
     */
    private Integer opUserId;
    /**
     * 溯源码列表
     */
    private List<TaskDispatchTraceCodeProductDTO> traceCodeList;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getOpUserId() {
        return opUserId;
    }

    public void setOpUserId(Integer opUserId) {
        this.opUserId = opUserId;
    }

    public List<TaskDispatchTraceCodeProductDTO> getTraceCodeList() {
        return traceCodeList;
    }

    public void setTraceCodeList(List<TaskDispatchTraceCodeProductDTO> traceCodeList) {
        this.traceCodeList = traceCodeList;
    }
}
