package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class WarehouseInventoryChangeDTO implements Serializable {

    private Long productSkuId;

    private Integer warehouseId;

    private BigDecimal count;
    /**
     * 操作平台来源 0:供应链，1:ERP,2:易经销'
     */
    private Integer systemSource = 0;
    /**
     * 是否更新交易平台库存.
     */
    private Boolean hasUpdateOPInventory = true;

    /**
     * 是否校验自身库存
     */
    private Boolean isValidateSelf = false;

    /**
     * 批属性JSON
     */
    private String attributeList;
    /**
     * 配送方式（0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    private Integer deliveryMode;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * '货位名称'
     */
    private String locationName;

    /**
     * 关联仓库Id
     */
    private Integer relateWarehouseId;

    /**
     * 批次时间
     */
    private Date batchTime;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 批次库存表ID
     */
    private String productStoreBatchId;

    private Integer cityId;

    private Integer orderType;

    private String orderId;

    private String orderNo;

    private Integer jiupiEventType;

    private Integer erpEventType;

    private String description;

    private String createUserId;
    private String createUserName;

    private Integer ownType;
    private Long ownId;

    private Long productSpecificationId;

    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 来源渠道
     */
    private Integer channel = 0;

    /**
     * 产品来源（0：酒批，1：微酒）
     */
    private Integer source = 0;

    /**
     * 是否分配计算
     */
    private Boolean allocationCalculation;

    /**
     * oms订单项id
     */
    private Long omsOrderItemId;

    /**
     * 订单项ID（处理组合订单库存变更明细替换为子单）
     */
    private Long orderItemId;

    /**
     * 明细id
     */
    private Long orderItemDetailId;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public Integer getSystemSource() {
        return systemSource;
    }

    public void setSystemSource(Integer systemSource) {
        this.systemSource = systemSource;
    }

    public Boolean getHasUpdateOPInventory() {
        return hasUpdateOPInventory;
    }

    public void setHasUpdateOPInventory(Boolean hasUpdateOPInventory) {
        this.hasUpdateOPInventory = hasUpdateOPInventory;
    }

    public Boolean getValidateSelf() {
        return isValidateSelf;
    }

    public void setValidateSelf(Boolean validateSelf) {
        isValidateSelf = validateSelf;
    }

    public String getAttributeList() {
        return attributeList;
    }

    public void setAttributeList(String attributeList) {
        this.attributeList = attributeList;
    }

    public Integer getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(Integer deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public String getProductStoreBatchId() {
        return productStoreBatchId;
    }

    public void setProductStoreBatchId(String productStoreBatchId) {
        this.productStoreBatchId = productStoreBatchId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    public Integer getErpEventType() {
        return erpEventType;
    }

    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public Integer getOwnType() {
        return ownType;
    }

    public void setOwnType(Integer ownType) {
        this.ownType = ownType;
    }

    public Long getOwnId() {
        return ownId;
    }

    public void setOwnId(Long ownId) {
        this.ownId = ownId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Boolean getAllocationCalculation() {
        return allocationCalculation;
    }

    public void setAllocationCalculation(Boolean allocationCalculation) {
        this.allocationCalculation = allocationCalculation;
    }

    public Integer getRelateWarehouseId() {
        return relateWarehouseId;
    }

    public void setRelateWarehouseId(Integer relateWarehouseId) {
        this.relateWarehouseId = relateWarehouseId;
    }

    public Long getOmsOrderItemId() {
        return omsOrderItemId;
    }

    public void setOmsOrderItemId(Long omsOrderItemId) {
        this.omsOrderItemId = omsOrderItemId;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Long getOrderItemDetailId() {
        return orderItemDetailId;
    }

    public void setOrderItemDetailId(Long orderItemDetailId) {
        this.orderItemDetailId = orderItemDetailId;
    }
}
