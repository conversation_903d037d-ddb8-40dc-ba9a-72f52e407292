package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

import com.yijiupi.himalaya.base.search.PageList;

/**
 * 仓库信息管理的详情
 * 
 * @author: lideng<PERSON>
 * @date 2018/12/3 17:38
 */
public class WarehouseDealerDetailDTO implements Serializable {

    /**
     * 仓库信息
     */
    private WarehouseChargeConfigDTO warehouseChargeConfigDTO;

    /**
     * 经销商费用配置信息
     */
    private PageList<DealerChargeConfigDTO> dealerChargeList;

    /**
     * 获取 仓库信息
     * 
     * @return
     */
    public WarehouseChargeConfigDTO getWarehouseChargeConfigDTO() {
        return warehouseChargeConfigDTO;
    }

    /**
     * 设置 仓库信息
     * 
     * @param warehouseChargeConfigDTO
     */
    public void setWarehouseChargeConfigDTO(WarehouseChargeConfigDTO warehouseChargeConfigDTO) {
        this.warehouseChargeConfigDTO = warehouseChargeConfigDTO;
    }

    /**
     * 获取经销商费用配置集合
     * 
     * @return
     */
    public PageList<DealerChargeConfigDTO> getDealerChargeList() {
        return dealerChargeList;
    }

    /**
     * 得到经销商费用配置集合
     * 
     * @param dealerChargeList
     */
    public void setDealerChargeList(PageList<DealerChargeConfigDTO> dealerChargeList) {
        this.dealerChargeList = dealerChargeList;
    }
}
