package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品库存
 *
 * <AUTHOR>
 * @date 2018/12/2 15:46
 */
public class ProductStoreDTO implements Serializable {
    private static final long serialVersionUID = 597151892434475873L;

    /**
     * 大件数量
     */
    private BigDecimal packageCount;

    /**
     * 大件单位
     */
    private String packageName;

    /**
     * 小件数量
     */
    private BigDecimal unitCount;

    /**
     * 小件单位
     */
    private String unitName;

    /**
     * 小件总数量
     */
    private BigDecimal unitTotolCount;

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitTotolCount() {
        return unitTotolCount;
    }

    public void setUnitTotolCount(BigDecimal unitTotolCount) {
        this.unitTotolCount = unitTotolCount;
    }
}
