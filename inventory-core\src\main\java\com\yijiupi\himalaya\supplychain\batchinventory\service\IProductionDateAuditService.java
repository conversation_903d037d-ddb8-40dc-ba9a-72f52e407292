package com.yijiupi.himalaya.supplychain.batchinventory.service;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateAuditDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateAuditQuery;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateTodoTaskDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-14 11:43
 **/
public interface IProductionDateAuditService {

    /**
     * 通过仓库、sku、生产日期查询关联的生产日期治理任务
     *
     * @param query 查询条件
     * @return 查询结果
     */
    ProductionDateTodoTaskDTO queryTodoTaskByProductionDate(ProductionDateAuditQuery query);

    /**
     * 申请生产日期审核
     *
     * @param productionDateAuditDTO 申请参数
     */
    void applyProductionDateAudit(ProductionDateAuditDTO productionDateAuditDTO);

    /**
     * 通过仓库 id、skuId、生产日期 查询关联的生产日期审核
     *
     * @param query 查询条件
     * @return 查询结果
     */
    ProductionDateAuditDTO queryAuditInfo(ProductionDateAuditQuery query);

    /**
     * 分页查询生产日期审核<br/>
     * 人工审核页面接口
     *
     * @param query 查询参数
     * @return 查询结果
     */
    PageList<ProductionDateAuditDTO> pageListProductionDateAudit(ProductionDateAuditQuery query);

    /**
     * 按仓库、skuId、生产日期查询生产日期审核数据<br/>
     * 后台接口
     *
     * @param query 查询条件
     * @return 查询结果
     */
    List<ProductionDateAuditDTO> listProductionDateAudit(ProductionDateAuditQuery query);

    /**
     * 审核生产日期审核
     *
     * @param productionDateAuditDTO 审核参数
     */
    void auditProductionDateAudit(ProductionDateAuditDTO productionDateAuditDTO);

}
