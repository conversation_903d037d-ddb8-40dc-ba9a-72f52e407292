package com.yijiupi.himalaya.supplychain.inventory.service;

import com.yijiupi.himalaya.supplychain.inventory.dto.instock.ProcessInStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.ProcessOutStockOrderInventoryDTO;

/**
 * Erp单据库存业务处理服务
 */
public interface IErpInventoryOrderBizService {

    /**
     * 处理入库单库存
     */
    void processInStockOrderInventory(ProcessInStockOrderInventoryDTO processDTO);

    /**
     * 处理出库单库存
     * 
     * @param processDTO
     */
    void processOutStockOrderInventory(ProcessOutStockOrderInventoryDTO processDTO);

}
