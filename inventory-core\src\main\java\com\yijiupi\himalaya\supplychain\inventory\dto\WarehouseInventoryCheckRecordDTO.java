package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * 仓库库存对账记录-变更库存
 *
 * <AUTHOR>
 * @date 2019/3/11 14:39
 */
public class WarehouseInventoryCheckRecordDTO extends WarehouseInventoryModDTO implements Serializable {

    private static final long serialVersionUID = -1331654730798113933L;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 酒批事件类型
     */
    private Integer jiupiEventType;

    /**
     * ERP事件类型
     */
    private Integer erpEventType;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 描述
     */
    private String description;

    @Override
    public Integer getOrderType() {
        return orderType;
    }

    @Override
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    @Override
    public String getOrderNo() {
        return orderNo;
    }

    @Override
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    @Override
    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    @Override
    public Integer getErpEventType() {
        return erpEventType;
    }

    @Override
    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }
}
