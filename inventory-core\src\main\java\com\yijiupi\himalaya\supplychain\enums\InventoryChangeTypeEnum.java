package com.yijiupi.himalaya.supplychain.enums;

/**
 * 库存变更类别
 *
 * <AUTHOR>
 * @date 2018/8/20 14:52
 */
public enum InventoryChangeTypeEnum {
    /**
     * SAAS变更库存
     */
    SAAS变更库存,
    /**
     * 设置仓库库存数量
     */
    设置仓库库存数量,
    /**
     * 设置经销商库存
     */
    设置经销商库存,
    /**
     * 设置货位库存数量
     */
    设置货位库存数量,
    /**
     * 供应链确认拣货
     */
    供应链确认拣货,
    /**
     * PDA提交拣货
     */
    PDA提交拣货,
    /**
     * 提交播种任务
     */
    提交播种任务,
    /**
     * 播种任务完成
     */
    播种任务完成,
    /**
     * 上架任务完成
     */
    上架任务完成,
    /**
     * 无单移库
     */
    无单移库,
    /**
     * 有单移库
     */
    有单移库,
    /**
     * 批量移库
     */
    批量移库,
    /**
     * ERP库存同步
     */
    ERP库存同步,
    /**
     * 补货任务完成
     */
    补货任务完成,
    /**
     * 按关联货位同步货位库存
     */
    按关联货位同步货位库存,
    /**
     * 货区库存转移
     */
    货区库存转移,
    /**
     * 修改出库位
     */
    修改出库位,
    /**
     * 修改集货位
     */
    修改集货位,
    /**
     * 清理负库存
     */
    清理负库存,
    /**
     * 货位库存校正
     */
    货位库存校正,
    /**
     * 波次删除
     */
    波次删除
}
