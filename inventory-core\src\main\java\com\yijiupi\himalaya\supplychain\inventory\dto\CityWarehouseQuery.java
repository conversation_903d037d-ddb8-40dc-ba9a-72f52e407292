package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * @author: lid<PERSON>feng
 * @date 2018/10/18 15:01
 */
public class CityWarehouseQuery implements Serializable {

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 经销商id
     */
    private Long dealerId;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 状态
     */
    private Byte status;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页的数量
     */
    private Integer pageSize;

    /**
     * 获取城市id
     * 
     * @return
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置城市id
     * 
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    /**
     * 获取状态
     * 
     * @return
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置状态
     * 
     * @param status
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取页码
     * 
     * @return
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 设置页码
     * 
     * @param pageNum
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 获取每页数量
     * 
     * @return
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 设置每页的数量
     * 
     * @param pageSize
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public Long getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(Long dealerId) {
        this.dealerId = dealerId;
    }
}
