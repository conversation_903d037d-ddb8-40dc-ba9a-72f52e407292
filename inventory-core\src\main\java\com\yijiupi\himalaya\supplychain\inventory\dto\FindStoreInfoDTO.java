package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 库存报表
 * 
 * @author: chengkai
 * @date: 2022年9月6日
 */
public class FindStoreInfoDTO implements Serializable {
    private static final long serialVersionUID = 2644865009571329290L;
    /**
     * 序号
     */
    private Long sequence;
    /**
     * 产品库存ID
     */
    private String productStoreId;
    /**
     * 商品名称
     */
    private String productSkuName;
    /**
     * 产品SkuId
     */
    private Long productSkuId;
    /**
     * 规格名称
     */
    private String specificationName;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 货位
     */
    private String goodsPositionName;
    /**
     * 库存分类
     */
    private String storeOwnerTypeName;
    /**
     * 供应商
     */
    private String supplierName;
    /**
     * 库存总数量
     */
    private BigDecimal storeTotalCount;
    /**
     * 库存数量大单位
     */
    private BigDecimal storeCountMax;
    /**
     * 库存数量小单位
     */
    private BigDecimal storeCountMin;
    /**
     * 单位
     */
    private String packageName;
    /**
     * 单位
     */
    private String unitName;
    /**
     * 库存预警数量
     */
    private BigDecimal storeWaringCount;
    /**
     * 销售库存数量
     */
    private BigDecimal storePreTotalCount;
    /**
     * 预售库存数量小单位
     */
    private BigDecimal storePreCountMin;
    /**
     * 预售库存数量大单位
     */
    private BigDecimal storePreCountMax;
    /**
     * 销售库存总
     */
    private BigDecimal storeSaleTotalCount;
    /**
     * 销售库存数量大单位
     */
    private BigDecimal storeSaleCountMax;
    /**
     * 销售库存数量大单位
     */
    private BigDecimal storeSaleCountMin;
    /**
     * 未发货数量
     */
    private BigDecimal unDeliveryCount;
    /**
     * 未发货数量
     */
    private BigDecimal unDeliveryCountMax;
    /**
     * 未发货数量
     */
    private BigDecimal unDeliveryCountMin;
    /**
     * 配送中数量
     */
    private BigDecimal deliveryingCount;
    /**
     * 配送中数量
     */
    private BigDecimal deliveryingCountMax;
    /**
     * 配送中数量
     */
    private BigDecimal deliveryingCountMin;
    /**
     * 调入数量
     */
    private Integer adjustInCount;
    /**
     * 调出数量
     */
    private Integer adjustOutCount;
    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 货位
     */
    private String productLocation;
    /**
     * 渠道 0 微酒 1大宗
     */
    private Integer channel;
    /**
     * 包装规格系数
     */
    private BigDecimal specQuantity;

    /**
     * 货主名称
     */
    private Long ownerId;
    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 是否拆包
     */
    private Boolean unpackage;

    /**
     * 箱码
     */
    private List<String> packageCode;
    /**
     * 瓶码
     */
    private List<String> unitCode;
    /**
     * 二级货主名称
     */
    private String secOwnerName;

    private Long secOwnerId;

    /**
     * 产品信息规格ID
     */
    private Long productSpecificationId;
    /**
     * ProductTypeEnums
     * 产品类型：1.成品;2.半成品;3.包装材料
     */
    private Byte productType;

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public String getSecOwnerName() {
        return secOwnerName;
    }

    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Boolean getUnpackage() {
        return unpackage;
    }

    public void setUnpackage(Boolean unpackage) {
        this.unpackage = unpackage;
    }

    public List<String> getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(List<String> packageCode) {
        this.packageCode = packageCode;
    }

    public List<String> getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(List<String> unitCode) {
        this.unitCode = unitCode;
    }

    public Long getSequence() {
        return sequence;
    }

    public void setSequence(Long sequence) {
        this.sequence = sequence;
    }

    public BigDecimal getUnDeliveryCountMax() {
        return unDeliveryCountMax;
    }

    public void setUnDeliveryCountMax(BigDecimal unDeliveryCountMax) {
        this.unDeliveryCountMax = unDeliveryCountMax;
    }

    public BigDecimal getUnDeliveryCountMin() {
        return unDeliveryCountMin;
    }

    public void setUnDeliveryCountMin(BigDecimal unDeliveryCountMin) {
        this.unDeliveryCountMin = unDeliveryCountMin;
    }

    public BigDecimal getDeliveryingCountMax() {
        return deliveryingCountMax;
    }

    public void setDeliveryingCountMax(BigDecimal deliveryingCountMax) {
        this.deliveryingCountMax = deliveryingCountMax;
    }

    public BigDecimal getDeliveryingCountMin() {
        return deliveryingCountMin;
    }

    public void setDeliveryingCountMin(BigDecimal deliveryingCountMin) {
        this.deliveryingCountMin = deliveryingCountMin;
    }

    public BigDecimal getStoreCountMax() {
        return storeCountMax;
    }

    public void setStoreCountMax(BigDecimal storeCountMax) {
        this.storeCountMax = storeCountMax;
    }

    public BigDecimal getStoreCountMin() {
        return storeCountMin;
    }

    public void setStoreCountMin(BigDecimal storeCountMin) {
        this.storeCountMin = storeCountMin;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getStorePreTotalCount() {
        return storePreTotalCount;
    }

    public void setStorePreTotalCount(BigDecimal storePreTotalCount) {
        this.storePreTotalCount = storePreTotalCount;
    }

    public BigDecimal getStorePreCountMin() {
        return storePreCountMin;
    }

    public void setStorePreCountMin(BigDecimal storePreCountMin) {
        this.storePreCountMin = storePreCountMin;
    }

    public BigDecimal getStorePreCountMax() {
        return storePreCountMax;
    }

    public void setStorePreCountMax(BigDecimal storePreCountMax) {
        this.storePreCountMax = storePreCountMax;
    }

    public BigDecimal getStoreSaleTotalCount() {
        return storeSaleTotalCount;
    }

    public void setStoreSaleTotalCount(BigDecimal storeSaleTotalCount) {
        this.storeSaleTotalCount = storeSaleTotalCount;
    }

    public BigDecimal getStoreSaleCountMax() {
        return storeSaleCountMax;
    }

    public void setStoreSaleCountMax(BigDecimal storeSaleCountMax) {
        this.storeSaleCountMax = storeSaleCountMax;
    }

    public BigDecimal getStoreSaleCountMin() {
        return storeSaleCountMin;
    }

    public void setStoreSaleCountMin(BigDecimal storeSaleCountMin) {
        this.storeSaleCountMin = storeSaleCountMin;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public String getProductSkuName() {
        return productSkuName;
    }

    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getGoodsPositionName() {
        return goodsPositionName;
    }

    public void setGoodsPositionName(String goodsPositionName) {
        this.goodsPositionName = goodsPositionName;
    }

    public String getStoreOwnerTypeName() {
        return storeOwnerTypeName;
    }

    public void setStoreOwnerTypeName(String storeOwnerTypeName) {
        this.storeOwnerTypeName = storeOwnerTypeName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public BigDecimal getStoreTotalCount() {
        return storeTotalCount;
    }

    public void setStoreTotalCount(BigDecimal storeTotalCount) {
        this.storeTotalCount = storeTotalCount;
    }

    public BigDecimal getStoreWaringCount() {
        return storeWaringCount;
    }

    public void setStoreWaringCount(BigDecimal storeWaringCount) {
        this.storeWaringCount = storeWaringCount;
    }

    public BigDecimal getUnDeliveryCount() {
        return unDeliveryCount;
    }

    public void setUnDeliveryCount(BigDecimal unDeliveryCount) {
        this.unDeliveryCount = unDeliveryCount;
    }

    public BigDecimal getDeliveryingCount() {
        return deliveryingCount;
    }

    public void setDeliveryingCount(BigDecimal deliveryingCount) {
        this.deliveryingCount = deliveryingCount;
    }

    public Integer getAdjustInCount() {
        return adjustInCount;
    }

    public void setAdjustInCount(Integer adjustInCount) {
        this.adjustInCount = adjustInCount;
    }

    public Integer getAdjustOutCount() {
        return adjustOutCount;
    }

    public void setAdjustOutCount(Integer adjustOutCount) {
        this.adjustOutCount = adjustOutCount;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getProductLocation() {
        return productLocation;
    }

    public void setProductLocation(String productLocation) {
        this.productLocation = productLocation;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取specQuantity
     * 
     * @return specQuantity specQuantity
     */
    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    /**
     * 设置specQuantity
     * 
     * @param specQuantity specQuantity
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取ownerName
     * 
     * @return ownerName ownerName
     */
    public String getOwnerName() {
        return ownerName;
    }

    /**
     * 设置ownerName
     * 
     * @param ownerName ownerName
     */
    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Byte getProductType() {
        return productType;
    }

    public void setProductType(Byte productType) {
        this.productType = productType;
    }
}
