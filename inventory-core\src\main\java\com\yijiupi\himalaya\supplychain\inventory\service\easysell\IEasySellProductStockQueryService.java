package com.yijiupi.himalaya.supplychain.inventory.service.easysell;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.easysell.*;
import com.yijiupi.himalaya.supplychain.dto.product.ProductTotalRecordDTO;

/**
 * 库存查询
 * 
 * @author: lid<PERSON>feng
 * @date: 2018/8/13 16:34
 */
public interface IEasySellProductStockQueryService {

    /**
     * 根据仓库编号、经销商编号查询商品库存信息
     */
    PageList<ProductStoreReturnDTO> findProductStoreList(ProductStoreQueryDTO productStoreQueryDTO);

    /**
     * 根据商品规格Id和经销商Id查询不同仓库的商品库存
     * 
     * @param productInfoStockQueryDTO
     * @return
     */
    PageList<ProductInfoStoreDTO> findProductInfoStoreList(ProductInfoStoreQueryDTO productInfoStockQueryDTO);

    /**
     * 根据商品skuId，仓库id,经销商id查询商品明细
     * 
     * @param productChangeStockDTO
     * @return
     */
    ProductTotalRecordDTO findProductStoreRecordList(ProductStoreChangeDetailQueryDTO productChangeStockDTO);

}
