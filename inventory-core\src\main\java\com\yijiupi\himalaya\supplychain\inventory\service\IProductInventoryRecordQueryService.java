package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.List;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.supplychain.dto.product.InventoryProductStoreBatchChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStoreChangeRecordByOrderDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStoreChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.FindStoreChangeInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.FindStoreChangePageDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.StoreChangeInfoDTO;
import com.yijiupi.himalaya.supplychain.search.ProductStoreChangeRecordByOrderSO;
import com.yijiupi.himalaya.supplychain.search.ProductStoreRecordSO;

/**
 * 库存变更记录查询(仓库库存)
 *
 * @author: wangdan
 * @date: 2017年8月25日
 */
public interface IProductInventoryRecordQueryService {
    /**
     * 查询仓库库存变更记录明细列表 todo productskuid,source下版本不用传递.
     */
    @Deprecated
    PageList<ProductStoreChangeRecordDTO> findProductStoreRecordList(ProductStoreRecordSO productStoreRecordSO,
        PagerCondition pager, Long productSkuId);

    /**
     * 根据SKUID和仓库Id查询库存变更记录
     * 
     * @param productStoreRecordSO
     * @param pager
     * @return
     */
    PageList<ProductStoreChangeRecordDTO>
        findProductInventoryChangeRecordPOListBySkuId(ProductStoreRecordSO productStoreRecordSO, PagerCondition pager);

    /**
     * 查询订单库存变更记录
     * 
     * @return
     */
    PageList<ProductStoreChangeRecordByOrderDTO>
        listProductStoreChangeRecordByOrder(ProductStoreChangeRecordByOrderSO orderSO);

    /**
     * 查询批次库存变更记录
     * 
     * @param orderSO
     * @return
     */
    List<InventoryProductStoreBatchChangeRecordDTO>
        findProductStoreBatchChangeRecordList(ProductStoreChangeRecordByOrderSO orderSO);

    /**
     * 查单据库存变更总数
     * 
     * @param orderSO
     * @return
     */
    List<ProductStoreChangeRecordByOrderDTO>
        listProductStoreChangeRecordGroupByOrder(ProductStoreChangeRecordByOrderSO orderSO);

    // /**
    // * 查询ERP库存变更记录明细列表
    // */
    // PageList<ProductStoreChangeRecordDTO> findErpProductStoreRecordList(ProductStoreRecordSO productStoreRecordSO,
    // PagerCondition pager, Integer prodcutInfoSpecId);

    // /**
    // * 查询sku在对应城市下所有仓库库存变更记录集合
    // */
    // PageList<CityInventoryRecordDTO> findCityProductInventoryRecordList(CityInventoryRecordQueryDTO
    // CityInventoryRecordQueryDTO, PagerCondition pager);

    /**
     * 根据规格id、货主id、二级货主id查询库存变更记录
     */
    PageList<StoreChangeInfoDTO> findInventoryChangeBySpec(ProductStoreRecordSO productStoreRecordSO,
        PagerCondition pager);

    /**
     * 库存变更记录（商户平台）
     */
    PageList<FindStoreChangeInfoDTO> findStoreChangePage(FindStoreChangePageDTO findStoreChangePageDTO);
}
