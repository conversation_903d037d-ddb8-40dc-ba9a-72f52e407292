package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.util.Collections;
import java.util.List;

import com.yijiupi.himalaya.base.search.PagerCondition;

/**
 * 同步货位库存
 *
 * <AUTHOR>
 * @date 2019/5/16 14:46
 */
public class BatchInventorySyncQueryDTO extends PagerCondition {

    private static final long serialVersionUID = 7466417215602368517L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 货区
     */
    private String locationArea;

    /**
     * 货区集合
     */
    private List<String> locationAreaList;

    /**
     * 货区集合
     */
    private List<Integer> locationTypeList;

    private Long ownerId;

    /**
     * 是否返回处理逻辑 （true：只返回处理逻辑数据，不会执行实际操作 默认false：执行实际操作）
     */
    private Boolean returnLogic = false;

    /**
     * 是否只处理差异大于0的； 默认为null：处理等于0的； true：处理大于0的； false：处理小于0的；
     */
    private Boolean processOutWeigh;

    /**
     * 是否直接处理 为null或者true：处理 false：不处理
     */
    private Boolean processDirector;

    /**
     * 库存id列表
     */
    private List<String> productStoreIds;

    /**
     * 目的货位
     */
    private String toLocationName;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 巷道集合
     */
    private List<String> roadWayList;

    /**
     * 巷道
     */
    private String roadWay;

    /**
     * 查询是否删除的sku：0 否，1 是
     */
    private List<Byte> deleted = Collections.singletonList((byte)0);

    /**
     * 货区
     */
    private String locationType;

    /**
     * skuid集合
     */
    private List<Long> skuIdList;

    /**
     * 货位id列表
     */
    private List<Long> locationIdList;

    public Boolean getProcessDirector() {
        return processDirector;
    }

    public void setProcessDirector(Boolean processDirector) {
        this.processDirector = processDirector;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Boolean getProcessOutWeigh() {
        return processOutWeigh;
    }

    public void setProcessOutWeigh(Boolean processOutWeigh) {
        this.processOutWeigh = processOutWeigh;
    }

    public Boolean getReturnLogic() {
        return returnLogic;
    }

    public void setReturnLogic(Boolean returnLogic) {
        this.returnLogic = returnLogic;
    }

    public List<Integer> getLocationTypeList() {
        return locationTypeList;
    }

    public void setLocationTypeList(List<Integer> locationTypeList) {
        this.locationTypeList = locationTypeList;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getLocationArea() {
        return locationArea;
    }

    public void setLocationArea(String locationArea) {
        this.locationArea = locationArea;
    }

    public List<String> getLocationAreaList() {
        return locationAreaList;
    }

    public void setLocationAreaList(List<String> locationAreaList) {
        this.locationAreaList = locationAreaList;
    }

    public List<String> getProductStoreIds() {
        return productStoreIds;
    }

    public void setProductStoreIds(List<String> productStoreIds) {
        this.productStoreIds = productStoreIds;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public List<String> getRoadWayList() {
        return roadWayList;
    }

    public void setRoadWayList(List<String> roadWayList) {
        this.roadWayList = roadWayList;
    }

    public String getRoadWay() {
        return roadWay;
    }

    public void setRoadWay(String roadWay) {
        this.roadWay = roadWay;
    }

    public List<Byte> getDeleted() {
        return deleted;
    }

    public void setDeleted(List<Byte> deleted) {
        this.deleted = deleted;
    }

    public String getLocationType() {
        return locationType;
    }

    public void setLocationType(String locationType) {
        this.locationType = locationType;
    }

    public List<Long> getSkuIdList() {
        return skuIdList;
    }

    public void setSkuIdList(List<Long> skuIdList) {
        this.skuIdList = skuIdList;
    }

    /**
     * 获取 货位id列表
     *
     * @return locationIdList 货位id列表
     */
    public List<Long> getLocationIdList() {
        return this.locationIdList;
    }

    /**
     * 设置 货位id列表
     *
     * @param locationIdList 货位id列表
     */
    public void setLocationIdList(List<Long> locationIdList) {
        this.locationIdList = locationIdList;
    }
}
