package com.yijiupi.himalaya.supplychain.inventory.dto.outstock;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderDTO;

/**
 * 处理出库单库存DTO
 */
public class ProcessOutStockOrderInventoryDTO implements Serializable {
    /**
     * ERP事件类型
     */
    private Integer erpEventType;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 是否直接抛出异常
     */
    private Boolean allowThrowException = false;

    /**
     * 是否变更库存
     */
    private Boolean needToChangeStore;

    /**
     * 是否变更批次库存
     */
    private Boolean needToChangeBatchStore;

    /**
     * 是否变更销售库存,默认需要
     */
    private Boolean needToChangeSaleStore = true;

    /**
     * 是否需要跳过不存在的sku
     */
    private Boolean needSkipNotExitsSku = true;

    /**
     * 是否需要修改配送数量
     */
    private Boolean needUpdateDeliveryCount = false;

    /**
     * 出库商品详情
     */
    private List<OutStockOrderDTO> outStockOrderDTOList;

    /***
     * 判断是否发送订单明细到oms
     */
    private Boolean sendDetailToOMS;

    public Integer getErpEventType() {
        return erpEventType;
    }

    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<OutStockOrderDTO> getOutStockOrderDTOList() {
        return outStockOrderDTOList;
    }

    public void setOutStockOrderDTOList(List<OutStockOrderDTO> outStockOrderDTOList) {
        this.outStockOrderDTOList = outStockOrderDTOList;
    }

    public Boolean getAllowThrowException() {
        return allowThrowException;
    }

    public void setAllowThrowException(Boolean allowThrowException) {
        this.allowThrowException = allowThrowException;
    }

    public Boolean getNeedToChangeStore() {
        return needToChangeStore;
    }

    public void setNeedToChangeStore(Boolean needToChangeStore) {
        this.needToChangeStore = needToChangeStore;
    }

    public Boolean getNeedToChangeBatchStore() {
        if (needToChangeBatchStore == null && needToChangeStore != null) {
            needToChangeBatchStore = true;
        } else if (needToChangeBatchStore == null) {
            needToChangeBatchStore = false;
        }
        return needToChangeBatchStore;
    }

    public void setNeedToChangeBatchStore(Boolean needToChangeBatchStore) {
        this.needToChangeBatchStore = needToChangeBatchStore;
    }

    public Boolean getNeedToChangeSaleStore() {
        return needToChangeSaleStore;
    }

    public void setNeedToChangeSaleStore(Boolean needToChangeSaleStore) {
        this.needToChangeSaleStore = needToChangeSaleStore;
    }

    public Boolean getNeedSkipNotExitsSku() {
        return needSkipNotExitsSku;
    }

    public void setNeedSkipNotExitsSku(Boolean needSkipNotExitsSku) {
        this.needSkipNotExitsSku = needSkipNotExitsSku;
    }

    public Boolean getNeedUpdateDeliveryCount() {
        return needUpdateDeliveryCount;
    }

    public void setNeedUpdateDeliveryCount(Boolean needUpdateDeliveryCount) {
        this.needUpdateDeliveryCount = needUpdateDeliveryCount;
    }

    public Boolean getSendDetailToOMS() {
        return sendDetailToOMS;
    }

    public void setSendDetailToOMS(Boolean sendDetailToOMS) {
        this.sendDetailToOMS = sendDetailToOMS;
    }
}
