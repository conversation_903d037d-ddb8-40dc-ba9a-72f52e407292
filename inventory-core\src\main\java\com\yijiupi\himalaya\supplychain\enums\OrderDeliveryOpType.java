package com.yijiupi.himalaya.supplychain.enums;

/**
 * Created by 余明 on 2017-12-28.
 */
public class OrderDeliveryOpType {

    /**
     * 订单批量发货标识
     */
    public static final String ORDER_DELIVER_MESSAGE_TYPE = "orderDelivery:";

    /**
     * 内配单发货标识
     */
    public static final String ALLCATION_DISTRIBUTION_ORDER_DELIVERY_MESSAGE_TYPE = "allocationOrderDeliveryType:";

    /**
     * 订单缺货标记
     */
    public static final String ORDER_OUT_OF_STOCK_MESSAGE_TYPE = "orderOutOfStock:";

    /**
     * 订单延迟配送标识
     */
    public static final String ORDER_DELIVERY_DELAY_TYPE = "orderDeliveryDelay:";

    /**
     * 入库单
     */
    public static final String IN_STOCK_ORDER_TYPE = "inStockOrderType:";

    /**
     * 自提出库
     */
    public static final String ZITI_OUT_STOCK_ORDER_TYPE = "ziTiOutStockOrderType:";

    /**
     * 订单配送失败标识
     */
    public static final String ORDER_DELIVERY_FAIL_TYPE = "orderDeliveryFaile:";

    /**
     * 订单配送完成标识
     */
    public static final String ORDER_DELIVERY_COMPLETE_TYPE = "orderComplete:";

    /**
     * 订单退货完成标识
     */
    public static final String RETURN_ORDER_COMPLETE_TYPE = "returnOrderComplete:";

    /**
     * 内配单入库标识
     */
    public static final String INTERNAL_DELIVERY_ORDER_IN_STOCK_TYPE = "internalDeliveryInStockType:";

    /**
     * 订单直接发货标识
     */
    public static final String ORDER_DIRECTLY_TYPE = "orderDirectly:";

    /**
     * 订单直接发货标识
     */
    public static final String RETURN_ORDER_DIRECTLY_TYPE = "returnOrderDirectly:";

    /**
     * 取货确认无误标识
     */
    public static final String ORDER_INVENTORY_DYY_MESSAGE_TYPE = "orderInventoryDYY:";

    /**
     * 经销商退货
     */
    public static final String DELIVERY_RETURN_TYPE = "deliveryReturn:";

    /**
     * 经销商配送失败
     */
    public static final String DELIVERY_FAILED_TYPE = "deliveryFailed:";
}
