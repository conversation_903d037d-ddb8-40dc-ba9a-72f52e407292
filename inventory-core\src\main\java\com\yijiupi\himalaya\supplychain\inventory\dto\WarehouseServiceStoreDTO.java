package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 *
 * 仓库信息库存查询
 * 
 * @author: lidengfeng
 * @date 2018/12/5 14:41
 */
public class WarehouseServiceStoreDTO implements Serializable {

    /**
     * 经销商id
     */
    private String dealerId;

    /**
     * 经销商手机号
     */
    private String mobileNo;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 获取手机号
     * 
     * @return
     */
    public String getMobileNo() {
        return mobileNo;
    }

    /**
     * 设置手机号
     * 
     * @param mobileNo
     */
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public String getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(String dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取 城市id
     * 
     * @return
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市id
     * 
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 城市
     * 
     * @return
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置 城市
     * 
     * @param city
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取仓库名称
     * 
     * @return
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置 仓库名称
     * 
     * @param warehouseName
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }
}
