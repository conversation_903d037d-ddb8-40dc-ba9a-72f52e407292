package com.yijiupi.himalaya.supplychain.dto.ordercenter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-12 14:38
 **/
public class ProductOwnerInventoryQueryParam implements Serializable {
    /**
     * 城市 id
     */
    private Integer cityId;
    /**
     * 仓库 id
     */
    private Integer warehouseId;
    /**
     * 产品模型
     */
    private List<ProductOwnerInfoDTO> productOwnerInfo;

    public static ProductOwnerInventoryQueryParam of(Integer cityId, Integer warehouseId, List<ProductOwnerInfoDTO> productOwnerInfo) {
        ProductOwnerInventoryQueryParam result = new ProductOwnerInventoryQueryParam();
        result.setCityId(cityId);
        result.setWarehouseId(warehouseId);
        result.setProductOwnerInfo(productOwnerInfo);
        return result;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<ProductOwnerInfoDTO> getProductOwnerInfo() {
        return productOwnerInfo;
    }

    public void setProductOwnerInfo(List<ProductOwnerInfoDTO> productOwnerInfo) {
        this.productOwnerInfo = productOwnerInfo;
    }
}
