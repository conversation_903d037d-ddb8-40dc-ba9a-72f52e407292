package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.constant.ERPDisposedInventoryType;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute.BatchAttributeInfoBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.model.StoreBatchQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.SyncBatchInventoryByProductLocationConvertor;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.inventory.ProductStoreMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventorySyncPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.util.BatchInventoryConfigUtil;
import com.yijiupi.himalaya.supplychain.batchinventory.util.HttpUtil;
import com.yijiupi.himalaya.supplychain.batchinventory.util.StreamUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.util.UUIDUtil;
import com.yijiupi.himalaya.supplychain.dto.ReportBatchLocationInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.ReportBatchLocationInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.enums.InventoryChangeTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.BaseResponse;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderItemDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferStateEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IStoreTransferOrderService;
import com.yijiupi.himalaya.supplychain.instockorder.dto.communal.InStockCommOrderAddDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockCommService;
import com.yijiupi.himalaya.supplychain.inventory.constant.InventoryChangeTypes;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductStoreQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IProductInventoryRecordManagerService;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryCheckService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.InStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.ProcessOutStockOrderCommDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommManageService;
import com.yijiupi.himalaya.supplychain.service.IWarehousePickingQueryService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductFeatureEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.LocationProductQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.*;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskQueryService;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 货位库存同步
 *
 * <AUTHOR>
 * @date 2019/2/23 18:19
 */
@Service
public class BatchInventorySyncBL {
    private final static Logger LOGGER = LoggerFactory.getLogger(BatchInventorySyncBL.class);

    @Autowired
    private BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;
    @Autowired
    private BatchAttributeInfoBL batchAttributeInfoBL;
    @Autowired
    private ProductStoreBatchBL productStoreBatchBL;
    @Autowired
    private ProductStoreBatchChangeRecordBL productStoreBatchChangeRecordBL;
    @Autowired
    private BatchInventoryQueryBL batchInventoryQueryBL;
    @Autowired
    private BatchInventoryManageBL batchInventoryManageBL;
    @Autowired
    private ProductStoreMapper productStoreMapper;
    @Autowired
    private SyncBatchInventoryByProductLocationConvertor syncInventoryConvertor;
    @Autowired
    private BatchInventorySyncAsyncBL batchInventorySyncAsyncBL;

    @Reference
    private IStoreTransferOrderService iStoreTransferOrderService;
    @Reference
    private ILocationService iLocationService;
    @Reference
    private IWarehouseInventoryCheckService iWarehouseInventoryCheckService;
    @Reference
    private LocationAreaService locationAreaService;
    @Reference
    private IWarehousePickingQueryService warehousePickingQueryService;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IProductSkuService productSkuService;
    @Reference
    private IProductInventoryRecordManagerService iProductInventoryRecordManagerService;
    @Reference
    private IInStockCommService iInStockCommService;
    @Reference
    private IOutStockCommManageService iOutStockCommManageService;
    @Reference
    private IProductLocationService iProductLocationService;
    @Reference
    private IBatchTaskQueryService iBatchTaskQueryService;
    @Reference
    private IProductSkuQueryService productSkuQueryService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    public static final ExecutorService EXECUTOR =
            new ThreadPoolExecutor(10, 20, 10L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(),
                    new DefaultThreadFactory("batchSyncTask"), new ThreadPoolExecutor.CallerRunsPolicy());

    // 零拣位,分拣位,零拣区,拣货区,存储位,存储区,暂存位,暂存区 ,容器位,移动区,退货位,退货区,残次品位,残次品区 ,出库位,周转区,集货位,集货区
    private static final List<Integer> REGULATION_ORDER_LIST = Arrays.asList(LocationEnum.零拣位.getType(), // 0
            LocationEnum.分拣位.getType(), // 24
            LocationAreaEnum.零拣区.getType(), // 52
            LocationAreaEnum.拣货区.getType(), // 51
            LocationEnum.存储位.getType(), // 1
            LocationAreaEnum.存储区.getType(), // 50
            LocationEnum.暂存位.getType(), // 20
            LocationAreaEnum.暂存区.getType(), // 55
            LocationEnum.容器位.getType(), // 28
            LocationAreaEnum.移动区.getType(), // 63
            LocationEnum.退货位.getType(), // 22
            LocationAreaEnum.退货区.getType(), // 54
            LocationEnum.残次品位.getType(), // 29
            LocationAreaEnum.残次品区.getType(), // 60
            LocationEnum.出库位.getType(), // 21
            LocationAreaEnum.周转区.getType(), // 53
            LocationEnum.集货位.getType(), // 26
            LocationAreaEnum.集货区.getType() // 61
    );

    /**
     * 根据产品货位配置同步货位库存
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncBatchInventoryByProductLocationList(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        AssertUtils.notNull(batchInventorySyncQueryDTO, "同步货位库存参数不能为空");
        LOGGER.info("同步货位库存：{}", JSON.toJSONString(batchInventorySyncQueryDTO));

        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(batchInventorySyncQueryDTO.getWarehouseId());
        batchInventorySyncQueryDTO.setCityId(warehouse.getCityId());

        // 1、获取需要同步的货位库存信息
        List<BatchInventorySyncPO> syncPOList =
                batchInventoryProductStoreBatchMapper.listBatchInventoryBySync(batchInventorySyncQueryDTO);
        if (CollectionUtils.isEmpty(syncPOList)) {
            return;
        }
        LOGGER.info("数据量为:{}", syncPOList.size());
        syncBatchInventoryByProductLocation(syncPOList, batchInventorySyncQueryDTO);
    }

    /**
     * 根据产品货位配置同步货位库存
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncBatchInventoryByProductLocation(List<BatchInventorySyncPO> syncPOList,
                                                    BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        if (CollectionUtils.isEmpty(syncPOList)) {
            return;
        }
        // 2、获取残次品库存
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        List<Long> skuIdList =
                syncPOList.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        batchInventoryQueryDTO.setSkuIds(skuIdList);
        batchInventoryQueryDTO.setWarehouseId(batchInventorySyncQueryDTO.getWarehouseId());
        List<Integer> subCategoryList = new ArrayList<>();
        subCategoryList.add(LocationEnum.残次品位.getType());
        subCategoryList.add(LocationAreaEnum.残次品区.getType());
        batchInventoryQueryDTO.setSubCategoryList(subCategoryList);
        List<BatchInventoryDTO> defectiveBatchStoreList =
                batchInventoryQueryBL.findBatchInventoryList(batchInventoryQueryDTO).getDataList();
        Map<Long, BigDecimal> defectiveStoreMap = new HashMap<>(16);
        List<String> defectiveStoreBatchIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(defectiveBatchStoreList)) {
            // 残次品批次库存id
            defectiveStoreBatchIdList =
                    defectiveBatchStoreList.stream().map(p -> p.getStoreBatchId()).collect(Collectors.toList());
            // 残次品数量
            defectiveBatchStoreList.stream().collect(Collectors.groupingBy(p -> p.getProductSkuId()))
                    .forEach((skuId, list) -> {
                        defectiveStoreMap.put(skuId,
                                list.stream().map(p -> p.getStoreTotalCount()).reduce(BigDecimal.ZERO, BigDecimal::add));
                    });
        }
        LOGGER.info(String.format("同步货位库存-第二步查残次品库存，共%s条", defectiveStoreMap.size()));

        // 3、封装货位库存记录PO
        List<ProductStoreBatchPO> productStoreBatchPOList = new ArrayList<>();
        syncPOList.forEach(po -> {
            // 去重，ProductStoreId不允许重复
            if (productStoreBatchPOList.stream().anyMatch(p -> p.getProductStoreId().equals(po.getProductStoreId()))) {
                return;
            }
            ProductStoreBatchPO storeBatchPO = new ProductStoreBatchPO();
            BeanUtils.copyProperties(po, storeBatchPO);
            storeBatchPO.setId(UUIDUtil.getUUID().replaceAll("-", ""));
            String batchAttributeNo = batchAttributeInfoBL.getProductBatchAttributeInfoNo(po.getProductStoreId(), null,
                    null, null, new ArrayList<>(), true);
            storeBatchPO.setBatchAttributeInfoNo(batchAttributeNo);
            // 排除残次品
            BigDecimal defectiveCount = defectiveStoreMap.get(storeBatchPO.getProductSkuId());
            if (defectiveCount != null) {
                storeBatchPO.setTotalCount(storeBatchPO.getTotalCount().subtract(defectiveCount));
            }
            // 关联货位有多个的，优先分拣位和零拣位
            Optional<BatchInventorySyncPO> locationPoOptional =
                    syncPOList.stream().filter(p -> Objects.equals(p.getProductSkuId(), po.getProductSkuId()))
                            .filter(p -> Objects.equals(p.getSubcategory(), LocationEnum.分拣位.getType())
                                    || Objects.equals(p.getSubcategory(), LocationEnum.零拣位.getType()))
                            .findAny();
            locationPoOptional.ifPresent(batchInventorySyncPO -> {
                storeBatchPO.setLocationId(batchInventorySyncPO.getLocationId());
                storeBatchPO.setLocationCategory(batchInventorySyncPO.getLocationCategory());
                storeBatchPO.setLocationName(batchInventorySyncPO.getLocationName());
            });
            productStoreBatchPOList.add(storeBatchPO);
        });

        Integer warehouseId = batchInventoryQueryDTO.getWarehouseId();
        boolean isOpenLocationGroup = false;
        boolean isOpenStock = false;
        if (warehouseId != null) {
            isOpenLocationGroup = warehouseConfigService.isOpenLocationGroup(warehouseId);
            ;
            isOpenStock = warehouseConfigService.isOpenLocationStock(warehouseId);
        }

        // 转货位为货位组
        transformLocToLocGroup(productStoreBatchPOList, batchInventorySyncQueryDTO.getWarehouseId(),
                isOpenLocationGroup, isOpenStock);
        // LOGGER.info("转为货位组之后：{}", JSON.toJSONString(productStoreBatchPOList));
        // 填充生产日期
        syncInventoryConvertor.fillProductionDate(productStoreBatchPOList);
        // 拆出分拣占用
        List<ProductStoreBatchPO> splitProductStoreBatchList =
                syncInventoryConvertor.splitByPickOccupy(productStoreBatchPOList, batchInventorySyncQueryDTO);

        Lists.partition(splitProductStoreBatchList, 100).forEach(item -> {
            LOGGER.info("插入数据为:{}", JSON.toJSONString(item));
        });

        // 3、批量新增货位库存
        PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
        pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.按关联货位同步货位库存.name());
        List<String> finalDefectiveStoreBatchIdList = defectiveStoreBatchIdList;
        List<List<ProductStoreBatchPO>> lists = Lists.partition(splitProductStoreBatchList, 100);
        for (List<ProductStoreBatchPO> list : lists) {
            List<String> storeIds = list.stream().map(p -> p.getProductStoreId()).collect(Collectors.toList());
            // 删除原货位库存
            // productStoreBatchMapper.deleteBatchInventoryByStoreIds(storeIds);
            // 将原货位库存数量更改为0
            // productStoreBatchMapper.updateBatchInventoryZeroByStoreIds(storeIds, defectiveStoreBatchIdList);
            // // 新增货位库存
            // productStoreBatchMapper.insertBatchInventoryPOList(list);
            // // 新增批次库存变更记录
            // productStoreBatchChangeRecordBL.createStoreBatchChangeRecordByPickUp(pickUpChangeRecordDTO, list);
            CompletableFuture<Boolean> completableFuture =
                    CompletableFuture.supplyAsync(() -> batchInventorySyncAsyncBL.syncBatchInventoryByProductLocation(list,
                            finalDefectiveStoreBatchIdList, pickUpChangeRecordDTO), EXECUTOR);
            try {
                boolean result = completableFuture.get();
                if (Boolean.TRUE.equals(result)) {
                    LOGGER.warn("同步成功, {}", JSON.toJSONString(storeIds));
                }
            } catch (Exception e) {
                LOGGER.warn("出库批次同步报错 : " + JSON.toJSONString(storeIds), e);
            }
        }
    }

    /**
     * 转换ProductStoreBatchPO货位为货位组
     */
    public void transformLocToLocGroup(List<ProductStoreBatchPO> productStoreBatchPOList, Integer warehouseId,
                                       boolean isOpenLocationGroup, boolean isOpenStock) {
        if (isOpenLocationGroup && isOpenStock) {
            List<String> locIdList = productStoreBatchPOList.stream().filter(e -> e.getLocationId() != null)
                    .map(e -> e.getLocationId().toString()).distinct().collect(Collectors.toList());
            List<LocationReturnDTO> locGroupIdList =
                    locationAreaService.findLocationAreaListExcludeDefective(locIdList);
            if (CollectionUtils.isEmpty(locGroupIdList)) {
                return;
            }

            productStoreBatchPOList.forEach(elem -> {
                LocationReturnDTO locationReturnDTO =
                        locGroupIdList.stream().filter(p -> p.getId().equals(elem.getLocationId())).findAny().orElse(null);
                if (locationReturnDTO != null) {
                    elem.setLocationId(locationReturnDTO.getArea_Id());
                    elem.setLocationName(locationReturnDTO.getArea());
                    elem.setLocationCategory(
                            locationReturnDTO.getCategory() == null ? null : locationReturnDTO.getCategory().intValue());
                    elem.setSubcategory(locationReturnDTO.getSubcategory() == null ? null
                            : locationReturnDTO.getSubcategory().intValue());
                }
            });
        }
    }

    public PageList<BatchInventorySyncPO> getPageList(BatchInventorySyncQueryDTO queryDTO) {
        PageResult<BatchInventorySyncPO> pageResult = batchInventoryProductStoreBatchMapper.pageListBatchInventoryBySync(queryDTO,
                queryDTO.getCurrentPage(), queryDTO.getPageSize());
        PageList<BatchInventorySyncPO> result = pageResult.toPageList();
        if (!org.springframework.util.CollectionUtils.isEmpty(result.getDataList())) {
            LOGGER.info("查询出的数据为：{}", JSON.toJSONString(pageResult.toPageList()));
        }
        return result;
    }

    /**
     * 清除总库存为0的货位库存不为0的项
     */
    public void clearZeroBatchInventoryByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        List<BatchInventorySyncPO> lstBatchStore =
                batchInventoryProductStoreBatchMapper.getZeroBatchInventoryByWarehouseId(warehouseId);
        if (CollectionUtils.isEmpty(lstBatchStore)) {
            return;
        }
        LOGGER.info(String.format("仓库ID：%s，清理不为0的货位库存：%s", warehouseId, JSON.toJSONString(lstBatchStore)));
        batchInventoryProductStoreBatchMapper.clearZeroBatchInventoryByWarehouseId(lstBatchStore);
    }

    /**
     * 清除负货位库存
     *
     * @param batchInventorySyncQueryDTO
     */
    public BatchInventoryClearNegativeLogicDTO
    clearBatchInventoryByProductLocaion(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        AssertUtils.notNull(batchInventorySyncQueryDTO, "清理负货位库存参数不能为空");
        AssertUtils.notNull(batchInventorySyncQueryDTO.getWarehouseId(), "仓库id不能为空");
        LOGGER.info("清理负货位库存：{}", JSON.toJSONString(batchInventorySyncQueryDTO));

        // 1、获取需要处理的负货位库存信息
        List<ProductStoreBatchPO> lstTmpNegativeBatchInventory =
                batchInventoryProductStoreBatchMapper.listNegativeBatchInventory(batchInventorySyncQueryDTO);
        if (CollectionUtils.isEmpty(lstTmpNegativeBatchInventory)) {
            LOGGER.warn("syncPOList is null");
            return null;
        }
        List<ProductStoreBatchPO> syncPOList = new ArrayList<>();
        lstTmpNegativeBatchInventory.forEach(p -> {
            if (!syncPOList.stream().anyMatch(q -> Objects.equals(q.getId(), p.getId()))) {
                syncPOList.add(p);
            }
        });

        List<String> lstStoreIds =
                syncPOList.stream().map(p -> p.getProductStoreId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstStoreIds)) {
            LOGGER.warn("lstStoreIds is null");
            return null;
        }
        // 货区有负库存的所有货位库存
        List<ProductStoreBatchPO> batchListByProductStoreIds = pageListProductStoreBatch(lstStoreIds);
        if (CollectionUtils.isEmpty(batchListByProductStoreIds)) {
            LOGGER.warn("batchListByProductStoreIds is null");
            return null;
        }
        // 2、封装货位库存记录PO
        List<ProductStoreBatchPO> productStoreBatchPOList = new ArrayList<>();
        syncPOList.forEach(po -> {
            BigDecimal totalCount = po.getTotalCount().abs();
            BigDecimal changeCount = BigDecimal.ZERO;
            BigDecimal finalChangeCount = BigDecimal.ZERO;
            List<ProductStoreBatchPO> lstTmpBatch = batchListByProductStoreIds.stream()
                    .filter(p -> Objects.equals(p.getProductStoreId(), po.getProductStoreId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lstTmpBatch)) {
                LOGGER.warn("lstTmpBatch is null");
                return;
            }
            // if (lstTmpBatch.stream().anyMatch(p -> p.getTotalCount().compareTo(po.getTotalCount().abs()) == 0)) {
            // ProductStoreBatchPO productStoreBatchPO = lstTmpBatch.stream().filter(p ->
            // p.getTotalCount().compareTo(po.getTotalCount().abs()) == 0).findAny().get();
            // processBatchChangePO(productStoreBatchPOList, totalCount, po.getProductStoreBatchId());
            // processBatchChangePO(productStoreBatchPOList, totalCount.multiply(new BigDecimal(-1)),
            // productStoreBatchPO.getId());
            // productStoreBatchPO.setTotalCount(productStoreBatchPO.getTotalCount().subtract(totalCount));
            // return;
            // }
            Collections.sort(lstTmpBatch, (o1, o2) -> {
                int io1 = REGULATION_ORDER_LIST.indexOf(o1.getSubcategory());
                int io2 = REGULATION_ORDER_LIST.indexOf(o2.getSubcategory());
                return (io1 == -1 || io2 == -1) ? (io2 - io1) : (io1 - io2);
            });
            // Collections.sort(lstTmpBatch,
            // Comparator.nullsFirst(Comparator.comparing(ProductStoreBatchPO::getTotalCount)));
            for (ProductStoreBatchPO tmp : lstTmpBatch) {
                if (totalCount.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }
                // 大于0开始扣
                if (tmp.getTotalCount().compareTo(BigDecimal.ZERO) > 0) {
                    if (totalCount.compareTo(tmp.getTotalCount()) > 0) {
                        changeCount = tmp.getTotalCount();
                    } else {
                        changeCount = totalCount;
                    }
                    totalCount = totalCount.subtract(changeCount);
                    tmp.setTotalCount(tmp.getTotalCount().subtract(changeCount));

                    String oldBatchId = tmp.getId();
                    processBatchChangePO(productStoreBatchPOList, changeCount.multiply(new BigDecimal(-1)), oldBatchId,
                            tmp);
                    // 记录每次变更的数量
                    finalChangeCount = finalChangeCount.add(changeCount);
                }
            }
            // 负库存需要变更的数量
            if (finalChangeCount.compareTo(BigDecimal.ZERO) > 0) {
                processBatchChangePO(productStoreBatchPOList, finalChangeCount, po.getId(), po);
            }
        });

        // 只返回处理逻辑数据，不会执行实际操作
        if (Objects.equals(batchInventorySyncQueryDTO.getReturnLogic(), true)) {
            LOGGER.info(String.format("清理负货位库存-只返回处理逻辑[%s]", batchInventorySyncQueryDTO.getWarehouseId()));
            BatchInventoryClearNegativeLogicDTO negativeLogicDTO = new BatchInventoryClearNegativeLogicDTO();
            // 负货位库存数据
            negativeLogicDTO.setNegativeBatchInventoryList(convertToInventoryLogicDTOList(syncPOList));
            // 变更的货位库存数据
            negativeLogicDTO.setChangeBatchInventoryList(convertToInventoryLogicDTOList(productStoreBatchPOList));
            return negativeLogicDTO;

        } else {
            // 3、批量新增货位库存
            PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
            pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.清理负库存.name());
            List<List<ProductStoreBatchPO>> lists = splitList(productStoreBatchPOList, 50);
            for (List<ProductStoreBatchPO> list : lists) {
                // 更新货位库存
                batchInventoryProductStoreBatchMapper.updateBatchInventory(list);
                // 新增批次库存变更记录
                productStoreBatchChangeRecordBL.createStoreBatchChangeRecordByPickUp(pickUpChangeRecordDTO, list);
            }
        }

        LOGGER.info("清理负货位库存完成：{}", JSON.toJSONString(productStoreBatchPOList));
        return null;
    }

    private List<BatchInventoryLogicDTO>
    convertToInventoryLogicDTOList(List<ProductStoreBatchPO> productStoreBatchPOS) {
        if (productStoreBatchPOS == null) {
            return null;
        }
        return productStoreBatchPOS.stream().map(p -> {
            BatchInventoryLogicDTO logicDTO = new BatchInventoryLogicDTO();
            BeanUtils.copyProperties(p, logicDTO);
            return logicDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 校正总库存与货位库存
     *
     * @param batchInventorySyncQueryDTO
     */
    public void adjustBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        AssertUtils.notNull(batchInventorySyncQueryDTO, "校正货位库存参数不能为空");
        AssertUtils.notNull(batchInventorySyncQueryDTO.getWarehouseId(), "仓库id不能为空");
        LOGGER.info("校正货位库存：{}", JSON.toJSONString(batchInventorySyncQueryDTO));

        // 1、获取需要处理的负货位库存信息
        List<BatchInventorySyncPO> syncPOList =
                batchInventoryProductStoreBatchMapper.listAdjustBatchInventory(batchInventorySyncQueryDTO);
        if (CollectionUtils.isEmpty(syncPOList)) {
            return;
        }

        List<String> lstStoreIds =
                syncPOList.stream().map(p -> p.getProductStoreId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstStoreIds)) {
            return;
        }
        // 货区有负库存的所有货位库存
        List<ProductStoreBatchPO> batchListByProductStoreIds = pageListProductStoreBatch(lstStoreIds);
        if (CollectionUtils.isEmpty(batchListByProductStoreIds)) {
            return;
        }
        // 2、封装货位库存记录PO
        List<ProductStoreBatchPO> productStoreBatchPOList = new ArrayList<>();
        final List<Integer> regulationOrder = Arrays.asList(0, 24, 52, 51, 1, 50, 20, 55);
        syncPOList.forEach(po -> {
            BigDecimal diffCount = po.getDiffCount();
            List<ProductStoreBatchPO> lstTmpBatch = batchListByProductStoreIds.stream()
                    .filter(p -> Objects.equals(p.getProductStoreId(), po.getProductStoreId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lstTmpBatch)) {
                return;
            }
            // //如果存在相等的，正负优先抵消
            // if (lstTmpBatch.stream().anyMatch(p -> p.getTotalCount().add(diffCount).compareTo(BigDecimal.ZERO) == 0))
            // {
            // ProductStoreBatchPO productStoreBatchPO = lstTmpBatch.stream().filter(p ->
            // p.getTotalCount().add(diffCount).compareTo(BigDecimal.ZERO) == 0).findAny().get();
            // LOGGER.info(String.format("货位库存校正-差异相抵，Diff:%s,Total:%s", diffCount,
            // JSON.toJSONString(productStoreBatchPO)));
            // processBatchChangePO(productStoreBatchPOList, diffCount, productStoreBatchPO.getId());
            // productStoreBatchPO.setTotalCount(productStoreBatchPO.getTotalCount().add(diffCount));
            // return;
            // }
            ProductStoreBatchPO productStoreBatchPO = null;
            for (Integer locationCategory : regulationOrder) {
                List<ProductStoreBatchPO> lstTmpLocationCategorys = lstTmpBatch.stream()
                        .filter(p -> Objects.equals(locationCategory, p.getSubcategory())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(lstTmpLocationCategorys)) {
                    continue;
                }
                if (lstTmpLocationCategorys.stream().anyMatch(p -> p.getTotalCount().compareTo(BigDecimal.ZERO) != 0)) {
                    lstTmpLocationCategorys = lstTmpLocationCategorys.stream()
                            .filter(p -> p.getTotalCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
                }
                Collections.sort(lstTmpLocationCategorys, Comparator.comparing(ProductStoreBatchPO::getTotalCount));
                productStoreBatchPO = lstTmpLocationCategorys.get(0);
                break;
            }
            // 如果没有满足条件的货位，取任意一个批次库存
            if (productStoreBatchPO == null && CollectionUtils.isNotEmpty(lstTmpBatch)) {
                productStoreBatchPO = lstTmpBatch.get(0);
            }
            if (productStoreBatchPO != null) {
                processBatchChangePO(productStoreBatchPOList, diffCount, productStoreBatchPO.getId(), null);
                productStoreBatchPO.setTotalCount(productStoreBatchPO.getTotalCount().add(diffCount));
            }
        });

        // 3、批量新增货位库存
        PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
        // pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.货位库存校正.name());
        pickUpChangeRecordDTO.setDescription("货位库存校正");
        List<List<ProductStoreBatchPO>> lists = splitList(productStoreBatchPOList, 50);
        for (List<ProductStoreBatchPO> list : lists) {
            // 更新货位库存
            batchInventoryProductStoreBatchMapper.updateBatchInventory(list);
            // 新增批次库存变更记录
            productStoreBatchChangeRecordBL.createStoreBatchChangeRecordByPickUp(pickUpChangeRecordDTO, list);
        }
    }

    /**
     * 转移指定货位或者货区的批次库存
     */
    public void transferBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        AssertUtils.notNull(batchInventorySyncQueryDTO, "转移货位库存参数不能为空");
        AssertUtils.notNull(batchInventorySyncQueryDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(batchInventorySyncQueryDTO.getLocationAreaList(), "必须指定货位或者货区！");
        LOGGER.info("转移货位库存：{}", JSON.toJSONString(batchInventorySyncQueryDTO));

        // 当前只存在一条货位库存时，转移到关联货位上
        batchInventoryTransferByLocationArea(batchInventorySyncQueryDTO);

        // 1、获取需要处理的负货位库存信息
        Long dtStart = System.currentTimeMillis();
        List<BatchInventorySyncPO> syncPOList =
                batchInventoryProductStoreBatchMapper.listTransferBatchInventory(batchInventorySyncQueryDTO);
        if (CollectionUtils.isEmpty(syncPOList)) {
            return;
        }

        List<String> lstStoreIds =
                syncPOList.stream().map(p -> p.getProductStoreId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstStoreIds)) {
            return;
        }
        // 根据库存Id查找所有货位库存
        List<ProductStoreBatchPO> batchListByProductStoreIds = pageListProductStoreBatch(lstStoreIds);
        if (CollectionUtils.isEmpty(batchListByProductStoreIds)) {
            return;
        }
        // 2、封装货位库存记录PO
        List<ProductStoreBatchPO> productStoreBatchPOList = new ArrayList<>();
        final List<Integer> regulationOrder = Arrays.asList(0, 24, 52, 51, 1, 50, 20, 55);
        syncPOList.forEach(po -> {
            BigDecimal transferTotalCount = po.getTotalCount().multiply(new BigDecimal(-1));
            BigDecimal changeCount = BigDecimal.ZERO;
            BigDecimal finalChangeCount = BigDecimal.ZERO;
            List<ProductStoreBatchPO> lstTmpBatch = batchListByProductStoreIds.stream()
                    .filter(p -> Objects.equals(p.getProductStoreId(), po.getProductStoreId())
                            && !Objects.equals(p.getId(), po.getProductStoreBatchId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lstTmpBatch)) {
                return;
            }
            Collections.sort(lstTmpBatch, (o1, o2) -> {
                int io1 = regulationOrder.indexOf(o1.getSubcategory());
                int io2 = regulationOrder.indexOf(o2.getSubcategory());
                return (io1 == -1 || io2 == -1) ? (io2 - io1) : (io1 - io2);
            });
            // Collections.sort(lstTmpBatch,
            // Comparator.nullsFirst(Comparator.comparing(ProductStoreBatchPO::getTotalCount)));
            for (ProductStoreBatchPO tmp : lstTmpBatch) {
                if (transferTotalCount.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                // 当前批次库存的总数量
                BigDecimal batchCount = tmp.getTotalCount();
                // totalCount大于0,POCount大于0
                // totalCount大于0,POCount小于0
                if (transferTotalCount.compareTo(batchCount) > 0) {
                    // 如果原库存小于0，本次新增库存，则优先补到0，剩余的轮训其他负库存货位
                    if (transferTotalCount.compareTo(BigDecimal.ZERO) < 0
                            && batchCount.compareTo(BigDecimal.ZERO) < 0) {
                        changeCount = (transferTotalCount.abs().min(batchCount.abs())).multiply(new BigDecimal(-1));
                    } else {
                        changeCount = batchCount;
                    }
                } else {
                    changeCount = transferTotalCount;
                }
                transferTotalCount = transferTotalCount.subtract(changeCount);
                tmp.setTotalCount(batchCount.subtract(changeCount));

                String oldBatchId = tmp.getId();
                processBatchChangePO(productStoreBatchPOList, changeCount.multiply(new BigDecimal(-1)), oldBatchId,
                        null);
                // 记录每次变更的数量
                finalChangeCount = finalChangeCount.add(changeCount);
            }
            // 负库存需要变更的数量
            if (finalChangeCount.compareTo(BigDecimal.ZERO) != 0) {
                processBatchChangePO(productStoreBatchPOList, finalChangeCount, po.getProductStoreBatchId(), null);
            }
        });

        // 3、批量新增货位库存
        PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
        pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.货区库存转移.name());

        List<List<ProductStoreBatchPO>> lists = splitList(productStoreBatchPOList, 50);
        for (List<ProductStoreBatchPO> list : lists) {
            LOGGER.info(String.format("更新批次库存信息：%s", JSON.toJSONString(list)));
            // 更新货位库存
            batchInventoryProductStoreBatchMapper.updateBatchInventory(list);
            // 新增批次库存变更记录
            productStoreBatchChangeRecordBL.createStoreBatchChangeRecordByPickUp(pickUpChangeRecordDTO, list);
        }
    }

    private void processBatchChangePO(List<ProductStoreBatchPO> productStoreBatchPOList, BigDecimal changeCount,
                                      String batchId, ProductStoreBatchPO oldBatchPO) {
        ProductStoreBatchPO batchPO = new ProductStoreBatchPO();
        if (oldBatchPO != null) {
            BeanUtils.copyProperties(oldBatchPO, batchPO);
        }
        batchPO.setId(batchId);
        batchPO.setTotalCount(BigDecimal.ZERO);
        // 去重，ProductStoreId不允许重复
        if (productStoreBatchPOList.stream().anyMatch(p -> p.getId().equals(batchId))) {
            batchPO = productStoreBatchPOList.stream().filter(p -> p.getId().equals(batchId)).findAny().get();
        }
        batchPO.setTotalCount(batchPO.getTotalCount().add(changeCount));
        productStoreBatchPOList.removeIf(p -> p.getId().equals(batchId));
        productStoreBatchPOList.add(batchPO);
    }

    /**
     * 每次50个
     *
     * @param list
     * @param len
     * @return
     */
    public List<List<ProductStoreBatchPO>> splitList(List<ProductStoreBatchPO> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }

        List<List<ProductStoreBatchPO>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<ProductStoreBatchPO> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    /**
     * 将所选库区中所有产品移动到产品关联货位
     */
    public void batchInventoryTransferByLocationArea(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        LOGGER.info("移动货位库存请求参数:{}", JSON.toJSONString(batchInventorySyncQueryDTO));
        Integer warehouseId = batchInventorySyncQueryDTO.getWarehouseId();
        List<String> locationAreaList = batchInventorySyncQueryDTO.getLocationAreaList();
        Long ownerId = batchInventorySyncQueryDTO.getOwnerId();
        /** begin luo kang 查询仓库是否开通货位组 SCM2-14567 */
        boolean isOpenLocationStockGroup = warehouseConfigService.isOpenLocationGroup(warehouseId);
        // 查询货区所有货位库存信息
        List<ProductStoreBatchPO> productStoreBatchPOS =
                batchInventoryProductStoreBatchMapper.findProductStoreBatchByLocationArea(warehouseId, locationAreaList, ownerId);
        // 查询货区下产品关联货位信息
        if (CollectionUtils.isEmpty(productStoreBatchPOS)) {
            return;
        }
        List<Long> skuIds = productStoreBatchPOS.stream().map(ProductStoreBatchPO::getProductSkuId).distinct()
                .collect(Collectors.toList());
        List<ProductLoactionItemDTO> locationBySkuId = iProductLocationService.findLocationBySkuId(warehouseId, skuIds);
        locationBySkuId = locationBySkuId.stream()
                .filter(location -> !locationAreaList.contains(location.getAreaName())).collect(Collectors.toList());
        LOGGER.info("移动货位库存产品货位关联数据：{}", JSON.toJSONString(locationBySkuId));
        // 组装移库数据
        if (CollectionUtils.isEmpty(locationBySkuId)) {
            return;
        }
        if (isOpenLocationStockGroup) {
            // SCM2-14567 开通货位组，货位Id值替换成货区Id
            List<String> idList =
                    locationBySkuId.stream().map(item -> item.getLocationId() + "").distinct().collect(Collectors.toList());
            List<LocationReturnDTO> locationReturnDTOS =
                    locationAreaService.findLocationAreaListExcludeDefective(idList);
            if (CollectionUtils.isNotEmpty(locationReturnDTOS)) {
                locationBySkuId.forEach(o -> {
                    locationReturnDTOS.stream().filter(m -> Objects.equals(m.getId(), o.getLocationId()))
                            .forEach(m -> o.setLocationId(m.getArea_Id()));
                });
                LOGGER.info("开通货位组->移动货位库存产品货位关联数据变更：{}", JSON.toJSONString(locationBySkuId));
            }
        }
        List<String> productStoreBatchIds = new ArrayList<>();
        List<ProductInventoryChangeRecordDTO> productInventoryChangeRecordDTOS = new ArrayList<>();
        List<ProductInventoryChangeRecordPO> productInventoryChangeRecordPOS = getProductInventoryChangeRecordPOS(
                productStoreBatchPOS, locationBySkuId, productStoreBatchIds, productInventoryChangeRecordDTOS);

        productInventoryChangeRecordPOS = productInventoryChangeRecordPOS.stream()
                .filter(StreamUtils.distinctByKey(ProductInventoryChangeRecordPO::getDistinctKey))
                .collect(Collectors.toList());
        LOGGER.info("移动货位库存组装数据：{}", JSON.toJSONString(productInventoryChangeRecordPOS));

        // 移库操作
        if (CollectionUtils.isEmpty(productInventoryChangeRecordPOS)) {
            return;
        }

        // 增加库存变更日志
        iProductInventoryRecordManagerService.saveProductStoreChangeRecord(productInventoryChangeRecordDTOS);
        productInventoryChangeRecordPOS.forEach(productInventoryChangeRecordPO -> {
            productStoreBatchBL.processLocationInventory(productInventoryChangeRecordPO, null, null, false, null);
        });

        // 删除货区下已经移库数据 -> 保留历史数据
        // productStoreBatchIds = productStoreBatchIds.stream().distinct().collect(Collectors.toList());
        // LOGGER.info("移动货位库存删除数据：{}", JSON.toJSONString(productStoreBatchIds));
        // productStoreBatchMapper.deleteBatchInventoryByIds(productStoreBatchIds);
    }

    /**
     * 组装批次库存转移参数
     */
    private List<ProductInventoryChangeRecordPO> getProductInventoryChangeRecordPOS(
            List<ProductStoreBatchPO> productStoreBatchPOS, List<ProductLoactionItemDTO> locationBySkuId,
            List<String> productStoreBatchIds, List<ProductInventoryChangeRecordDTO> productInventoryChangeRecordDTOS) {
        List<ProductInventoryChangeRecordPO> productInventoryChangeRecordPOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productStoreBatchPOS) && CollectionUtils.isNotEmpty(locationBySkuId)) {
            Map<Long, List<ProductStoreBatchPO>> ProductStoreBatchMap =
                    productStoreBatchPOS.stream().collect(Collectors.groupingBy(ProductStoreBatchPO::getProductSkuId));
            Map<Long, List<ProductLoactionItemDTO>> productLocationMap =
                    locationBySkuId.stream().collect(Collectors.groupingBy(ProductLoactionItemDTO::getProductSkuId));
            productLocationMap.forEach((skuId, productLocations) -> {
                ProductLoactionItemDTO productLocation = productLocations.get(0);
                List<ProductStoreBatchPO> productStoreBatchList =
                        ProductStoreBatchMap.get(productLocation.getProductSkuId());
                productStoreBatchList.forEach(productStoreBatch -> {
                    BigDecimal totalCount = productStoreBatch.getTotalCount();
                    if (totalCount != null && totalCount.compareTo(BigDecimal.ZERO) != 0) {
                        ProductInventoryChangeRecordPO productInventoryChangeRecordPO =
                                new ProductInventoryChangeRecordPO();
                        productInventoryChangeRecordPO.setCityId(productLocation.getCityId());
                        productInventoryChangeRecordPO.setJiupiEventType(JiupiEventType.手动修改.getType());
                        productInventoryChangeRecordPO.setLocationId(productLocation.getLocationId());
                        productInventoryChangeRecordPO.setBatchTime(productStoreBatch.getBatchTime());
                        productInventoryChangeRecordPO.setExpireTime(productStoreBatch.getExpireTime());
                        productInventoryChangeRecordPO.setProductionDate(productStoreBatch.getProductionDate());
                        productInventoryChangeRecordPO.setProductStoreId(productStoreBatch.getProductStoreId());
                        productInventoryChangeRecordPO.setTotalCount(totalCount);
                        productInventoryChangeRecordPO.setCreateTime(new Date());
                        String batchAttributeNo = batchAttributeInfoBL.getProductBatchAttributeInfoNo(
                                productStoreBatch.getProductStoreId(), null, productStoreBatch.getProductionDate(),
                                productStoreBatch.getBatchTime(), new ArrayList<>(), true);
                        productInventoryChangeRecordPO.setBatchAttributeInfoNo(batchAttributeNo);

                        String id = UUIDUtil.getUUID();
                        productInventoryChangeRecordPO.setId(id);

                        productInventoryChangeRecordPO.setDescription("货区库存转移");

                        // 后续去重用
                        productInventoryChangeRecordPO.setDistinctKey(productStoreBatch.getId());

                        // 库存变更记录
                        ProductInventoryChangeRecordDTO productInventoryChangeRecordDTO =
                                new ProductInventoryChangeRecordDTO();
                        productInventoryChangeRecordDTO.setId(id);
                        productInventoryChangeRecordDTO.setProductStoreId("");
                        productInventoryChangeRecordDTO.setCityId(productLocation.getCityId());
                        productInventoryChangeRecordDTO.setJiupiEventType(JiupiEventType.手动修改.getType());
                        productInventoryChangeRecordDTO.setCountMaxUnit(BigDecimal.ZERO);
                        productInventoryChangeRecordDTO.setCountMinUnit(BigDecimal.ZERO);
                        productInventoryChangeRecordDTO.setTotalCount(totalCount);
                        productInventoryChangeRecordDTO.setSourceTotalCount(BigDecimal.ZERO);
                        productInventoryChangeRecordDTO.setDescription(InventoryChangeTypeEnum.货区库存转移.name());
                        productInventoryChangeRecordDTO.setCreateTime(new Date());
                        productInventoryChangeRecordDTO.setStoreType(InventoryChangeTypes.WAREHOUSE);

                        productInventoryChangeRecordPOS.add(productInventoryChangeRecordPO);
                        productInventoryChangeRecordDTOS.add(productInventoryChangeRecordDTO);
                    }
                    // 后续删除用
                    productStoreBatchIds.add(productStoreBatch.getId());
                });

            });
        }
        return productInventoryChangeRecordPOS;
    }

    /**
     * 残次品货位库存校正
     */
    public void adjustDisposedProductLocationInventory(BatchInventoryAdjustDTO batchInventoryAdjustDTO) {
        if (!warehouseConfigService.isOpenLocationStock(batchInventoryAdjustDTO.getWarehouseId())) {
            throw new BusinessValidateException("请检查该仓库是否开启货位库存");
        }
        // 查出erp所有的残次品、陈列品库存
        List<ERPDisposedInventoryDTO> erpDisposedInventories = getERPDisposedProductInventoryList(
                batchInventoryAdjustDTO.getOrgId(), batchInventoryAdjustDTO.getWarehouseId());

        // 查出仓库所有残次品、陈列品库存
        List<BatchInventoryPO> batchDisposedInventories =
                getWMSDisposedProductInventoryList(batchInventoryAdjustDTO.getWarehouseId());

        // 组装数据
        List<OutStockOrderDTO> outStockOrderDTOS = new ArrayList<>();
        List<InStockOrderDTO> inStockOrderDTOS = new ArrayList<>();
        Boolean basedOnErp =
                batchInventoryAdjustDTO.getBasedOnErp() == null ? false : batchInventoryAdjustDTO.getBasedOnErp();

        if (basedOnErp) {
            buildDisposedBillBasedOnERP(batchDisposedInventories, erpDisposedInventories, outStockOrderDTOS,
                    inStockOrderDTOS, batchInventoryAdjustDTO.getOrgId(), batchInventoryAdjustDTO.getWarehouseId());
        } else {
            buildDisposedBill(batchDisposedInventories, erpDisposedInventories, outStockOrderDTOS, inStockOrderDTOS,
                    batchInventoryAdjustDTO.getOrgId(), batchInventoryAdjustDTO.getWarehouseId());
        }

        // 建单据，扣库存，发消息
        outStockOrderDTOS = outStockOrderDTOS.stream()
                .filter(outStockOrder -> CollectionUtils.isNotEmpty(outStockOrder.getOutStockOrderItemDTOS()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outStockOrderDTOS)) {
            ProcessOutStockOrderCommDTO processOutStockOrderCommDTO = new ProcessOutStockOrderCommDTO();
            processOutStockOrderCommDTO.setNeedSendMsg(!basedOnErp);
            processOutStockOrderCommDTO.setNeedToChangeBatchStore(basedOnErp);
            processOutStockOrderCommDTO.setNeedToChangeStore(basedOnErp);
            processOutStockOrderCommDTO.setOutStockOrderDTOS(outStockOrderDTOS);
            LOGGER.info("处理品校正出库数据:{}", JSON.toJSONString(processOutStockOrderCommDTO));
            iOutStockCommManageService.batchSaveOutStockOrder(processOutStockOrderCommDTO);
        }

        inStockOrderDTOS = inStockOrderDTOS.stream()
                .filter(inStockOrder -> CollectionUtils.isNotEmpty(inStockOrder.getInStockOrderItemDTOList()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(inStockOrderDTOS)) {
            InStockCommOrderAddDTO inStockCommOrderAddDTO = new InStockCommOrderAddDTO();
            inStockCommOrderAddDTO.setOrgId(batchInventoryAdjustDTO.getOrgId());
            inStockCommOrderAddDTO.setWarehouseId(batchInventoryAdjustDTO.getWarehouseId());
            inStockCommOrderAddDTO.setNeedSendMsgToErp(!basedOnErp);
            inStockCommOrderAddDTO.setNeedToChangeBatchStore(basedOnErp);
            inStockCommOrderAddDTO.setNeedToChangeStore(basedOnErp);
            inStockCommOrderAddDTO.setNeedToCreatePutAwayTask(false);
            inStockCommOrderAddDTO.setNeedSetERPEventType(false);
            inStockCommOrderAddDTO.setInStockOrderDTOList(inStockOrderDTOS);
            LOGGER.info("处理品校正入库数据:{}", JSON.toJSONString(inStockCommOrderAddDTO));
            iInStockCommService.insertInStockCommOrder(inStockCommOrderAddDTO);
        }
    }

    /**
     * YJP为主发消息
     */
    private void buildDisposedBill(List<BatchInventoryPO> batchDisposedInventories,
                                   List<ERPDisposedInventoryDTO> erpDisposedInventories, List<OutStockOrderDTO> outStockOrderDTOS,
                                   List<InStockOrderDTO> inStockOrderDTOS, Integer orgId, Integer warehouseId) {
        if (CollectionUtils.isEmpty(batchDisposedInventories) && CollectionUtils.isEmpty(erpDisposedInventories)) {
            return;
        }
        Map<Long, List<BatchInventoryPO>> disposedInventoryMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(batchDisposedInventories)) {
            disposedInventoryMap =
                    batchDisposedInventories.stream().collect(Collectors.groupingBy(BatchInventoryPO::getProductSkuId));
        }

        // 准备数据
        OutStockOrderDTO defectiveOutStockOrderDTO =
                setOutStockOrderDTO(orgId, warehouseId, OutStockOrderTypeEnum.处理品转入.getType());
        List<OutStockOrderItemDTO> defectiveOutStockOrderItems = new ArrayList<>();

        OutStockOrderDTO exhibitOutStockOrderDTO =
                setOutStockOrderDTO(orgId, warehouseId, OutStockOrderTypeEnum.陈列品转入.getType());
        List<OutStockOrderItemDTO> exhibitOutStockOrderItems = new ArrayList<>();

        InStockOrderDTO defectiveInStockOrderDTO =
                setInStockOrderDTO(orgId, warehouseId, InStockOrderTypeEnum.处理品转出入库.getType());
        List<InStockOrderItemDTO> defectiveInStockOrderItems = new ArrayList<>();

        InStockOrderDTO exhibitInStockOrderDTO =
                setInStockOrderDTO(orgId, warehouseId, InStockOrderTypeEnum.陈列品转出入库.getType());
        List<InStockOrderItemDTO> exhibitInStockOrderItems = new ArrayList<>();

        List<String> skuIds = new ArrayList<>();
        for (Map.Entry<Long, List<BatchInventoryPO>> entry : disposedInventoryMap.entrySet()) {
            Long skuId = entry.getKey();
            skuIds.add(skuId.toString());
            List<BatchInventoryPO> batchInventories = entry.getValue();

            BigDecimal defectiveStoreCount = batchInventories.stream()
                    .filter(inventory -> LocationAreaEnum.残次品区.getType().equals(inventory.getLocationSubcategory())
                            || LocationEnum.残次品位.getType().equals(inventory.getLocationSubcategory()))
                    .map(BatchInventoryPO::getStoreTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal erpDefectiveStoreCount = erpDisposedInventories.stream()
                    .filter(erp -> erp.getProductSkuId().equals(skuId.toString())
                            && ERPDisposedInventoryType.残次品.getType().equals(erp.getDisposedType()))
                    .map(ERPDisposedInventoryDTO::getStockCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal exhibitStoreCount = batchInventories.stream()
                    .filter(inventory -> LocationAreaEnum.陈列品区.getType().equals(inventory.getLocationSubcategory())
                            || LocationEnum.陈列品位.getType().equals(inventory.getLocationSubcategory()))
                    .map(BatchInventoryPO::getStoreTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal erpExhibitStoreCount = erpDisposedInventories.stream()
                    .filter(erp -> erp.getProductSkuId().equals(skuId.toString())
                            && ERPDisposedInventoryType.陈列品.getType().equals(erp.getDisposedType()))
                    .map(ERPDisposedInventoryDTO::getStockCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal defectiveInventoryCount = defectiveStoreCount.subtract(erpDefectiveStoreCount);
            BigDecimal exhibitInventoryCount = exhibitStoreCount.subtract(erpExhibitStoreCount);

            // 当yjp数量比erp多或erp没有时，组装残次品入消息
            if (defectiveInventoryCount.compareTo(BigDecimal.ZERO) > 0) {
                defectiveInStockOrderItems
                        .add(setInStockOrderItemDTO(orgId, skuId, defectiveInventoryCount, null, null));
            } else if (defectiveInventoryCount.compareTo(BigDecimal.ZERO) < 0) {
                // 当yjp数量比erp多或erp没有时，组装残次品出消息
                defectiveOutStockOrderItems
                        .add(setOutStockOrderItemDTO(orgId, skuId, defectiveInventoryCount.abs(), null, null));
            }

            // 同残次品处理逻辑
            if (exhibitInventoryCount.compareTo(BigDecimal.ZERO) > 0) {
                exhibitInStockOrderItems.add(setInStockOrderItemDTO(orgId, skuId, exhibitInventoryCount, null, null));
            } else if (exhibitInventoryCount.compareTo(BigDecimal.ZERO) < 0) {
                exhibitOutStockOrderItems
                        .add(setOutStockOrderItemDTO(orgId, skuId, exhibitInventoryCount.abs(), null, null));
            }
        }

        // erp存在而yjp不存在的则需要生成出入库单据
        erpDisposedInventories = erpDisposedInventories.stream().filter(erp -> !skuIds.contains(erp.getProductSkuId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(erpDisposedInventories)) {
            LOGGER.info("ERP存在而WMS不存在的处理品数据:{}", JSON.toJSONString(erpDisposedInventories));
            erpDisposedInventories.forEach(erp -> {
                BigDecimal stockCount = erp.getStockCount();
                if (ERPDisposedInventoryType.残次品.getType().equals(erp.getDisposedType())
                        && stockCount.compareTo(BigDecimal.ZERO) > 0) {
                    defectiveOutStockOrderItems.add(
                            setOutStockOrderItemDTO(orgId, Long.valueOf(erp.getProductSkuId()), stockCount, null, null));
                } else if (ERPDisposedInventoryType.陈列品.getType().equals(erp.getDisposedType())
                        && stockCount.compareTo(BigDecimal.ZERO) > 0) {
                    exhibitOutStockOrderItems.add(
                            setOutStockOrderItemDTO(orgId, Long.valueOf(erp.getProductSkuId()), stockCount, null, null));
                } else if (ERPDisposedInventoryType.残次品.getType().equals(erp.getDisposedType())
                        && stockCount.compareTo(BigDecimal.ZERO) < 0) {
                    defectiveInStockOrderItems.add(setInStockOrderItemDTO(orgId, Long.valueOf(erp.getProductSkuId()),
                            stockCount.abs(), null, null));
                } else if (ERPDisposedInventoryType.陈列品.getType().equals(erp.getDisposedType())
                        && stockCount.compareTo(BigDecimal.ZERO) < 0) {
                    exhibitInStockOrderItems.add(setInStockOrderItemDTO(orgId, Long.valueOf(erp.getProductSkuId()),
                            stockCount.abs(), null, null));
                }
            });
        }

        if (CollectionUtils.isNotEmpty(defectiveOutStockOrderItems)) {
            defectiveOutStockOrderDTO.setOutStockOrderItemDTOS(defectiveOutStockOrderItems);
            outStockOrderDTOS.add(defectiveOutStockOrderDTO);
        }
        if (CollectionUtils.isNotEmpty(exhibitOutStockOrderItems)) {
            exhibitOutStockOrderDTO.setOutStockOrderItemDTOS(exhibitOutStockOrderItems);
            outStockOrderDTOS.add(exhibitOutStockOrderDTO);
        }

        if (CollectionUtils.isNotEmpty(defectiveInStockOrderItems)) {
            defectiveInStockOrderDTO.setInStockOrderItemDTOList(defectiveInStockOrderItems);
            inStockOrderDTOS.add(defectiveInStockOrderDTO);
        }
        if (CollectionUtils.isNotEmpty(exhibitInStockOrderItems)) {
            exhibitInStockOrderDTO.setInStockOrderItemDTOList(exhibitInStockOrderItems);
            inStockOrderDTOS.add(exhibitInStockOrderDTO);
        }
    }

    /**
     * ERP为主生处理单据
     */
    private void buildDisposedBillBasedOnERP(List<BatchInventoryPO> batchDisposedInventories,
                                             List<ERPDisposedInventoryDTO> erpDisposedInventories, List<OutStockOrderDTO> outStockOrderDTOS,
                                             List<InStockOrderDTO> inStockOrderDTOS, Integer orgId, Integer warehouseId) {
        if (CollectionUtils.isEmpty(batchDisposedInventories) && CollectionUtils.isEmpty(erpDisposedInventories)) {
            return;
        }
        Map<String, List<ERPDisposedInventoryDTO>> erpDisposedInventoryMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(erpDisposedInventories)) {
            erpDisposedInventoryMap = erpDisposedInventories.stream()
                    .collect(Collectors.groupingBy(ERPDisposedInventoryDTO::getProductSkuId));
        }
        // 查出可以用来转移的货位库存
        List<Long> existSkuIds =
                batchDisposedInventories.stream().map(BatchInventoryPO::getProductSkuId).collect(Collectors.toList());
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setSkuIds(existSkuIds);
        batchInventoryQueryDTO.setWarehouseId(warehouseId);
        List<BatchInventoryDTO> batchStoreBySkuIds =
                batchInventoryQueryBL.findBatchInventoryList(batchInventoryQueryDTO).getDataList();
        Map<Long, List<BatchInventoryDTO>> batchInventoryMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(batchStoreBySkuIds)) {
            batchInventoryMap = batchStoreBySkuIds.stream().filter(productInventory -> {
                if (productInventory.getStoreTotalCount().compareTo(BigDecimal.ZERO) <= 0
                        || LocationEnum.残次品位.getType().equals(productInventory.getLocationSubcategory())
                        || LocationEnum.陈列品位.getType().equals(productInventory.getLocationSubcategory())
                        || LocationAreaEnum.残次品区.getType().equals(productInventory.getLocationSubcategory())
                        || LocationAreaEnum.陈列品区.getType().equals(productInventory.getLocationSubcategory())) {
                    return false;
                }
                if (LocationEnum.零拣位.getType().equals(productInventory.getLocationSubcategory())) {
                    productInventory.setLocationSequence(1);
                } else if (LocationAreaEnum.零拣区.getType().equals(productInventory.getLocationSubcategory())) {
                    productInventory.setLocationSequence(2);
                } else if (LocationEnum.分拣位.getType().equals(productInventory.getLocationSubcategory())) {
                    productInventory.setLocationSequence(3);
                } else if (LocationAreaEnum.拣货区.getType().equals(productInventory.getLocationSubcategory())) {
                    productInventory.setLocationSequence(4);
                } else {
                    productInventory.setLocationSequence(5);
                }
                return true;
            }).collect(Collectors.groupingBy(BatchInventoryDTO::getProductSkuId));
            LOGGER.info("校正产品货位库存:{}", JSON.toJSONString(batchInventoryMap));
        }

        // 查出产品关联货位
        List<Long> skuIds = erpDisposedInventories.stream().map(erp -> Long.valueOf(erp.getProductSkuId())).distinct()
                .collect(Collectors.toList());
        existSkuIds.addAll(skuIds);
        Map<Long, List<LoactionDTO>> productLocationMap = iProductLocationService.findLocationDTOBySkuId(warehouseId,
                existSkuIds.stream().distinct().collect(Collectors.toList()));

        // 查出残次品，陈列品货位
        List<Byte> subCategoryList = new ArrayList<>();
        subCategoryList.add(LocationAreaEnum.残次品区.getType().byteValue());
        subCategoryList.add(LocationAreaEnum.陈列品区.getType().byteValue());
        subCategoryList.add(LocationEnum.残次品位.getType().byteValue());
        subCategoryList.add(LocationEnum.陈列品位.getType().byteValue());
        LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
        locationQueryDTO.setSubcategoryList(subCategoryList);
        locationQueryDTO.setWarehouseId(warehouseId);
        locationQueryDTO.setCityId(orgId);
        List<LoactionDTO> disposedLocations =
                iProductLocationService.findProductLocationPageList(locationQueryDTO).getDataList();
        List<LoactionDTO> defectiveLocations =
                disposedLocations.stream()
                        .filter(location -> LocationAreaEnum.残次品区.getType().byteValue() == location.getSubcategory()
                                || LocationEnum.残次品位.getType().byteValue() == location.getSubcategory())
                        .collect(Collectors.toList());
        List<LoactionDTO> exhibitLocations =
                disposedLocations.stream()
                        .filter(location -> LocationAreaEnum.陈列品区.getType().byteValue() == location.getSubcategory()
                                || LocationEnum.陈列品位.getType().byteValue() == location.getSubcategory())
                        .collect(Collectors.toList());
        LoactionDTO defectiveLocation =
                CollectionUtils.isNotEmpty(defectiveLocations) ? defectiveLocations.get(0) : null;
        LoactionDTO exhibitLocation = CollectionUtils.isNotEmpty(exhibitLocations) ? exhibitLocations.get(0) : null;

        // 准备数据
        OutStockOrderDTO defectiveOutStockOrderDTO =
                setOutStockOrderDTO(orgId, warehouseId, OutStockOrderTypeEnum.处理品转入.getType());
        List<OutStockOrderItemDTO> defectiveOutStockOrderItems = new ArrayList<>();

        OutStockOrderDTO exhibitOutStockOrderDTO =
                setOutStockOrderDTO(orgId, warehouseId, OutStockOrderTypeEnum.陈列品转入.getType());
        List<OutStockOrderItemDTO> exhibitOutStockOrderItems = new ArrayList<>();

        InStockOrderDTO defectiveInStockOrderDTO =
                setInStockOrderDTO(orgId, warehouseId, InStockOrderTypeEnum.处理品转出入库.getType());
        List<InStockOrderItemDTO> defectiveInStockOrderItems = new ArrayList<>();

        InStockOrderDTO exhibitInStockOrderDTO =
                setInStockOrderDTO(orgId, warehouseId, InStockOrderTypeEnum.陈列品转出入库.getType());
        List<InStockOrderItemDTO> exhibitInStockOrderItems = new ArrayList<>();

        // 以erp为主进行循环组装数据，残次品和陈列品单独处理
        for (Map.Entry<String, List<ERPDisposedInventoryDTO>> entry : erpDisposedInventoryMap.entrySet()) {
            Long skuId = Long.valueOf(entry.getKey());
            // 查出产品关联货位
            LoactionDTO productLocation =
                    CollectionUtils.isNotEmpty(productLocationMap.get(skuId)) ? productLocationMap.get(skuId).get(0) : null;

            // 查出相应skuId相关的残次品、陈列品、货位库存数据
            List<ERPDisposedInventoryDTO> erpDefectiveInventories = entry.getValue().stream()
                    .filter(erp -> ERPDisposedInventoryType.残次品.getType().equals(erp.getDisposedType()))
                    .collect(Collectors.toList());
            List<ERPDisposedInventoryDTO> erpExhibitInventories = entry.getValue().stream()
                    .filter(erp -> ERPDisposedInventoryType.陈列品.getType().equals(erp.getDisposedType()))
                    .collect(Collectors.toList());
            List<BatchInventoryPO> defectiveInventories = batchDisposedInventories.stream()
                    .filter(inventory -> inventory.getProductSkuId().equals(skuId)
                            && (LocationAreaEnum.残次品区.getType().equals(inventory.getLocationSubcategory())
                            || LocationEnum.残次品位.getType().equals(inventory.getLocationSubcategory())))
                    .collect(Collectors.toList());
            List<BatchInventoryPO> exhibitInventories = batchDisposedInventories.stream()
                    .filter(inventory -> inventory.getProductSkuId().equals(skuId)
                            && (LocationAreaEnum.陈列品区.getType().equals(inventory.getLocationSubcategory())
                            || LocationEnum.陈列品位.getType().equals(inventory.getLocationSubcategory())))
                    .collect(Collectors.toList());
            List<BatchInventoryDTO> batchInventories =
                    batchInventoryMap.get(skuId) == null ? new ArrayList<>() : batchInventoryMap.get(skuId);
            // 查出残次品、陈列品货位
            if (CollectionUtils.isNotEmpty(defectiveInventories)) {
                defectiveLocation = new LoactionDTO();
                defectiveLocation.setId(defectiveInventories.get(0).getLocationId());
                defectiveLocation.setName(defectiveInventories.get(0).getLocationName());
            }
            if (CollectionUtils.isNotEmpty(exhibitInventories)) {
                exhibitLocation = new LoactionDTO();
                exhibitLocation.setId(exhibitInventories.get(0).getLocationId());
                exhibitLocation.setName(exhibitInventories.get(0).getLocationName());
            }

            BigDecimal erpDefectiveStoreCount = erpDefectiveInventories.stream()
                    .map(ERPDisposedInventoryDTO::getStockCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal erpExhibitStoreCount = erpExhibitInventories.stream().map(ERPDisposedInventoryDTO::getStockCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal defectiveStoreCount = defectiveInventories.stream().map(BatchInventoryPO::getStoreTotalCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal exhibitStoreCount = exhibitInventories.stream().map(BatchInventoryPO::getStoreTotalCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal defectiveInventoryCount = erpDefectiveStoreCount.subtract(defectiveStoreCount);
            BigDecimal exhibitInventoryCount = erpExhibitStoreCount.subtract(exhibitStoreCount);

            if (defectiveInventoryCount.compareTo(BigDecimal.ZERO) > 0 && defectiveLocation != null) {
                // 当erp数量比yjp多或yjp没有、并且货位库存足够时，生成普通货位出库，残次品货位入库
                calculateStockOrderItemByInventory(skuId, batchInventories, defectiveOutStockOrderItems,
                        defectiveInStockOrderItems, defectiveInventoryCount, orgId, defectiveLocation, null);
            } else if (defectiveInventoryCount.compareTo(BigDecimal.ZERO) < 0) {
                // 当erp数量比yjp少，生成残次品货位出库，产品关联货位入库
                if (productLocation != null && defectiveLocation != null) {
                    defectiveOutStockOrderItems.add(
                            setOutStockOrderItemDTO(orgId, skuId, defectiveInventoryCount.abs(), defectiveLocation, null));
                    defectiveInStockOrderItems.add(
                            setInStockOrderItemDTO(orgId, skuId, defectiveInventoryCount.abs(), productLocation, null));
                } else {
                    LOGGER.warn("残次品校正时未找到对应产品货位,skuId:{}", skuId);
                }
            }

            // 陈列品处理逻辑基本同残次品
            if (exhibitInventoryCount.compareTo(BigDecimal.ZERO) > 0) {
                calculateStockOrderItemByInventory(skuId, batchInventories, exhibitOutStockOrderItems,
                        exhibitInStockOrderItems, exhibitInventoryCount, orgId, exhibitLocation, null);
            } else if (exhibitInventoryCount.compareTo(BigDecimal.ZERO) < 0) {
                if (productLocation != null && exhibitLocation != null) {
                    exhibitOutStockOrderItems
                            .add(setOutStockOrderItemDTO(orgId, skuId, exhibitInventoryCount.abs(), exhibitLocation, null));
                    exhibitInStockOrderItems
                            .add(setInStockOrderItemDTO(orgId, skuId, exhibitInventoryCount.abs(), productLocation, null));
                } else {
                    LOGGER.warn("陈列品校正时未找到对应产品货位,skuId:{}", skuId);
                }
            }
        }

        // yjp存在而erp不存在的数据,处理逻辑基本同上
        batchDisposedInventories = batchDisposedInventories.stream()
                .filter(inventory -> !skuIds.contains(inventory.getProductSkuId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(batchDisposedInventories)) {
            LOGGER.info("WMS存在而ERP不存在的处理品数据:{}", JSON.toJSONString(batchDisposedInventories));
            for (BatchInventoryPO inventory : batchDisposedInventories) {
                Long skuId = inventory.getProductSkuId();
                BigDecimal storeTotalCount = inventory.getStoreTotalCount();
                LoactionDTO productLocation = CollectionUtils.isNotEmpty(productLocationMap.get(skuId))
                        ? productLocationMap.get(skuId).get(0) : null;
                LoactionDTO disposedLocation = new LoactionDTO();
                disposedLocation.setId(inventory.getLocationId());
                disposedLocation.setName(inventory.getLocationName());
                boolean isCanCiPin = LocationAreaEnum.残次品区.getType().equals(inventory.getLocationSubcategory())
                        || LocationEnum.残次品位.getType().equals(inventory.getLocationSubcategory());
                boolean isChenLie = LocationAreaEnum.陈列品区.getType().equals(inventory.getLocationSubcategory())
                        || LocationEnum.陈列品位.getType().equals(inventory.getLocationSubcategory());
                if (isCanCiPin && storeTotalCount.compareTo(BigDecimal.ZERO) > 0) {
                    if (productLocation != null) {
                        defectiveOutStockOrderItems.add(setOutStockOrderItemDTO(orgId, skuId, storeTotalCount,
                                disposedLocation, inventory.getBatchTime()));

                        defectiveInStockOrderItems.add(setInStockOrderItemDTO(orgId, skuId, storeTotalCount,
                                productLocation, inventory.getBatchTime()));
                    } else {
                        LOGGER.warn("残次品校正时未找到对应产品货位,skuId:{}", skuId);
                    }
                } else if (isChenLie && storeTotalCount.compareTo(BigDecimal.ZERO) > 0) {
                    if (productLocation != null) {
                        exhibitOutStockOrderItems.add(setOutStockOrderItemDTO(orgId, skuId, storeTotalCount.abs(),
                                disposedLocation, inventory.getBatchTime()));

                        exhibitInStockOrderItems.add(setInStockOrderItemDTO(orgId, skuId, storeTotalCount.abs(),
                                productLocation, inventory.getBatchTime()));
                    } else {
                        LOGGER.warn("陈列品校正时未找到对应产品货位,skuId:{}", skuId);
                    }
                } else if (isCanCiPin && storeTotalCount.compareTo(BigDecimal.ZERO) < 0) {
                    storeTotalCount = storeTotalCount.abs();
                    List<BatchInventoryDTO> batchInventories = batchInventoryMap.get(skuId);
                    calculateStockOrderItemByInventory(skuId, batchInventories, defectiveOutStockOrderItems,
                            defectiveInStockOrderItems, storeTotalCount, orgId, disposedLocation, inventory.getBatchTime());
                } else if (isChenLie && storeTotalCount.compareTo(BigDecimal.ZERO) < 0) {
                    storeTotalCount = storeTotalCount.abs();
                    List<BatchInventoryDTO> batchInventories = batchInventoryMap.get(skuId);
                    calculateStockOrderItemByInventory(skuId, batchInventories, exhibitOutStockOrderItems,
                            exhibitInStockOrderItems, storeTotalCount, orgId, disposedLocation, inventory.getBatchTime());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(defectiveOutStockOrderItems)) {
            defectiveOutStockOrderDTO.setOutStockOrderItemDTOS(defectiveOutStockOrderItems);
            outStockOrderDTOS.add(defectiveOutStockOrderDTO);
        }
        if (CollectionUtils.isNotEmpty(exhibitOutStockOrderItems)) {
            exhibitOutStockOrderDTO.setOutStockOrderItemDTOS(exhibitOutStockOrderItems);
            outStockOrderDTOS.add(exhibitOutStockOrderDTO);
        }

        if (CollectionUtils.isNotEmpty(defectiveInStockOrderItems)) {
            defectiveInStockOrderDTO.setInStockOrderItemDTOList(defectiveInStockOrderItems);
            inStockOrderDTOS.add(defectiveInStockOrderDTO);
        }
        if (CollectionUtils.isNotEmpty(exhibitInStockOrderItems)) {
            exhibitInStockOrderDTO.setInStockOrderItemDTOList(exhibitInStockOrderItems);
            inStockOrderDTOS.add(exhibitInStockOrderDTO);
        }
    }

    private InStockOrderDTO setInStockOrderDTO(Integer orgId, Integer warehouseId, Byte inStockOrderType) {
        InStockOrderDTO inStockOrderDTO = new InStockOrderDTO();
        inStockOrderDTO.setOrgId(orgId);
        inStockOrderDTO.setFromCityId(orgId);
        inStockOrderDTO.setWarehouseId(warehouseId);
        inStockOrderDTO.setOrderType(inStockOrderType);
        inStockOrderDTO.setRemark("处理品校正");

        return inStockOrderDTO;
    }

    private OutStockOrderDTO setOutStockOrderDTO(Integer orgId, Integer warehouseId, Byte outStockOrderType) {
        OutStockOrderDTO outStockOrderDTO = new OutStockOrderDTO();
        outStockOrderDTO.setOrgId(orgId);
        outStockOrderDTO.setFromCityId(orgId);
        outStockOrderDTO.setWarehouseId(warehouseId);
        outStockOrderDTO.setOrderType(outStockOrderType);
        outStockOrderDTO.setRemark("处理品校正");

        return outStockOrderDTO;
    }

    private InStockOrderItemDTO setInStockOrderItemDTO(Integer orgId, Long skuId, BigDecimal totalCount,
                                                       LoactionDTO locationDTO, Date batchTime) {
        InStockOrderItemDTO inStockOrderItemDTO = new InStockOrderItemDTO();
        inStockOrderItemDTO.setOrgId(orgId);
        inStockOrderItemDTO.setSkuId(skuId);
        inStockOrderItemDTO.setUnitTotalCount(totalCount);
        if (locationDTO != null) {
            inStockOrderItemDTO.setLocationId(locationDTO.getId());
            inStockOrderItemDTO.setLocationName(locationDTO.getName());
        }
        inStockOrderItemDTO.setBatchTime(batchTime);

        return inStockOrderItemDTO;
    }

    private OutStockOrderItemDTO setOutStockOrderItemDTO(Integer orgId, Long skuId, BigDecimal totalCount,
                                                         LoactionDTO locationDTO, Date batchTime) {
        OutStockOrderItemDTO outStockOrderItemDTO = new OutStockOrderItemDTO();
        outStockOrderItemDTO.setOrgId(orgId);
        outStockOrderItemDTO.setSkuId(skuId);
        outStockOrderItemDTO.setUnitTotalCount(totalCount);
        if (locationDTO != null) {
            outStockOrderItemDTO.setLocationid(locationDTO.getId());
            outStockOrderItemDTO.setLocationname(locationDTO.getName());
        }
        outStockOrderItemDTO.setBatchTime(batchTime);

        return outStockOrderItemDTO;
    }

    /**
     * 根据货位库存计算组装出入库数据
     */
    private void calculateStockOrderItemByInventory(Long skuId, List<BatchInventoryDTO> batchInventories,
                                                    List<OutStockOrderItemDTO> outStockOrderItemDTOS, List<InStockOrderItemDTO> inStockOrderItemDTOS,
                                                    BigDecimal changeTotalCount, Integer orgId, LoactionDTO toLocation, Date batchTime) {
        batchInventories = batchInventories.stream()
                .filter(batchInventoryDTO -> batchInventoryDTO.getStoreTotalCount().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        batchInventories.sort(Comparator.comparing(BatchInventoryDTO::getLocationSequence));
        BigDecimal totalCount = batchInventories.stream().map(BatchInventoryDTO::getStoreTotalCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalCount.compareTo(changeTotalCount) > 0) {
            inStockOrderItemDTOS.add(setInStockOrderItemDTO(orgId, skuId, changeTotalCount, toLocation, batchTime));

            for (BatchInventoryDTO batchInventory : batchInventories) {
                LoactionDTO fromLocation = new LoactionDTO();
                fromLocation.setId(batchInventory.getLocationId());
                fromLocation.setName(batchInventory.getLocationName());
                BigDecimal currentStoreTotalCount = batchInventory.getStoreTotalCount();
                BigDecimal storeCount =
                        changeTotalCount.compareTo(currentStoreTotalCount) <= 0 ? changeTotalCount : currentStoreTotalCount;
                outStockOrderItemDTOS.add(
                        setOutStockOrderItemDTO(orgId, skuId, storeCount, fromLocation, batchInventory.getBatchTime()));

                batchInventory.setStoreTotalCount(currentStoreTotalCount.subtract(storeCount));
                if (changeTotalCount.compareTo(currentStoreTotalCount) <= 0) {
                    break;
                } else {
                    changeTotalCount = changeTotalCount.subtract(currentStoreTotalCount);
                }
            }
        } else {
            LOGGER.warn("处理品校正时对应货位库存数据不足,skuId:{}", skuId);
        }
    }

    /**
     * 筛选出非易款数据
     */
    private List<Long> filterSkuBySource(List<Long> skuIds, int source) {
        List<Long> filterSkuIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(skuIds)) {
            Map<Long, ProductSkuInfoReturnDTO> products = productSkuService.getProductInfoBySkuId(skuIds);
            filterSkuIds = products.values().stream().filter(product -> source != product.getSource().intValue())
                    .map(ProductSkuInfoReturnDTO::getSkuId).collect(Collectors.toList());
        }
        return filterSkuIds;
    }

    /**
     * 残次品校正(以ERP为准)
     */
    public void adjustDisposedProductLocationInventoryByERP(BatchInventoryAdjustDTO batchInventoryAdjustDTO) {
        // 查出erp所有的残次品、陈列品库存
        Integer orgId = batchInventoryAdjustDTO.getOrgId();
        Integer warehouseId = batchInventoryAdjustDTO.getWarehouseId();
        List<ERPDisposedInventoryDTO> erpDisposedInventories = getERPDisposedProductInventoryList(orgId, warehouseId);
        List<BatchInventoryPO> wmsDisposedInventories = getWMSDisposedProductInventoryList(warehouseId);

        // 查出erp和wms残次品的批次库存，为后续加减库存提供参照
        List<Long> skuIds = new ArrayList<>();

        Map<Long, List<ERPDisposedInventoryDTO>> erpDisposedInventoryMap = new HashMap<>(16);
        Map<Long, List<BatchInventoryPO>> wmsDisposedInventoryMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(erpDisposedInventories)) {
            erpDisposedInventoryMap = erpDisposedInventories.stream()
                    .collect(Collectors.groupingBy(erp -> Long.valueOf(erp.getProductSkuId())));
            skuIds.addAll(erpDisposedInventoryMap.keySet());
        }
        if (CollectionUtils.isNotEmpty(wmsDisposedInventories)) {
            wmsDisposedInventoryMap =
                    wmsDisposedInventories.stream().collect(Collectors.groupingBy(BatchInventoryPO::getProductSkuId));
            skuIds.addAll(wmsDisposedInventoryMap.keySet());
        }
        skuIds = skuIds.stream().distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(skuIds)) {
            LOGGER.warn("该仓库ERP和WMS都没有残次品:{}", warehouseId);
            return;
        }

        // 获取一个分拣位，为后续找不到可转移货位做备用
        LocationReturnDTO location = batchInventoryManageBL.getLoactionByWarehouseId(warehouseId, orgId,
                LocationAreaEnum.待检区.getType().byteValue(), BigDecimal.ZERO);
        // 获取仓库下所有残次品货位
        LocationInfoQueryDTO locationInfoQueryDTO = new LocationInfoQueryDTO();
        List<Byte> subCategoryList = new ArrayList<>();
        subCategoryList.add(LocationEnum.残次品位.getType().byteValue());
        subCategoryList.add(LocationAreaEnum.残次品区.getType().byteValue());
        locationInfoQueryDTO.setSubcategoryList(subCategoryList);
        locationInfoQueryDTO.setWarehouseId(warehouseId);
        locationInfoQueryDTO.setCityId(orgId);
        List<LoactionDTO> defectiveLocationList = iLocationService.pageListLocation(locationInfoQueryDTO).getDataList();

        List<Long> defectiveLocationIds = new ArrayList<>();
        LocationReturnDTO defectiveLocation = new LocationReturnDTO();
        if (CollectionUtils.isNotEmpty(defectiveLocationList)) {
            defectiveLocationList.sort(Comparator.comparing(LoactionDTO::getSubcategory));
            defectiveLocationIds = defectiveLocationList.stream().map(LoactionDTO::getId).collect(Collectors.toList());
            defectiveLocation.setId(defectiveLocationList.get(0).getId());
            defectiveLocation.setName(defectiveLocationList.get(0).getName());
        } else {
            defectiveLocation = batchInventoryManageBL.getLoactionByWarehouseId(warehouseId, orgId,
                    LocationAreaEnum.残次品区.getType().byteValue(), BigDecimal.ZERO);
        }

        // 查询仓库批次库存
        Map<Long, List<BatchInventoryPO>> batchInventoryMap = new HashMap<>(16);
        List<BatchInventoryPO> batchInventories = getWMSBatchInventoryList(skuIds, warehouseId, defectiveLocationIds);
        if (CollectionUtils.isNotEmpty(batchInventories)) {
            batchInventoryMap =
                    batchInventories.stream().collect(Collectors.groupingBy(BatchInventoryPO::getProductSkuId));
        }

        // 残次品转出
        StoreTransferOrderDTO defectiveOutStoreTransferOrder = new StoreTransferOrderDTO();
        List<StoreTransferOrderItemDTO> defectiveOutStoreTransferOrderItems = new ArrayList<>();

        // 残次品转入
        StoreTransferOrderDTO defectiveInStoreTransferOrder = new StoreTransferOrderDTO();
        List<StoreTransferOrderItemDTO> defectiveInStoreTransferOrderItems = new ArrayList<>();

        // 以ERP为准比对差异
        Map<Long, List<ERPDisposedInventoryDTO>> finalErpDisposedInventoryMap = erpDisposedInventoryMap;
        Map<Long, List<BatchInventoryPO>> finalBatchInventoryMap = batchInventoryMap;
        List<Long> finalDefectiveLocationIds = defectiveLocationIds;
        LocationReturnDTO finalDefectiveLocation = defectiveLocation;
        wmsDisposedInventoryMap.forEach((skuId, wmsDisposedInventoryList) -> {
            List<ERPDisposedInventoryDTO> erpDisposedInventoryList = finalErpDisposedInventoryMap.get(skuId);
            List<BatchInventoryPO> batchInventoryList = finalBatchInventoryMap.get(skuId);

            BigDecimal erpStoreCount = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(erpDisposedInventoryList)) {
                erpDisposedInventories.removeAll(erpDisposedInventoryList);
                erpStoreCount = erpDisposedInventoryList.stream().map(ERPDisposedInventoryDTO::getStockCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            BigDecimal wmsStoreCount = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(wmsDisposedInventoryList)) {
                wmsStoreCount = wmsDisposedInventoryList.stream().map(BatchInventoryPO::getStoreTotalCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            BigDecimal changeCount = erpStoreCount.subtract(wmsStoreCount);
            if (changeCount.compareTo(BigDecimal.ZERO) > 0) {
                // erp比wms多，需要随机找个批次库存移到残次品货位去
                if (CollectionUtils.isEmpty(batchInventoryList)) {
                    LOGGER.warn("{},该残次品库存ERP比WMS多，但未找到批次库存，skuId:{}", warehouseId, skuId);
                } else {
                    batchInventoryList = batchInventoryList.stream()
                            .filter(inventory -> inventory.getStoreTotalCount().compareTo(BigDecimal.ZERO) > 0)
                            .sorted(Comparator.comparing(BatchInventoryPO::getStoreTotalCount).reversed())
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(batchInventoryList)) {
                        LOGGER.warn("{},该残次品库存ERP比WMS多，但批次库存都为负数，skuId:{}", warehouseId, skuId);
                    } else {
                        buildStoreTransferOrderItemDTOS(skuId, batchInventoryList, defectiveInStoreTransferOrderItems,
                                finalDefectiveLocation.getId(), finalDefectiveLocation.getName(), changeCount, orgId);
                    }
                }
            } else if (changeCount.compareTo(BigDecimal.ZERO) < 0) {
                // erp比wms少，需要从残次品货位随机移到有库存的货位去,如若找不到则放到分拣位上
                changeCount = changeCount.negate();
                List<BatchInventoryPO> finalWmsDisposedInventoryList = wmsDisposedInventoryList.stream()
                        .sorted(Comparator.comparing(BatchInventoryPO::getStoreTotalCount).reversed())
                        .collect(Collectors.toList());
                Long toLocationId = location.getId();
                String toLocationName = location.getName();

                if (CollectionUtils.isNotEmpty(batchInventoryList)) {
                    Optional<BatchInventoryPO> any =
                            batchInventoryList.stream().filter(batchInventory -> batchInventory.getLocationId() != null
                                    && !finalDefectiveLocationIds.contains(batchInventory.getLocationId())).findAny();
                    if (any.isPresent()) {
                        toLocationId = any.get().getLocationId();
                        toLocationName = any.get().getLocationName();
                    }
                }
                buildStoreTransferOrderItemDTOS(skuId, finalWmsDisposedInventoryList,
                        defectiveOutStoreTransferOrderItems, toLocationId, toLocationName, changeCount, orgId);
            }
        });

        // 处理erp有，WMS没有的
        if (CollectionUtils.isNotEmpty(erpDisposedInventories)) {
            erpDisposedInventories.forEach(erpDisposedInventory -> {
                Long skuId = Long.valueOf(erpDisposedInventory.getProductSkuId());
                List<BatchInventoryPO> batchInventoryList = finalBatchInventoryMap.get(skuId);
                if (CollectionUtils.isEmpty(batchInventoryList)) {
                    LOGGER.warn("{},该残次品库存WMS没有批次库存，skuId:{}", warehouseId, skuId);
                } else {
                    batchInventoryList = batchInventoryList.stream()
                            .filter(inventory -> inventory.getStoreTotalCount().compareTo(BigDecimal.ZERO) > 0)
                            .sorted(Comparator.comparing(BatchInventoryPO::getStoreTotalCount).reversed())
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(batchInventoryList)) {
                        LOGGER.warn("{},该残次品库存WMS没有，并且批次库存都为负数，skuId:{}", warehouseId, skuId);
                    } else {
                        buildStoreTransferOrderItemDTOS(skuId, batchInventoryList, defectiveInStoreTransferOrderItems,
                                finalDefectiveLocation.getId(), finalDefectiveLocation.getName(),
                                erpDisposedInventory.getStockCount(), orgId);
                    }
                }
            });
        }

        if (CollectionUtils.isNotEmpty(defectiveOutStoreTransferOrderItems)) {
            defectiveOutStoreTransferOrder.setWarehouse_Id(warehouseId);
            defectiveOutStoreTransferOrder.setOrg_id(orgId);
            defectiveOutStoreTransferOrder.setStartTime(new Date());
            defectiveOutStoreTransferOrder.setFinishTime(new Date());
            defectiveOutStoreTransferOrder.setIgnoreProductionDate(false);
            defectiveOutStoreTransferOrder.setIgnoreHasNotEnoughStore(true);
            defectiveOutStoreTransferOrder.setTransferType(StoreTransferEnum.处理品转出移库.getType());
            defectiveOutStoreTransferOrder.setStoreTransferOrderItemDTOS(defectiveOutStoreTransferOrderItems);
            LOGGER.info("{},以ERP为准校正残次品库存，处理品转出移库组装数据:{}", warehouseId,
                    JSON.toJSONString(defectiveOutStoreTransferOrder));
            iStoreTransferOrderService.updateStoreTranferNoOrder(defectiveOutStoreTransferOrder);
        }

        if (CollectionUtils.isNotEmpty(defectiveInStoreTransferOrderItems)) {
            defectiveInStoreTransferOrder.setWarehouse_Id(warehouseId);
            defectiveInStoreTransferOrder.setOrg_id(orgId);
            defectiveInStoreTransferOrder.setStartTime(new Date());
            defectiveInStoreTransferOrder.setFinishTime(new Date());
            defectiveInStoreTransferOrder.setIgnoreProductionDate(false);
            defectiveInStoreTransferOrder.setIgnoreHasNotEnoughStore(true);
            defectiveInStoreTransferOrder.setTransferType(StoreTransferEnum.处理品转入移库.getType());
            defectiveInStoreTransferOrder.setStoreTransferOrderItemDTOS(defectiveInStoreTransferOrderItems);
            LOGGER.info("{},以ERP为准校正残次品库存，处理品转入移库组装数据:{}", warehouseId,
                    JSON.toJSONString(defectiveInStoreTransferOrder));
            iStoreTransferOrderService.updateStoreTranferNoOrder(defectiveInStoreTransferOrder);
        }
    }

    /**
     * 获取ERP残次品数据
     *
     * @param orgId
     * @param warehouseId
     * @return
     */
    private List<ERPDisposedInventoryDTO> getERPDisposedProductInventoryList(Integer orgId, Integer warehouseId) {
        List<ERPDisposedInventoryDTO> erpDisposedInventories = new ArrayList<>();
        ERPDisposedInventoryQueryDTO erpDisposedInventoryQueryDTO = new ERPDisposedInventoryQueryDTO();
        erpDisposedInventoryQueryDTO.setCityId(orgId.toString());
        erpDisposedInventoryQueryDTO.setStoreHouseId(warehouseId.toString());
        erpDisposedInventoryQueryDTO.setOnlySearchUncompletedNote(false);
        erpDisposedInventoryQueryDTO.setDisposedType(ERPDisposedInventoryType.残次品.getType());
        erpDisposedInventoryQueryDTO.setPageIndex(1);
        erpDisposedInventoryQueryDTO.setPageSize(100);
        try {
            BaseResponse<List<ERPDisposedInventoryDTO>> baseResponse =
                    HttpUtil.httpPost(BatchInventoryConfigUtil.erpAPIUrl + "DisposedInNoteInfo/GetDisposedProductList",
                            JSON.toJSONString(erpDisposedInventoryQueryDTO),
                            new TypeToken<BaseResponse<List<ERPDisposedInventoryDTO>>>() {
                            }.getType());
            List<ERPDisposedInventoryDTO> data = baseResponse.getData();
            Integer totalCount = baseResponse.getTotalCount();
            final Integer nPerPageCount = 100;
            if (totalCount > nPerPageCount) {
                final int startIndex = 2;
                int pageTotalIndex = totalCount - totalCount / 100 * nPerPageCount > 0 ? 1 + totalCount / nPerPageCount
                        : totalCount / nPerPageCount;
                for (int pageIndex = startIndex; pageIndex <= pageTotalIndex; pageIndex++) {
                    erpDisposedInventoryQueryDTO.setPageIndex(pageIndex);
                    BaseResponse<List<ERPDisposedInventoryDTO>> response =
                            HttpUtil.httpPost(BatchInventoryConfigUtil.erpAPIUrl + "DisposedInNoteInfo/GetDisposedProductList",
                                    JSON.toJSONString(erpDisposedInventoryQueryDTO),
                                    new TypeToken<BaseResponse<List<ERPDisposedInventoryDTO>>>() {
                                    }.getType());
                    data.addAll(response.getData());
                }
            }
            // 过滤易款产品
            erpDisposedInventories = data;
            if (CollectionUtils.isNotEmpty(erpDisposedInventories)) {
                List<Long> erpFilterSkuIds =
                        filterSkuBySource(erpDisposedInventories.stream().map(erp -> Long.valueOf(erp.getProductSkuId()))
                                .collect(Collectors.toList()), ProductSourceType.易款连锁);
                erpDisposedInventories = erpDisposedInventories.stream()
                        .filter(erp -> erpFilterSkuIds.contains(Long.valueOf(erp.getProductSkuId()))
                                && erp.getStockCount().compareTo(BigDecimal.ZERO) != 0)
                        .collect(Collectors.toList());
            }
            LOGGER.info("调用ERP查询处理品库存数据:{}", JSON.toJSONString(erpDisposedInventories));
        } catch (Exception e) {
            LOGGER.error("调用ERP查询处理品库存失败，参数:{}", JSON.toJSONString(erpDisposedInventoryQueryDTO), e);
        }

        return erpDisposedInventories;
    }

    /**
     * 获取WMS残次品数据
     *
     * @param warehouseId
     * @return
     */
    private List<BatchInventoryPO> getWMSDisposedProductInventoryList(Integer warehouseId) {
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setWarehouseId(warehouseId);
        List<Integer> subCategoryList = new ArrayList<>();
        subCategoryList.add(LocationAreaEnum.残次品区.getType());
        subCategoryList.add(LocationEnum.残次品位.getType());
        batchInventoryQueryDTO.setSubCategoryList(subCategoryList);
        PageResult<BatchInventoryPO> pageResult =
                batchInventoryProductStoreBatchMapper.findBatchInventoryList(batchInventoryQueryDTO, null, null);
        List<BatchInventoryPO> batchDisposedInventories = pageResult.getResult();
        // 过滤易款产品
        List<Long> filterSkuIds = filterSkuBySource(
                batchDisposedInventories.stream().map(BatchInventoryPO::getProductSkuId).collect(Collectors.toList()),
                ProductSourceType.易款连锁);
        List<BatchInventoryPO> wmsDisposedProductInventoryList = batchDisposedInventories.stream()
                .filter(sku -> filterSkuIds.contains(sku.getProductSkuId())).collect(Collectors.toList());
        LOGGER.info("WMS处理品库存数据:{}", JSON.toJSONString(wmsDisposedProductInventoryList));
        return wmsDisposedProductInventoryList;
    }

    /**
     * 获取仓库批次库存(大于0且过滤易款,过滤残次品货位批次库存)
     */
    private List<BatchInventoryPO> getWMSBatchInventoryList(List<Long> skuIds, Integer warehouseId,
                                                            List<Long> defectiveLocationIds) {
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setWarehouseId(warehouseId);
        batchInventoryQueryDTO.setSkuIds(skuIds);
        PageResult<BatchInventoryPO> pageResult =
                batchInventoryProductStoreBatchMapper.findBatchInventoryList(batchInventoryQueryDTO, null, null);
        List<BatchInventoryPO> batchDisposedInventories = pageResult.getResult();
        // 过滤易款产品
        List<Long> filterSkuIds = filterSkuBySource(
                batchDisposedInventories.stream().map(BatchInventoryPO::getProductSkuId).collect(Collectors.toList()),
                ProductSourceType.易款连锁);
        return batchDisposedInventories.stream().filter(sku -> sku.getLocationId() != null
                        && filterSkuIds.contains(sku.getProductSkuId()) && !defectiveLocationIds.contains(sku.getLocationId()))
                .collect(Collectors.toList());
    }

    /**
     * 组装移库单项
     *
     * @param batchInventoryList
     * @param toLocationId
     * @param toLocationName
     * @param changeCount
     */
    private void buildStoreTransferOrderItemDTOS(Long skuId, List<BatchInventoryPO> batchInventoryList,
                                                 List<StoreTransferOrderItemDTO> storeTransferOrderItems, Long toLocationId, String toLocationName,
                                                 BigDecimal changeCount, Integer orgId) {
        for (BatchInventoryPO batchInventory : batchInventoryList) {
            if (changeCount.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            StoreTransferOrderItemDTO storeTransferOrderItemDTO = new StoreTransferOrderItemDTO();
            storeTransferOrderItemDTO.setSkuId(skuId);
            storeTransferOrderItemDTO.setOrg_id(orgId);

            storeTransferOrderItemDTO.setProductStoreId(batchInventory.getProductStoreId());
            storeTransferOrderItemDTO.setProductStoreBatchId(batchInventory.getStoreBatchId());
            storeTransferOrderItemDTO.setBatchAttributeInfoNo(batchInventory.getBatchAttributeInfoNo());
            storeTransferOrderItemDTO.setProductionDate(batchInventory.getProductionDate());
            storeTransferOrderItemDTO.setBatchTime(batchInventory.getBatchTime());
            storeTransferOrderItemDTO.setExpireTime(batchInventory.getExpireTime());
            storeTransferOrderItemDTO.setFromLocation_id(batchInventory.getLocationId());
            storeTransferOrderItemDTO.setFromLocationName(batchInventory.getLocationName());
            storeTransferOrderItemDTO.setToLocation_id(toLocationId);
            storeTransferOrderItemDTO.setToLocationName(toLocationName);

            if (batchInventory.getStoreTotalCount().compareTo(changeCount) >= 0) {
                storeTransferOrderItemDTO.setUnitTotalCount(changeCount);
                BigDecimal[] divideAndRemainder = changeCount.divideAndRemainder(batchInventory.getPackageQuantity());
                storeTransferOrderItemDTO.setPackageCount(divideAndRemainder[0]);
                storeTransferOrderItemDTO.setOverMovePackageCount(divideAndRemainder[0]);
                storeTransferOrderItemDTO.setUnitCount(divideAndRemainder[1]);
                storeTransferOrderItemDTO.setOverMoveUnitCount(divideAndRemainder[1]);

                storeTransferOrderItems.add(storeTransferOrderItemDTO);
                break;
            } else {
                storeTransferOrderItemDTO.setUnitTotalCount(batchInventory.getStoreTotalCount());
                BigDecimal[] divideAndRemainder =
                        batchInventory.getStoreTotalCount().divideAndRemainder(batchInventory.getPackageQuantity());
                storeTransferOrderItemDTO.setPackageCount(divideAndRemainder[0]);
                storeTransferOrderItemDTO.setOverMovePackageCount(divideAndRemainder[0]);
                storeTransferOrderItemDTO.setUnitCount(divideAndRemainder[1]);
                storeTransferOrderItemDTO.setOverMoveUnitCount(divideAndRemainder[1]);

                storeTransferOrderItems.add(storeTransferOrderItemDTO);

                changeCount = changeCount.subtract(batchInventory.getStoreTotalCount());
            }
        }
    }

    /**
     * 校正批次编号
     */
    public void syncBatchInventoryBatchNo(BatchInventoryProductionDateSyncQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");

        LOGGER.info("【校正批次编号】[{}]开始...", queryDTO.getWarehouseId());
        long startTime = System.currentTimeMillis();
        // 1、查询仓库下的所有不为0的货位库存
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setWarehouseId(queryDTO.getWarehouseId());
        PageList<BatchInventoryDTO> pageList = batchInventoryQueryBL.findBatchInventoryListNew(batchInventoryQueryDTO);
        if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
            LOGGER.info("【校正批次编号】[{}]货位库存为空，跳过", queryDTO.getWarehouseId());
            return;
        }
        long firstTime = System.currentTimeMillis();
        LOGGER.info("【校正批次编号】[{}]第一步查货位库存，耗时：{}ms，共{}条", queryDTO.getWarehouseId(), (firstTime - startTime),
                pageList.getDataList().size());

        // 2、拼接对象
        long secondTime = System.currentTimeMillis();
        List<BatchInventoryInfoUpdateDTO> lstUpdateDto = new ArrayList<>();
        pageList.getDataList().stream().filter(p -> p.getProductionDate() != null).forEach(p -> {
            BatchInventoryInfoUpdateDTO updateDto = new BatchInventoryInfoUpdateDTO();
            updateDto.setBatchTime(p.getBatchTime());
            updateDto.setProductionDate(p.getProductionDate());
            updateDto.setProductStoreId(p.getProductStoreId());
            updateDto.setOwnerType(p.getOwnerType());
            updateDto.setBatchAttributeInfoNo(p.getBatchAttributeInfoNo());
            updateDto.setStoreBatchId(p.getStoreBatchId());
            lstUpdateDto.add(updateDto);
        });
        long thirdTime = System.currentTimeMillis();
        LOGGER.info("【校正批次编号】[{}]拼接对象，找出待修改的货位库存，耗时：{}ms", queryDTO.getWarehouseId(), (thirdTime - secondTime));

        // 4、修改货位库存的生产日期
        if (CollectionUtils.isNotEmpty(lstUpdateDto)) {
            List<List<BatchInventoryInfoUpdateDTO>> lists = splitListNew(lstUpdateDto, 200);
            for (List<BatchInventoryInfoUpdateDTO> updateList : lists) {
                batchInventoryManageBL.updateBatchNoByList(updateList);
            }
        }
        long fourTime = System.currentTimeMillis();
        LOGGER.info("【校正批次编号】[{}]结束，耗时：{}ms", queryDTO.getWarehouseId(), (fourTime - thirdTime));
    }

    /**
     * 根据ERP校正货位库存的生产日期
     */
    public void syncBatchInventoryProductionDateByERP(BatchInventoryProductionDateSyncQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");

        LOGGER.info("【根据ERP校正货位库存的生产日期】[{}]开始...", queryDTO.getWarehouseId());
        long startTime = System.currentTimeMillis();
        // 1、查询仓库下的所有不为0的货位库存
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setWarehouseId(queryDTO.getWarehouseId());
        PageList<BatchInventoryDTO> pageList = batchInventoryQueryBL.findBatchInventoryListNew(batchInventoryQueryDTO);
        if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
            LOGGER.info("【根据ERP校正货位库存的生产日期】[{}]货位库存为空，跳过", queryDTO.getWarehouseId());
            return;
        }

        // 2、查询产品的销售库存
        List<Long> productSkuIds =
                pageList.getDataList().stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        ProductStoreQueryDTO productStoreQueryDTO = new ProductStoreQueryDTO();
        productStoreQueryDTO.setCityId(queryDTO.getCityId());
        productStoreQueryDTO.setWarehouseId(queryDTO.getWarehouseId());
        productStoreQueryDTO.setProductSkuIds(productSkuIds);
        List<WarehouseStoreDTO> warehouseStoreDTOS =
                iWarehouseInventoryCheckService.getSaleInventoryList(productStoreQueryDTO);
        if (CollectionUtils.isEmpty(warehouseStoreDTOS)) {
            LOGGER.info("【根据ERP校正货位库存的生产日期】[{}]销售库存为空，跳过", queryDTO.getWarehouseId());
            return;
        }

        // 3、查询ERP的生产日期
        List<BatchInventoryInfoUpdateDTO> updateBatchInventoryList = new ArrayList<>();
        Map<Long, List<BatchInventoryDTO>> batchInventoryMap =
                pageList.getDataList().stream().collect(Collectors.groupingBy(p -> p.getProductSkuId()));
        batchInventoryMap.forEach((skuId, batchInventoryList) -> {
            if (warehouseStoreDTOS.stream().anyMatch(p -> Objects.equals(p.getProductSkuId(), skuId))) {
                // 按sku维度去查找生产日期
                Optional<WarehouseStoreDTO> optional =
                        warehouseStoreDTOS.stream().filter(p -> Objects.equals(p.getProductSkuId(), skuId)).findFirst();
                if (optional.isPresent() && optional.get().getSaleStoreTotalCount() != null) {
                    // 查ERP的生产日期
                    Date erpProductionDate = getProductDateByERP(optional.get().getWarehouseId(),
                            optional.get().getProductSpecId(), optional.get().getSaleStoreTotalCount());
                    if (erpProductionDate != null) {
                        // 找到生产日期小于ERP生产日期的货位库存
                        List<BatchInventoryInfoUpdateDTO> updateList = batchInventoryList.stream()
                                .filter(
                                        p -> p.getProductionDate() == null || p.getProductionDate().before(erpProductionDate))
                                .map(p -> {
                                    BatchInventoryInfoUpdateDTO updateDTO = new BatchInventoryInfoUpdateDTO();
                                    updateDTO.setStoreBatchId(p.getStoreBatchId());
                                    updateDTO.setProductStoreId(p.getProductStoreId());
                                    updateDTO.setOwnerType(p.getOwnerType());
                                    updateDTO.setBatchTime(p.getBatchTime());
                                    updateDTO.setProductionDate(erpProductionDate);
                                    return updateDTO;
                                }).collect(Collectors.toList());
                        // 待修改的货位库存
                        if (CollectionUtils.isNotEmpty(updateList)) {
                            updateBatchInventoryList.addAll(updateList);
                        }
                    }
                }
            } else {
                LOGGER.info("【根据ERP校正货位库存的生产日期】跳过查不到销售库存的skuId：{}", skuId);
            }
        });

        // 4、修改货位库存的生产日期
        if (CollectionUtils.isNotEmpty(updateBatchInventoryList)) {
            List<List<BatchInventoryInfoUpdateDTO>> lists = splitListNew(updateBatchInventoryList, 200);
            for (List<BatchInventoryInfoUpdateDTO> updateList : lists) {
                batchInventoryManageBL.updateBatchInventoryInfo(updateList, false);
            }
        }
    }

    /**
     * 根据ERP查生产日期
     *
     * @return
     */
    private Date getProductDateByERP(Integer warehouseId, Long productSpecId, BigDecimal saleStoreCount) {
        try {
            String url = String.format("Product/GetProductionDateBySepcId?productSpecId=%s&storeHouseId=%s&num=%d",
                    productSpecId, warehouseId, saleStoreCount.intValue());
            String timeStr = HttpUtil.httpGet(BatchInventoryConfigUtil.newErpAPIUrl + url);
            LOGGER.info("根据ERP查生产日期storeHouseId：{}, productSpecId：{}, num：{}, timeStr：{}", warehouseId, productSpecId,
                    saleStoreCount, timeStr);
            if (StringUtils.isNotEmpty(timeStr)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                return formatter.parse(timeStr);
            }
        } catch (Exception e) {
            LOGGER.error(String.format("根据ERP查生产日期异常, storeHouseId：%s, productSpecId：%s, num：%s", warehouseId,
                    productSpecId, saleStoreCount), e);
        }
        return null;
    }

    /**
     * list分组
     *
     * @return
     */
    private <T> List<List<T>> splitListNew(List<T> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }

        List<List<T>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addBatchInventoryByWarehouseId(Integer warehouseId, String locationName) {
        LOGGER.info("根据仓库新增批次库存warehouseId:{},locationName:{}", warehouseId, locationName);
        LoactionDTO location = iLocationService.getLocationByName(warehouseId, locationName);
        if (location == null) {
            throw new BusinessValidateException("找不到该货位，请检查货位名称是否正确");
        }
        // 查找仓库下有仓库库存无批次库存的数据
        List<ProductInventoryPO> noBatchInventoryPOS = productStoreMapper.findNoBatchInventoryStoreIds(warehouseId);
        if (CollectionUtils.isEmpty(noBatchInventoryPOS)) {
            return;
        }
        LOGGER.info("有仓库库存，无货位库存的数据:{}", JSON.toJSONString(noBatchInventoryPOS));
        List<ProductStoreBatchPO> productStoreBatchPOS = new ArrayList<>();
        List<ProductInventoryChangeRecordDTO> productInventoryChangeRecordDTOS = new ArrayList<>();
        noBatchInventoryPOS.forEach(noBatchInventory -> {
            ProductStoreBatchPO productStoreBatchPO = new ProductStoreBatchPO();
            productStoreBatchPO.setId(UUIDUtil.getUUID().replaceAll("-", ""));
            productStoreBatchPO.setProductStoreId(noBatchInventory.getId());
            productStoreBatchPO.setLocationId(location.getId());
            productStoreBatchPO.setLocationName(location.getName());
            productStoreBatchPO.setLocationCategory(Integer.valueOf(location.getCategory()));
            productStoreBatchPO.setSubcategory(Integer.valueOf(location.getSubcategory()));
            productStoreBatchPO.setBatchTime(noBatchInventory.getLastUpdateTime());
            productStoreBatchPO.setTotalCount(noBatchInventory.getUnitTotalCount());

            String batchAttributeInfoNo = batchAttributeInfoBL.getProductBatchAttributeInfoNo(noBatchInventory.getId(),
                    noBatchInventory.getOwnerType(), null, noBatchInventory.getLastUpdateTime(), new ArrayList<>(), true);
            productStoreBatchPO.setBatchAttributeInfoNo(batchAttributeInfoNo);
            productStoreBatchPOS.add(productStoreBatchPO);

            ProductInventoryChangeRecordDTO productInventoryChangeRecordDTO = new ProductInventoryChangeRecordDTO();
            productInventoryChangeRecordDTO.setId(UUIDUtil.getUUID());
            productInventoryChangeRecordDTO.setProductStoreId(noBatchInventory.getId());
            productInventoryChangeRecordDTO.setCityId(noBatchInventory.getCityId());
            productInventoryChangeRecordDTO.setJiupiEventType(JiupiEventType.自动校正.getType());
            productInventoryChangeRecordDTO.setCountMaxUnit(BigDecimal.ZERO);
            productInventoryChangeRecordDTO.setCountMinUnit(noBatchInventory.getUnitTotalCount());
            productInventoryChangeRecordDTO.setTotalCount(noBatchInventory.getUnitTotalCount());
            productInventoryChangeRecordDTO.setSourceTotalCount(BigDecimal.ZERO);
            productInventoryChangeRecordDTO.setDescription("自动校正");
            productInventoryChangeRecordDTO.setCreateTime(new Date());
            productInventoryChangeRecordDTO.setStoreType(InventoryChangeTypes.WAREHOUSE);

            // 2、新增批次库存变更记录
            productStoreBatchChangeRecordBL.createStoreBatchChangeRecord(productInventoryChangeRecordDTO.getId(),
                    Arrays.asList(productStoreBatchPO));
        });

        LOGGER.info("批量新增批次库存数据:{}", JSON.toJSONString(productStoreBatchPOS));
        batchInventoryProductStoreBatchMapper.batchAdd(productStoreBatchPOS);

        // 新增批次库存变更记录
        iProductInventoryRecordManagerService.saveProductStoreChangeRecord(productInventoryChangeRecordDTOS);

    }

    /**
     * 分页查询批次库存，返回list
     *
     * @param storeIdList
     * @return
     */
    private List<ProductStoreBatchPO> pageListProductStoreBatch(List<String> storeIdList) {
        StoreBatchQueryDTO storeBatchQueryDTO = new StoreBatchQueryDTO();
        storeBatchQueryDTO.setPageSize(1000);
        storeBatchQueryDTO.setStoreIdList(storeIdList);
        int pageCount = 1;
        List<ProductStoreBatchPO> productStoreBatchPOS = new ArrayList<>();
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
            PageResult<ProductStoreBatchPO> batchListByProductStoreIds =
                    batchInventoryProductStoreBatchMapper.pageListProductStoreBatch(storeBatchQueryDTO);
            storeBatchQueryDTO.setPageNum(1);
            if (pageNum == 1) {
                pageCount = batchListByProductStoreIds.getPager().getTotalPage();
            }
            List<ProductStoreBatchPO> dataList = batchListByProductStoreIds.getResult();
            if (CollectionUtils.isNotEmpty(dataList)) {
                dataList.forEach(p -> {
                    if (!productStoreBatchPOS.stream().anyMatch(q -> Objects.equals(p.getId(), q.getId()))) {
                        productStoreBatchPOS.add(p);
                    }
                });
            }
        }
        return productStoreBatchPOS;
    }

    /**
     * 根据拣货统计库存计算周转区库存
     */
    public void adjustCHKBatchInventoryByPicking(BatchInventorySyncQueryDTO queryDTO) {
        LOGGER.info("矫正周转区货位库存请求参数:{}", JSON.toJSONString(queryDTO));
        Integer warehouseId = queryDTO.getWarehouseId();
        boolean openLocationStock = warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!openLocationStock) {
            LOGGER.info("未开启货位库存，不需要处理：{}", warehouseId);
            return;
        }
        // 查询货区所有货位库存信息
        List<ProductStoreBatchDTO> productStoreBatchPOS =
                warehousePickingQueryService.findPickingProductStoreBatchWithOutSku(warehouseId, queryDTO.getOwnerId());

        // 查询拣货中数据
        ReportBatchLocationInfoQueryDTO batchLocationInfoQueryDTO = new ReportBatchLocationInfoQueryDTO();
        batchLocationInfoQueryDTO.setWarehouseId(warehouseId);
        List<ReportBatchLocationInfoDTO> lstPickingData =
                warehousePickingQueryService.listPickingCountByWarehouseId(batchLocationInfoQueryDTO);
        // 当前仓库无拣货中数据，不需要处理
        if (CollectionUtils.isEmpty(lstPickingData)) {
            LOGGER.info("当前仓库无拣货中数据，不需要处理：{}", warehouseId);
            return;
        }

        Map<String, List<ProductStoreBatchDTO>> productStoreBatchMap =
                productStoreBatchPOS.stream().collect(Collectors.groupingBy(
                        p -> String.format("%s-%s-%s", p.getProductSpecificationId(), p.getOwnerId(), p.getSecOwnerId())));
        Map<String, List<ReportBatchLocationInfoDTO>> pickingMap =
                lstPickingData.stream().collect(Collectors.groupingBy(
                        p -> String.format("%s-%s-%s", p.getProductSpecificationId(), p.getOwnerId(), p.getSecOwnerId())));

        pickingMap.forEach((key, pickData) -> {
            if (!productStoreBatchMap.containsKey(key)) {
                // todo 当前无周转区货位库存，需要补充
                productStoreBatchMap.put(key, new ArrayList<>());
                return;
            }
            pickData.forEach(pickItem -> productStoreBatchMap.get(key).stream()
                    .filter(p -> p.getTotalCount().compareTo(BigDecimal.ZERO) != 0
                            && Objects.equals(pickItem.getLocationId(), p.getLocationId()))
                    .forEach(batchItem -> {
                        if (pickItem.getTotalCount().compareTo(BigDecimal.ZERO) != 0) {
                            // 抹掉货位与拣货中对应的数量
                            BigDecimal changeCount = batchItem.getTotalCount().compareTo(pickItem.getTotalCount()) > 0
                                    ? pickItem.getTotalCount() : batchItem.getTotalCount();
                            batchItem.setTotalCount(batchItem.getTotalCount().subtract(changeCount));
                            pickItem.setTotalCount(pickItem.getTotalCount().subtract(changeCount));
                        }
                    }));
        });

        LOGGER.info(String.format("分拣占用数量:%s,总库存数量:%s", pickingMap.size(), productStoreBatchMap.size()));
        // LOGGER.info(String.format("分拣占用:%s,总库存:%s", JSON.toJSONString(pickingMap),
        // JSON.toJSONString(productStoreBatchMap)));

        Integer sameCount = 0;
        Integer outWeighCount = 0;
        Integer lessThanCount = 0;
        List<ProductInventoryChangeRecordDTO> productInventoryChangeRecordDTOS = new ArrayList<>();
        List<ProductInventoryChangeRecordPO> productInventoryChangeRecordPOS = new ArrayList<>();
        for (Map.Entry<String, List<ReportBatchLocationInfoDTO>> entry : pickingMap.entrySet()) {
            List<ReportBatchLocationInfoDTO> lstPick = entry.getValue().stream()
                    .filter(p -> p.getTotalCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            List<ProductStoreBatchDTO> lstBatch = productStoreBatchMap.get(entry.getKey()).stream()
                    .filter(p -> p.getTotalCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            BigDecimal storeTotalCount =
                    lstBatch.stream().map(ProductStoreBatchDTO::getTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal pickTotalCount = lstPick.stream().map(ReportBatchLocationInfoDTO::getTotalCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 处理总库存与拣货占用库存相同的情况
            if (Objects.equals(queryDTO.getProcessOutWeigh(), null)) {
                // 分拣占用库存等于周转区总库存，且均不等于0，需要移库；等于0说明库存是正确的，不需要处理
                if (storeTotalCount.compareTo(pickTotalCount) == 0) {
                    if (storeTotalCount.compareTo(BigDecimal.ZERO) > 0) {
                        sameCount++;
                        LOGGER.info(String.format("分拣占用：【%s】,周转区库存：【%s】", JSON.toJSONString(lstPick),
                                JSON.toJSONString(lstBatch)));
                        List<ProductInventoryChangeRecordPO> lstBatchPO = getProductInventoryChangeRecordPOS(
                                queryDTO.getCityId(), lstBatch, lstPick, productInventoryChangeRecordDTOS);
                        productInventoryChangeRecordPOS.addAll(lstBatchPO);
                    }
                } else if (storeTotalCount.compareTo(pickTotalCount) < 0) {
                    lessThanCount++;
                } else if (storeTotalCount.compareTo(pickTotalCount) > 0) {
                    outWeighCount++;
                }
            }
            // 处理总库存>拣货占用库存相同的情况
            else if (queryDTO.getProcessOutWeigh()) {
                if (storeTotalCount.compareTo(pickTotalCount) > 0) {

                }
            }
            // 处理总库存<拣货占用库存相同的情况
            else {
                if (storeTotalCount.compareTo(pickTotalCount) < 0) {

                }
            }
        }

        // productInventoryChangeRecordPOS = productInventoryChangeRecordPOS.stream()
        // .filter(StreamUtils.distinctByKey(ProductInventoryChangeRecordPO::getDistinctKey))
        // .collect(Collectors.toList());

        LOGGER.info(String.format("sameCount:%s,outWeighCount:%s,lessThanCount:%s，周转区移库货位库存组装数据：%s", sameCount,
                outWeighCount, lessThanCount, JSON.toJSONString(productInventoryChangeRecordPOS)));

        // 移库操作
        if (CollectionUtils.isEmpty(productInventoryChangeRecordPOS)) {
            return;
        }

        // 不处理只统计
        if (Objects.equals(queryDTO.getProcessDirector(), false)) {
            return;
        }
        // 增加库存变更日志
        iProductInventoryRecordManagerService.saveProductStoreChangeRecord(productInventoryChangeRecordDTOS);
        productInventoryChangeRecordPOS.forEach(productInventoryChangeRecordPO -> {
            productStoreBatchBL.processLocationInventory(productInventoryChangeRecordPO, null, null, false, null);
        });
    }

    /**
     * 组装批次库存转移参数
     */
    private List<ProductInventoryChangeRecordPO> getProductInventoryChangeRecordPOS(Integer cityId,
                                                                                    List<ProductStoreBatchDTO> lstStoreData, List<ReportBatchLocationInfoDTO> lstPickData,
                                                                                    List<ProductInventoryChangeRecordDTO> productInventoryChangeRecordDTOS) {
        List<ProductInventoryChangeRecordPO> productInventoryChangeRecordPOS = new ArrayList<>();
        Date dtNow = new Date();
        if (CollectionUtils.isNotEmpty(lstStoreData) && CollectionUtils.isNotEmpty(lstPickData)) {
            lstPickData.forEach(pickItem -> {
                lstStoreData.forEach(productStoreBatch -> {
                    // 等于0的不需要处理
                    if (productStoreBatch.getTotalCount().compareTo(BigDecimal.ZERO) == 0) {
                        return;
                    }
                    // 取最小值
                    BigDecimal totalCount = productStoreBatch.getTotalCount().compareTo(pickItem.getTotalCount()) > 0
                            ? pickItem.getTotalCount() : productStoreBatch.getTotalCount();

                    // 来源货位减库存
                    ProductInventoryChangeRecordPO fromInventoryPO = new ProductInventoryChangeRecordPO();
                    fromInventoryPO.setCityId(cityId);
                    fromInventoryPO.setJiupiEventType(JiupiEventType.自动校正.getType());
                    fromInventoryPO.setLocationId(productStoreBatch.getLocationId());
                    fromInventoryPO.setBatchTime(productStoreBatch.getBatchTime());
                    fromInventoryPO.setExpireTime(productStoreBatch.getExpireTime());
                    fromInventoryPO.setProductionDate(productStoreBatch.getProductionDate());
                    fromInventoryPO.setProductStoreId(productStoreBatch.getProductStoreId());
                    // 负数，移除库存
                    fromInventoryPO.setTotalCount(totalCount.negate());
                    fromInventoryPO.setCreateTime(dtNow);
                    fromInventoryPO.setBatchAttributeInfoNo(productStoreBatch.getBatchAttributeInfoNo());
                    String fromUid = UUIDUtil.getUUID();
                    fromInventoryPO.setId(fromUid);
                    fromInventoryPO.setDescription("货区库存转移");
                    // 后续去重用
                    fromInventoryPO.setDistinctKey(productStoreBatch.getId());

                    // 来源货位库存变更记录
                    ProductInventoryChangeRecordDTO fromInventoryDTO = new ProductInventoryChangeRecordDTO();
                    fromInventoryDTO.setId(fromUid);
                    fromInventoryDTO.setProductStoreId("");
                    fromInventoryDTO.setCityId(cityId);
                    fromInventoryDTO.setJiupiEventType(JiupiEventType.自动校正.getType());
                    fromInventoryDTO.setCountMaxUnit(BigDecimal.ZERO);
                    fromInventoryDTO.setCountMinUnit(BigDecimal.ZERO);
                    fromInventoryDTO.setTotalCount(totalCount.negate());
                    fromInventoryDTO.setSourceTotalCount(BigDecimal.ZERO);
                    fromInventoryDTO.setDescription(InventoryChangeTypeEnum.货区库存转移.name());
                    fromInventoryDTO.setCreateTime(dtNow);
                    fromInventoryDTO.setStoreType(InventoryChangeTypes.WAREHOUSE);

                    productInventoryChangeRecordPOS.add(fromInventoryPO);
                    productInventoryChangeRecordDTOS.add(fromInventoryDTO);

                    // 目标货位加库存
                    ProductInventoryChangeRecordPO toInventoryPO = new ProductInventoryChangeRecordPO();
                    toInventoryPO.setCityId(cityId);
                    toInventoryPO.setJiupiEventType(JiupiEventType.自动校正.getType());
                    toInventoryPO.setLocationId(pickItem.getLocationId());
                    toInventoryPO.setBatchTime(productStoreBatch.getBatchTime());
                    toInventoryPO.setExpireTime(productStoreBatch.getExpireTime());
                    toInventoryPO.setProductionDate(productStoreBatch.getProductionDate());
                    toInventoryPO.setProductStoreId(productStoreBatch.getProductStoreId());
                    toInventoryPO.setTotalCount(totalCount);
                    toInventoryPO.setCreateTime(dtNow);
                    toInventoryPO.setBatchAttributeInfoNo(productStoreBatch.getBatchAttributeInfoNo());
                    String toUid = UUIDUtil.getUUID();
                    toInventoryPO.setId(toUid);
                    toInventoryPO.setDescription("货区库存转移");
                    toInventoryPO.setDistinctKey(productStoreBatch.getId());

                    // 目标货位库存变更记录
                    ProductInventoryChangeRecordDTO toInventoryDTO = new ProductInventoryChangeRecordDTO();
                    toInventoryDTO.setId(toUid);
                    toInventoryDTO.setProductStoreId("");
                    toInventoryDTO.setCityId(cityId);
                    toInventoryDTO.setJiupiEventType(JiupiEventType.自动校正.getType());
                    toInventoryDTO.setCountMaxUnit(BigDecimal.ZERO);
                    toInventoryDTO.setCountMinUnit(BigDecimal.ZERO);
                    toInventoryDTO.setTotalCount(totalCount);
                    toInventoryDTO.setSourceTotalCount(BigDecimal.ZERO);
                    toInventoryDTO.setDescription(InventoryChangeTypeEnum.货区库存转移.name());
                    toInventoryDTO.setCreateTime(dtNow);
                    toInventoryDTO.setStoreType(InventoryChangeTypes.WAREHOUSE);

                    productInventoryChangeRecordPOS.add(toInventoryPO);
                    productInventoryChangeRecordDTOS.add(toInventoryDTO);
                });

            });
        }
        return productInventoryChangeRecordPOS;
    }

    /**
     * 转移补货上限库存到指定货位
     */
    public void batchInventoryTransferNoOrder(BatchInventorySyncQueryDTO transferDTO) {
        LOGGER.info("转移补货上限库存到指定货位  入参:{}", JSON.toJSONString(transferDTO));

        Integer warehouseId = transferDTO.getWarehouseId();
        List<String> locationAreaList = transferDTO.getLocationAreaList();
        Long ownerId = transferDTO.getOwnerId();
        String toLocationName = transferDTO.getToLocationName();
        List<String> roadWayList = transferDTO.getRoadWayList();
        List<Integer> locationTypeList = transferDTO.getLocationTypeList();

        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(transferDTO.getWarehouseId());
        if (warehouse == null) {
            throw new BusinessValidateException("仓库不存在");
        }

        LoactionDTO toLocation = iLocationService.getLocationByName(warehouseId, toLocationName);
        LOGGER.info("根据货区查询货位信息 查询结果：{}", JSON.toJSONString(toLocation));
        if (toLocation == null) {
            throw new BusinessValidateException("找不到该货位，请检查货位名称是否正确");
        }

        // 2.5+ 通过货区locationAreaList 和 巷道roadWayList，获取关联货位的产品库存
        LocationProductQuery locationProductQuery = new LocationProductQuery();
        locationProductQuery.setWarehouseId(warehouseId);
        locationProductQuery.setLocationAreaList(locationAreaList);
        locationProductQuery.setRoadWayList(roadWayList);
        List<ProductLoactionSkuInfoDTO> skuInfoDTOS =
                iProductLocationService.findProductByCondition(locationProductQuery);
        if (CollectionUtils.isEmpty(skuInfoDTOS)) {
            throw new BusinessValidateException("指定货区和巷道没有关联产品数据");
        }

        BatchInventorySyncQueryDTO queryDTO = new BatchInventorySyncQueryDTO();
        List<Long> skuIdList = skuInfoDTOS.stream().filter(p -> p.getProductSkuId() != null)
                .map(ProductLoactionSkuInfoDTO::getProductSkuId).distinct().collect(Collectors.toList());
        queryDTO.setSkuIdList(skuIdList);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setLocationTypeList(locationTypeList);
        queryDTO.setOwnerId(ownerId);
        // 根据货区查询所有货位库存信息
        List<ProductStoreBatchPO> productStoreBatchPOS =
                batchInventoryProductStoreBatchMapper.findProductStoreBatchByCondition(queryDTO);
        LOGGER.info("根指定货区和巷道关联产品货位库存信息 查询结果：{}", JSON.toJSONString(productStoreBatchPOS));
        if (CollectionUtils.isEmpty(productStoreBatchPOS)) {
            throw new BusinessValidateException("所选货区库存数量等于0");
        }

        // 根据产品查询库存上限信息
        List<Long> skuIds = productStoreBatchPOS.stream().map(ProductStoreBatchPO::getProductSkuId).distinct()
                .collect(Collectors.toList());
        List<ProductSkuDTO> productSkuDTOS = productSkuQueryService.findSkuInfoWithConfig(warehouseId, skuIds);
        LOGGER.info("货区库存产品信息 查询结果：{}", JSON.toJSONString(productSkuDTOS));
        if (CollectionUtils.isEmpty(productSkuDTOS)) {
            throw new BusinessValidateException("所选货区库存产品信息不存在");
        }

        productSkuDTOS =
                productSkuDTOS.stream().filter(p -> p.getProductSkuId() != null && p.getMaxReplenishment() != null
                        && p.getMaxReplenishment().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productSkuDTOS)) {
            throw new BusinessValidateException("所选货区库存产品没有配置补货上限或补货上限为0");
        }
        List<Long> eixstMaxLimitSkuIds =
                productSkuDTOS.stream().map(ProductSkuDTO::getProductSkuId).distinct().collect(Collectors.toList());
        Map<Long, ProductSkuDTO> skuDTOMap = productSkuDTOS.stream()
                .collect(Collectors.toMap(ProductSkuDTO::getProductSkuId, Function.identity(), (key1, key2) -> key2));

        // 过滤产品不存在补货上限 或 货位库存数量小于等于0 的库存数据
        productStoreBatchPOS.removeIf(p -> !eixstMaxLimitSkuIds.contains(p.getProductSkuId())
                || p.getTotalCount().compareTo(BigDecimal.ZERO) <= 0);
        if (CollectionUtils.isEmpty(productStoreBatchPOS)) {
            throw new BusinessValidateException("所选货区库存小于0，或产品没有配置补货上限");
        }

        // 组装无单移库数据，移库数量 = 货位库存数量 - 补货上限
        List<StoreTransferOrderItemDTO> storeTransferOrderItemDTOS = new ArrayList<>();
        StoreTransferOrderDTO storeTransferOrderDTO = new StoreTransferOrderDTO();
        storeTransferOrderDTO.setOrg_id(warehouse.getCityId());
        storeTransferOrderDTO.setState(StoreTransferStateEnum.待移库.getType());
        storeTransferOrderDTO.setTransferType(StoreTransferEnum.人工移库.getType());
        storeTransferOrderDTO.setStartTime(new Date());
        storeTransferOrderDTO.setWarehouse_Id(warehouseId);
        storeTransferOrderDTO.setWarehouseName(warehouse.getName());
        storeTransferOrderDTO.setSorter_id(transferDTO.getUserId());
        storeTransferOrderDTO.setSorterName(transferDTO.getUserName());
        storeTransferOrderDTO.setRemark("转移补货上限库存");
        storeTransferOrderDTO.setCreateUser(transferDTO.getUserName());
        storeTransferOrderDTO.setLastupdateuser(transferDTO.getUserName());
        productStoreBatchPOS.stream().forEach(p -> {
            ProductSkuDTO productSkuDTO = skuDTOMap.get(p.getProductSkuId());
            if (productSkuDTO == null) {
                return;
            }

            // 获取产品配置补货上限
            BigDecimal maxReplenishment =
                    Objects.equals(productSkuDTO.getProductFeature(), ProductFeatureEnum.小件.getType())
                            ? productSkuDTO.getMaxReplenishment()
                            : productSkuDTO.getMaxReplenishment().multiply(productSkuDTO.getPackageQuantity());
            if (p.getTotalCount().compareTo(maxReplenishment) <= 0) {
                return;
            }

            BigDecimal[] remainder =
                    p.getTotalCount().subtract(maxReplenishment).divideAndRemainder(productSkuDTO.getPackageQuantity());
            BigDecimal packageCount = remainder[0];
            if (packageCount.compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }

            StoreTransferOrderItemDTO storeTransferOrderItemDTO = new StoreTransferOrderItemDTO();
            storeTransferOrderItemDTO.setOrg_id(warehouse.getCityId());
            storeTransferOrderItemDTO.setState(StoreTransferStateEnum.待移库.getType());
            storeTransferOrderItemDTO.setSkuId(productSkuDTO.getProductSkuId());
            storeTransferOrderItemDTO.setProductName(productSkuDTO.getName());
            storeTransferOrderItemDTO.setSpecName(productSkuDTO.getSpecificationName());
            storeTransferOrderItemDTO.setSpecQuantity(productSkuDTO.getPackageQuantity());
            storeTransferOrderItemDTO.setPackageName(productSkuDTO.getPackageName());
            storeTransferOrderItemDTO.setPackageCount(packageCount);
            storeTransferOrderItemDTO.setUnitName(productSkuDTO.getUnitName());
            storeTransferOrderItemDTO.setUnitCount(BigDecimal.ZERO);
            storeTransferOrderItemDTO.setUnitTotalCount(packageCount.multiply(productSkuDTO.getPackageQuantity()));
            storeTransferOrderItemDTO.setOverMovePackageCount(packageCount);
            storeTransferOrderItemDTO.setOverMoveUnitCount(BigDecimal.ZERO);
            storeTransferOrderItemDTO.setFromLocation_id(p.getLocationId());
            storeTransferOrderItemDTO.setFromLocationName(p.getLocationName());
            storeTransferOrderItemDTO.setToLocation_id(toLocation.getId());
            storeTransferOrderItemDTO.setToLocationName(toLocationName);
            storeTransferOrderItemDTO.setCreateUser(transferDTO.getUserName());
            storeTransferOrderItemDTO.setLastupdateuser(transferDTO.getUserName());
            storeTransferOrderItemDTO.setOwner_id(p.getOwnerId());
            storeTransferOrderItemDTO.setProductionDate(p.getProductionDate());
            storeTransferOrderItemDTO.setBatchTime(p.getBatchTime());
            storeTransferOrderItemDTO.setProductSpecificationId(p.getProductSpecificationId());
            storeTransferOrderItemDTO.setSecOwnerId(p.getSecOwnerId());
            storeTransferOrderItemDTO.setBatchAttributeInfoNo(p.getBatchAttributeInfoNo());
            // storeTransferOrderItemDTO.setToChannel("");
            // storeTransferOrderItemDTO.setOwnerName();
            storeTransferOrderItemDTOS.add(storeTransferOrderItemDTO);
        });
        storeTransferOrderDTO.setStoreTransferOrderItemDTOS(storeTransferOrderItemDTOS);
        LOGGER.info("转移补货上限库存到指定货位 移库组装数据：{}", JSON.toJSONString(storeTransferOrderDTO));

        if (CollectionUtils.isEmpty(storeTransferOrderDTO.getStoreTransferOrderItemDTOS())) {
            throw new BusinessValidateException("没有符合条件的库存需要转移");
        }

        iStoreTransferOrderService.updateStoreTranferNoOrder(storeTransferOrderDTO);
    }

    /**
     * 指定货位/货区间库存转移 (目前仅支持2.5+)
     */
    public void batchInventoryMove(BatchInventoryMoveDTO moveDTO) {
        LOGGER.info("指定货位/货区间库存转移，入参:{}", JSON.toJSONString(moveDTO));
        AssertUtils.notNull(moveDTO, "参数不能为空");
        AssertUtils.notNull(moveDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(moveDTO.getFromLocationTypes(), "来源货区类型不能为空");
        AssertUtils.notNull(moveDTO.getSkuId(), "skuid不能为空");
        AssertUtils.notNull(moveDTO.getToLocationId(), "目的货位不能为空");
        AssertUtils.notNull(moveDTO.getMoveCount(), "移动数量不能为空");

        Integer warehouseId = moveDTO.getWarehouseId();
        Long skuId = moveDTO.getSkuId();
        Long ownerId = moveDTO.getOwnerId();
        List<Integer> locationTypeList = moveDTO.getFromLocationTypes();
        Long toLocationId = moveDTO.getToLocationId();

        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(warehouseId);
        if (warehouse == null) {
            throw new BusinessValidateException("仓库不存在");
        }

        List<LoactionDTO> toLocationList = iLocationService.findLocationByIds(Arrays.asList(toLocationId));
        LOGGER.info("指定货位/货区间库存转移 货位查询结果：{}", JSON.toJSONString(toLocationList));
        if (CollectionUtils.isEmpty(toLocationList)) {
            throw new BusinessValidateException("找不到该货位，请检查货位名称是否正确");
        }
        LoactionDTO toLocation = toLocationList.get(0);

        BatchInventorySyncQueryDTO queryDTO = new BatchInventorySyncQueryDTO();
        queryDTO.setSkuIdList(Arrays.asList(skuId));
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setLocationTypeList(locationTypeList);
        queryDTO.setOwnerId(ownerId);
        // 根据货区查询所有货位库存信息
        List<ProductStoreBatchPO> productStoreBatchPOS =
                batchInventoryProductStoreBatchMapper.findProductStoreBatchByCondition(queryDTO);
        LOGGER.info("指定货位/货区间库存转移 货位库存查询结果：{}", JSON.toJSONString(productStoreBatchPOS));
        if (CollectionUtils.isEmpty(productStoreBatchPOS)) {
            throw new BusinessValidateException("所选货区库存数量等于0");
        }

        ProductSkuDTO productSkuDTO = productSkuQueryService.selectBySkuId(skuId);
        LOGGER.info("指定货位/货区间库存转移 产品查询结果：{}", JSON.toJSONString(productSkuDTO));
        if (productSkuDTO == null) {
            throw new BusinessValidateException("所选货区库存产品信息不存在");
        }

        // 组装无单移库数据
        List<StoreTransferOrderItemDTO> storeTransferOrderItemDTOS = new ArrayList<>();
        StoreTransferOrderDTO storeTransferOrderDTO = new StoreTransferOrderDTO();
        storeTransferOrderDTO.setOrg_id(warehouse.getCityId());
        storeTransferOrderDTO.setState(StoreTransferStateEnum.待移库.getType());
        storeTransferOrderDTO.setTransferType(StoreTransferEnum.人工移库.getType());
        storeTransferOrderDTO.setStartTime(new Date());
        storeTransferOrderDTO.setWarehouse_Id(warehouseId);
        storeTransferOrderDTO.setWarehouseName(warehouse.getName());
        storeTransferOrderDTO.setSorter_id(moveDTO.getUserId());
        storeTransferOrderDTO.setSorterName(moveDTO.getUserName());
        storeTransferOrderDTO.setRemark("转移关联货位库存");
        storeTransferOrderDTO.setCreateUser(moveDTO.getUserName());
        storeTransferOrderDTO.setLastupdateuser(moveDTO.getUserName());

        // 移动小单位总数
        BigDecimal moveUnitTotalCount = moveDTO.getMoveCount();
        for (ProductStoreBatchPO p : productStoreBatchPOS) {
            if (moveUnitTotalCount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            BigDecimal moveStoreCount = BigDecimal.ZERO;
            BigDecimal fromStoreTotalCount = p.getTotalCount();
            if (fromStoreTotalCount.compareTo(moveUnitTotalCount) < 0) {
                moveStoreCount = fromStoreTotalCount;
                moveUnitTotalCount = moveUnitTotalCount.subtract(fromStoreTotalCount);
            } else {
                moveStoreCount = moveUnitTotalCount;
                moveUnitTotalCount = BigDecimal.ZERO;
            }

            BigDecimal[] remainder = moveStoreCount.divideAndRemainder(productSkuDTO.getPackageQuantity());
            BigDecimal packageCount = remainder[0];
            BigDecimal unitCount = remainder[1];

            StoreTransferOrderItemDTO storeTransferOrderItemDTO = new StoreTransferOrderItemDTO();
            storeTransferOrderItemDTO.setOrg_id(warehouse.getCityId());
            storeTransferOrderItemDTO.setState(StoreTransferStateEnum.待移库.getType());
            storeTransferOrderItemDTO.setSkuId(productSkuDTO.getProductSkuId());
            storeTransferOrderItemDTO.setProductName(productSkuDTO.getName());
            storeTransferOrderItemDTO.setSpecName(productSkuDTO.getSpecificationName());
            storeTransferOrderItemDTO.setSpecQuantity(productSkuDTO.getPackageQuantity());
            storeTransferOrderItemDTO.setPackageName(productSkuDTO.getPackageName());
            storeTransferOrderItemDTO.setPackageCount(packageCount);
            storeTransferOrderItemDTO.setUnitCount(unitCount);
            storeTransferOrderItemDTO.setUnitName(productSkuDTO.getUnitName());
            storeTransferOrderItemDTO.setUnitCount(BigDecimal.ZERO);
            storeTransferOrderItemDTO.setUnitTotalCount(moveStoreCount);
            storeTransferOrderItemDTO.setOverMovePackageCount(packageCount);
            storeTransferOrderItemDTO.setOverMoveUnitCount(unitCount);
            storeTransferOrderItemDTO.setFromLocation_id(p.getLocationId());
            storeTransferOrderItemDTO.setFromLocationName(p.getLocationName());
            storeTransferOrderItemDTO.setToLocation_id(toLocation.getId());
            storeTransferOrderItemDTO.setToLocationName(toLocation.getName());
            storeTransferOrderItemDTO.setCreateUser(moveDTO.getUserName());
            storeTransferOrderItemDTO.setLastupdateuser(moveDTO.getUserName());
            storeTransferOrderItemDTO.setOwner_id(p.getOwnerId());
            storeTransferOrderItemDTO.setProductionDate(p.getProductionDate());
            storeTransferOrderItemDTO.setBatchTime(p.getBatchTime());
            storeTransferOrderItemDTO.setProductSpecificationId(p.getProductSpecificationId());
            storeTransferOrderItemDTO.setSecOwnerId(p.getSecOwnerId());
            storeTransferOrderItemDTO.setBatchAttributeInfoNo(p.getBatchAttributeInfoNo());
            // storeTransferOrderItemDTO.setToChannel("");
            // storeTransferOrderItemDTO.setOwnerName();
            storeTransferOrderItemDTOS.add(storeTransferOrderItemDTO);
        }

        storeTransferOrderDTO.setStoreTransferOrderItemDTOS(storeTransferOrderItemDTOS);
        LOGGER.info("指定货位/货区间库存转移 移库组装数据：{}", JSON.toJSONString(storeTransferOrderDTO));

        if (CollectionUtils.isEmpty(storeTransferOrderDTO.getStoreTransferOrderItemDTOS())) {
            throw new BusinessValidateException("没有符合条件的库存需要转移");
        }

        iStoreTransferOrderService.updateStoreTranferNoOrder(storeTransferOrderDTO);
    }

    /**
     * 清除负货位库存
     *
     * @param batchInventorySyncQueryDTO
     */
    public BatchInventoryClearNegativeLogicDTO
    clearBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        // 清除总库存为0的货位库存不为0的项
        clearZeroBatchInventoryByWarehouseId(batchInventorySyncQueryDTO.getWarehouseId());
        // 清除负货位库存
        return clearBatchInventoryByProductLocaion(batchInventorySyncQueryDTO);
    }
}
