package com.yijiupi.himalaya.supplychain.dto.erp;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-10-18 10:32
 **/
public class ErpPurchaseReturnOutStockDTO implements Serializable {
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 产品信息规格id
     */
    private Long productSpecId;
    /**
     * skuId
     */
    private Long productSkuId;
    /**
     * 所属人ID
     */
    private Long ownerId;
    /**
     * 二级货主Id
     */
    private String secOwnerId;
    /**
     * 仓库库存按照小单位换算的数量(文本)
     */
    private BigDecimal warehouseTotalCount;

    /**
     * 已退数量中含处理品的数量
     */
    private BigDecimal includeDisposalCount;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(String secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public BigDecimal getWarehouseTotalCount() {
        return warehouseTotalCount;
    }

    public void setWarehouseTotalCount(BigDecimal warehouseTotalCount) {
        this.warehouseTotalCount = warehouseTotalCount;
    }

    public BigDecimal getIncludeDisposalCount() {
        return includeDisposalCount;
    }

    public void setIncludeDisposalCount(BigDecimal includeDisposalCount) {
        this.includeDisposalCount = includeDisposalCount;
    }
}
