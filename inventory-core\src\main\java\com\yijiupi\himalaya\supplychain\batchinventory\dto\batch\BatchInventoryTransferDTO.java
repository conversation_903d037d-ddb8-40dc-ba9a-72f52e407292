package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 批次库存转移dto
 *
 * <AUTHOR> 2018/3/31
 */
public class BatchInventoryTransferDTO implements Serializable {
    /**
     * 批次库存主键id
     */
    private String storeBatchId;
    /**
     * 产品库存ID
     */
    private String productStoreId;
    /**
     * 被转移的大单位数量
     */
    private BigDecimal packageCount = BigDecimal.ZERO;
    /**
     * 被转移的小单位数量.
     */
    private BigDecimal unitCount = BigDecimal.ZERO;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity = BigDecimal.ZERO;
    /**
     * 来源货位id
     */
    private Long fromLocationId;
    /**
     * 目标货位id
     */
    private Long locationId;
    /**
     * 目标货位名称
     */
    private String locationName;
    /**
     * 目标'货区或货位类型：0:货位，1:货区',
     */
    private Integer locationCategory;
    /**
     * 目标货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    private Integer subcategory;
    /**
     * 来源渠道
     */
    private Integer fromChannel;
    /**
     * 目标渠道
     */
    private Integer toChannel;
    /**
     * 批次时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;
    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 批次属性
     */
    private Byte batchProperty;

    /**
     * 业务类型
     */
    private Byte businessType;

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    /**
     * 获取 批次库存主键id
     */
    public String getStoreBatchId() {
        return this.storeBatchId;
    }

    /**
     * 设置 批次库存主键id
     */
    public void setStoreBatchId(String storeBatchId) {
        this.storeBatchId = storeBatchId;
    }

    /**
     * 获取 产品库存ID
     */
    public String getProductStoreId() {
        return this.productStoreId;
    }

    /**
     * 设置 产品库存ID
     */
    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    /**
     * 获取 被转移的大单位数量
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 被转移的大单位数量
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 被转移的小单位数量.
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 被转移的小单位数量.
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    /**
     * 获取 包装规格大小单位转换系数
     */
    public BigDecimal getPackageQuantity() {
        return this.packageQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     */
    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 来源货位id
     */
    public Long getFromLocationId() {
        return this.fromLocationId;
    }

    /**
     * 设置 来源货位id
     */
    public void setFromLocationId(Long fromLocationId) {
        this.fromLocationId = fromLocationId;
    }

    /**
     * 获取 目标货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 目标货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 目标货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 目标货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 目标'货区或货位类型：0:货位，1:货区',
     */
    public Integer getLocationCategory() {
        return this.locationCategory;
    }

    /**
     * 设置 目标'货区或货位类型：0:货位，1:货区',
     */
    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    /**
     * 目标货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    public Integer getSubcategory() {
        return this.subcategory;
    }

    /**
     * 目标货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    public void setSubcategory(Integer subcategory) {
        this.subcategory = subcategory;
    }

    /**
     * 获取 来源渠道
     */
    public Integer getFromChannel() {
        return this.fromChannel;
    }

    /**
     * 设置 来源渠道
     */
    public void setFromChannel(Integer fromChannel) {
        this.fromChannel = fromChannel;
    }

    /**
     * 获取 目标渠道
     */
    public Integer getToChannel() {
        return this.toChannel;
    }

    /**
     * 设置 目标渠道
     */
    public void setToChannel(Integer toChannel) {
        this.toChannel = toChannel;
    }

    public Byte getBatchProperty() {
        return batchProperty;
    }

    public void setBatchProperty(Byte batchProperty) {
        this.batchProperty = batchProperty;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }
}
