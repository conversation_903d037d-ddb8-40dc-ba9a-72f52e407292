package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import java.io.Serializable;
import java.util.List;

public class BatchAttributeValueSaveDTO implements Serializable {

    private static final long serialVersionUID = 2857748644560424637L;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 产品库存不存在时是否创建库存,默认true true - 创建 false - 不创建
     */
    private Boolean createInventory = true;

    /**
     * 批属性信息集合
     */
    private List<BatchAttributeValueSaveItemDTO> itemList;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Boolean getCreateInventory() {
        return createInventory;
    }

    public void setCreateInventory(Boolean createInventory) {
        this.createInventory = createInventory;
    }

    public List<BatchAttributeValueSaveItemDTO> getItemList() {
        return itemList;
    }

    public void setItemList(List<BatchAttributeValueSaveItemDTO> itemList) {
        this.itemList = itemList;
    }
}
