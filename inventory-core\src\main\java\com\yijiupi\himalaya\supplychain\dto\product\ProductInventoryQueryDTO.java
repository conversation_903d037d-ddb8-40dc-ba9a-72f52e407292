package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.util.Map;

public class ProductInventoryQueryDTO implements Serializable {
    /**
     * 规格-sku集合 key : 规格ID value : skuId
     */
    private Map<Long, Long> specSkuMap;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 货主ID
     */
    private Long ownerId;

    /**
     * 渠道：0-酒批，1,-大宗
     */
    private Integer channel;

    /**
     * 二级货主
     */
    private Long secOwnerId;

    /**
     * 产品库存不存在时是否创建库存,默认true true - 创建 false - 不创建
     */
    private Boolean createInventory = true;

    public Map<Long, Long> getSpecSkuMap() {
        return specSkuMap;
    }

    public void setSpecSkuMap(Map<Long, Long> specSkuMap) {
        this.specSkuMap = specSkuMap;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Boolean getCreateInventory() {
        return createInventory;
    }

    public void setCreateInventory(Boolean createInventory) {
        this.createInventory = createInventory;
    }
}
