package com.yijiupi.himalaya.supplychain.dto.standard;

import java.io.Serializable;
import java.math.BigDecimal;

public class ProductCityWarehouseStoreDTO implements Serializable {

    /**
     */
    private static final long serialVersionUID = 1L;
    /**
     * 城市ID
     */
    private Integer cityId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 仓库总库存
     */
    private BigDecimal storeTotalCount;
    /**
     * 渠道
     */
    private Integer channel;
    /**
     * 发货中数量
     */
    private BigDecimal deliveryedCount;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 产品来源
     */
    private Integer source;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getStoreTotalCount() {
        return storeTotalCount;
    }

    public void setStoreTotalCount(BigDecimal storeTotalCount) {
        this.storeTotalCount = storeTotalCount;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public BigDecimal getDeliveryedCount() {
        return deliveryedCount;
    }

    public void setDeliveryedCount(BigDecimal deliveryedCount) {
        this.deliveryedCount = deliveryedCount;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }
}
