package com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025-03-14 11:44
 **/
public class ProductionDateAuditDTO implements Serializable {

    /**
     * 主键 id
     */
    private Long id;

    /**
     * 仓库 id
     */
    private Integer warehouseId;

    /**
     * 仓库名称, 需要手动设置, 给前端展示用
     */
    private String warehouseName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品 skuId
     */
    private Long skuId;

    /**
     * 包装规格名称
     */
    private String packageSpecName;

    /**
     * 批次库存小件总数
     */
    private BigDecimal unitTotalCount;

    /**
     * 保质期
     */
    private Integer shelfLife;

    /**
     * 保质期单位, 1=年 2=月 3=日
     */
    private Integer shelfLifeUnit;

    /**
     * 生产日期
     */
    private String productionDate;

    /**
     * 批次库存数量
     */
    private String batchInventoryCount;

    /**
     * 关联任务编号
     */
    private String refTodoTaskNo;

    /**
     * 关联任务 id
     */
    private Long refTodoTaskId;

    /**
     * 申请人
     */
    private String applyUser;

    /**
     * 申请人电话
     */
    private String applyUserMobile;

    /**
     * 审核状态, 0=审核中 1=审核通过 2=审核拒绝
     */
    private Integer state;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 操作人 id
     */
    private Integer userId;

    /**
     * 是否审核通过
     */
    private Boolean isApplyPassed;

    /**
     * 业务类型: 0：无，1：临期促销
     */
    private Byte businessType;

    /**
     * 关注期, 这里是分数, 类似 1/2
     */
    private String attentionPeriod;

    /**
     * 临期, 这里是分数, 类似 1/2
     */
    private String nearExpiryPeriod;

    /**
     * 禁止销售期, 距离过期日期的天数
     */
    private String forbidSalesPeriod;

    public String getAttentionPeriod() {
        return attentionPeriod;
    }

    public void setAttentionPeriod(String attentionPeriod) {
        this.attentionPeriod = attentionPeriod;
    }

    public String getNearExpiryPeriod() {
        return nearExpiryPeriod;
    }

    public void setNearExpiryPeriod(String nearExpiryPeriod) {
        this.nearExpiryPeriod = nearExpiryPeriod;
    }

    public String getForbidSalesPeriod() {
        return forbidSalesPeriod;
    }

    public void setForbidSalesPeriod(String forbidSalesPeriod) {
        this.forbidSalesPeriod = forbidSalesPeriod;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getPackageSpecName() {
        return packageSpecName;
    }

    public void setPackageSpecName(String packageSpecName) {
        this.packageSpecName = packageSpecName;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Integer getShelfLife() {
        return shelfLife;
    }

    public void setShelfLife(Integer shelfLife) {
        this.shelfLife = shelfLife;
    }

    public Integer getShelfLifeUnit() {
        return shelfLifeUnit;
    }

    public void setShelfLifeUnit(Integer shelfLifeUnit) {
        this.shelfLifeUnit = shelfLifeUnit;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(String productionDate) {
        this.productionDate = productionDate;
    }

    public String getBatchInventoryCount() {
        return batchInventoryCount;
    }

    public void setBatchInventoryCount(String batchInventoryCount) {
        this.batchInventoryCount = batchInventoryCount;
    }

    public String getRefTodoTaskNo() {
        return refTodoTaskNo;
    }

    public void setRefTodoTaskNo(String refTodoTaskNo) {
        this.refTodoTaskNo = refTodoTaskNo;
    }

    public Long getRefTodoTaskId() {
        return refTodoTaskId;
    }

    public void setRefTodoTaskId(Long refTodoTaskId) {
        this.refTodoTaskId = refTodoTaskId;
    }

    public String getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser;
    }

    public String getApplyUserMobile() {
        return applyUserMobile;
    }

    public void setApplyUserMobile(String applyUserMobile) {
        this.applyUserMobile = applyUserMobile;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Boolean getApplyPassed() {
        return isApplyPassed;
    }

    public void setApplyPassed(Boolean applyPassed) {
        isApplyPassed = applyPassed;
    }

    @Override
    public String toString() {
        return "ProductionDateAuditDTO{" +
               "id=" + id +
               ", warehouseId=" + warehouseId +
               ", warehouseName='" + warehouseName + '\'' +
               ", productName='" + productName + '\'' +
               ", skuId=" + skuId +
               ", packageSpecName='" + packageSpecName + '\'' +
               ", unitTotalCount=" + unitTotalCount +
               ", shelfLife=" + shelfLife +
               ", shelfLifeUnit=" + shelfLifeUnit +
               ", productionDate='" + productionDate + '\'' +
               ", batchInventoryCount='" + batchInventoryCount + '\'' +
               ", refTodoTaskNo='" + refTodoTaskNo + '\'' +
               ", refTodoTaskId=" + refTodoTaskId +
               ", applyUser='" + applyUser + '\'' +
               ", applyUserMobile='" + applyUserMobile + '\'' +
               ", state=" + state +
               ", auditRemark='" + auditRemark + '\'' +
               ", userId=" + userId +
               ", isApplyPassed=" + isApplyPassed +
               '}';
    }
}
