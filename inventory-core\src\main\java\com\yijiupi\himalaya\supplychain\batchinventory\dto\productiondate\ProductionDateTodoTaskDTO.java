package com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-03-17 13:49
 **/
public class ProductionDateTodoTaskDTO implements Serializable {
    /**
     * 主键 id
     */
    private Long id;

    /**
     * 任务编号
     */
    private String taskNo;

    /**
     * 任务类型 id
     */
    private Long taskTypeId;

    /**
     * 任务类型名称
     */
    private String taskTypeName;

    /**
     * 任务状态, 0=待处理, 1=处理中, 2=已完成
     */
    private Integer taskStatus;

    /**
     * 任务优先级 0=高, 1=中, 2=低
     */
    private Integer taskRank;

    /**
     * 是否已删除 0=未删除; 1=已删除
     */
    private Integer isDeleted;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 任务详情
     */
    private String taskDetail;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 逾期时间
     */
    private Date overdueTime;

    /**
     * 完成人员
     */
    private Integer finishUser;

    /**
     * 完成人员 用户名
     */
    private String finishUserName;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 是否逾期, 0=未逾期, 1=逾期
     */
    private Integer isOverdue;

    /**
     * 任务属性, 0=未指定、1=酒饮、2=休食
     */
    private Integer taskProperty;

    /**
     * 任务负责人, 创建时设置了分仓属性的任务才会有值
     */
    private String director;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public Long getTaskTypeId() {
        return taskTypeId;
    }

    public void setTaskTypeId(Long taskTypeId) {
        this.taskTypeId = taskTypeId;
    }

    public String getTaskTypeName() {
        return taskTypeName;
    }

    public void setTaskTypeName(String taskTypeName) {
        this.taskTypeName = taskTypeName;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Integer getTaskRank() {
        return taskRank;
    }

    public void setTaskRank(Integer taskRank) {
        this.taskRank = taskRank;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public String getTaskDetail() {
        return taskDetail;
    }

    public void setTaskDetail(String taskDetail) {
        this.taskDetail = taskDetail;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getOverdueTime() {
        return overdueTime;
    }

    public void setOverdueTime(Date overdueTime) {
        this.overdueTime = overdueTime;
    }

    public Integer getFinishUser() {
        return finishUser;
    }

    public void setFinishUser(Integer finishUser) {
        this.finishUser = finishUser;
    }

    public String getFinishUserName() {
        return finishUserName;
    }

    public void setFinishUserName(String finishUserName) {
        this.finishUserName = finishUserName;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(Integer isOverdue) {
        this.isOverdue = isOverdue;
    }

    public Integer getTaskProperty() {
        return taskProperty;
    }

    public void setTaskProperty(Integer taskProperty) {
        this.taskProperty = taskProperty;
    }

    public String getDirector() {
        return director;
    }

    public void setDirector(String director) {
        this.director = director;
    }

    @Override
    public String toString() {
        return "ProductionDateTodoTaskDTO{" +
               "id=" + id +
               ", taskNo='" + taskNo + '\'' +
               ", taskTypeId=" + taskTypeId +
               ", taskTypeName='" + taskTypeName + '\'' +
               ", taskStatus=" + taskStatus +
               ", taskRank=" + taskRank +
               ", isDeleted=" + isDeleted +
               ", businessNo='" + businessNo + '\'' +
               ", taskDetail='" + taskDetail + '\'' +
               ", beginTime=" + beginTime +
               ", overdueTime=" + overdueTime +
               ", finishUser=" + finishUser +
               ", finishUserName='" + finishUserName + '\'' +
               ", finishTime=" + finishTime +
               ", createTime=" + createTime +
               ", lastUpdateTime=" + lastUpdateTime +
               ", isOverdue=" + isOverdue +
               ", taskProperty=" + taskProperty +
               ", director='" + director + '\'' +
               '}';
    }
}
