package com.yijiupi.himalaya.supplychain.batchinventory.service;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeRuleDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeRuleQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeRuleReturnDTO;

/**
 * 批属性配置管理
 *
 * <AUTHOR> 2018/4/9
 */
public interface IBatchAttributeRuleService {

    /**
     * 新增
     *
     * @param batchAttributeRuleDTO
     */
    void addBatchAttributeRule(BatchAttributeRuleDTO batchAttributeRuleDTO);

    /**
     * 编辑
     *
     * @param batchAttributeRuleDTO
     */
    void updateBatchAttributeRule(BatchAttributeRuleDTO batchAttributeRuleDTO);

    /**
     * 删除
     */
    void deleteById(Long id);

    /**
     * 列表
     */
    PageList<BatchAttributeRuleReturnDTO>
        findBatchAttributeRuleList(BatchAttributeRuleQueryDTO batchAttributeRuleQueryDTO);
}
