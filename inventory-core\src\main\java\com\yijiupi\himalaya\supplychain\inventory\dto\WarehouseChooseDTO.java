package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * @author: lidengfeng
 * @date 2018/10/12 9:48
 */
public class WarehouseChooseDTO implements Serializable {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 经销商id
     */
    private Long dealerId;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 仓库类型
     */
    private Integer warehouseChooseType;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页数
     */
    private Integer pageSize;

    /**
     * 用户业务类型（0：非联营 1：联营）
     */
    private Integer userBuinessType;

    /**
     * 获取 店铺id
     * 
     * @return
     */
    public Long getShopId() {
        return shopId;
    }

    /**
     * 设置 店铺id
     * 
     * @param shopId
     */
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public Long getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(Long dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    /**
     * 获取 城市id
     * 
     * @return
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市id
     * 
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库类型
     * 
     * @return
     */
    public Integer getWarehouseChooseType() {
        return warehouseChooseType;
    }

    /**
     * 设置 仓库类型
     * 
     * @param warehouseChooseType
     */
    public void setWarehouseChooseType(Integer warehouseChooseType) {
        this.warehouseChooseType = warehouseChooseType;
    }

    /**
     * 获取 页数
     * 
     * @return
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 设置 页数
     * 
     * @param pageNum
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 获取 每页的数量
     * 
     * @return
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 设置每页的数量
     * 
     * @param pageSize
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 获取 城市名称
     * 
     * @return
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置 城市名称
     * 
     * @param city
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 获取业务类型
     * 
     * @return
     */
    public Integer getUserBuinessType() {
        return userBuinessType;
    }

    /**
     * 设置 业务类型
     * 
     * @param userBuinessType
     */
    public void setUserBuinessType(Integer userBuinessType) {
        this.userBuinessType = userBuinessType;
    }
}
