package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import java.io.Serializable;

/**
 * 查询该类目和仓库绑定的批属性
 *
 * <AUTHOR> 2018/4/13
 */
public class BatchAttributeEnableQueryDTO implements Serializable {
    /**
     * 类目
     */
    private String category;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 获取 类目
     */
    public String getCategory() {
        return this.category;
    }

    /**
     * 设置 类目
     */
    public void setCategory(String category) {
        this.category = category;
    }

    /**
     * 获取 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
