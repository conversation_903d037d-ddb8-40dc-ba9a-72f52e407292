package com.yijiupi.himalaya.supplychain.enums;

/**
 * 库存变更展示名称
 */
public class StoreOrderType {
    public static final int 订单 = 0;
    public static final int 合作商订单 = 1;
    public static final int 退货单 = 2;
    public static final int 采购入库单 = 3;
    public static final int 采购退货单 = 4;
    public static final int 销售出库单 = 5;
    public static final int 退货入库单 = 6;
    public static final int 库存盘点单 = 7;
    public static final int 破损出库单 = 8;
    public static final int 其他出库单 = 9;
    public static final int 物料调拨单 = 10;
    public static final int 兑奖单 = 11;
    public static final int 易经销配送单 = 12;
    public static final int 易经销出库单 = 13;
    public static final int 处理品转入 = 15;
    public static final int 处理品转出 = 16;
    public static final int 盘点单 = 17;
    public static final int 其他入库单 = 18;
    public static final int 易酒批零团购订单 = 24;
    public static final int 兑奖配送单 = 25;
    public static final int 临期产品订单 = 30;
    public static final int 大商转配送 = 31;
    public static final int 轻加盟订单 = 32;
    public static final int 经销商订单 = 33;
    public static final int 团购直营 = 34;
    public static final int 拼团订单 = 35;
    public static final int 第三方入库 = 50;
    public static final int 第三方出库 = 51;
    public static final int 同城调拨转入 = 52;
    public static final int 同城调拨转出 = 53;
    public static final int 知花知果加工单 = 80;
    public static final int 知花知果订单 = 81;
    public static final int 矫正库存 = 98;
    public static final int ERP手动处理 = 99;
    public static final int 陈列品转入 = 61;
    public static final int 陈列品转出 = 58;

    /**
     * OP手动处理(100)
     */
    public static final int OP_MANUAL_ORDER = 100;
    /**
     * 第三方出库
     */
    public static final int TRANSFER_OUT_ORDER = 101;
    /**
     * 第三方入库
     */
    public static final int TRANSFER_IN_ORDER = 102;
    /**
     * 微酒出库
     */
    public static final int WJ_TRANSFER_OUT_ORDER = 103;
    /**
     * 微酒入库
     */
    public static final int WJ_TRANSFER_IN_ORDER = 104;

    public static final int 调拨出库 = 105;

    // 易款便利线下单
    public static final int 易款便利线下单 = 116;

    // 易款便利线下退货单
    public static final int 易款便利线下退货单 = 118;

    // 移库单
    public static final int 移库单 = 119;

    // 实物兑奖出库单
    public static final int 实物兑奖出库单 = 41;
}
