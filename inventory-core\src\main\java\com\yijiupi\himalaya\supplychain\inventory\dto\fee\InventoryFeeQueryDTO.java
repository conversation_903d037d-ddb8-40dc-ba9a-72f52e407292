package com.yijiupi.himalaya.supplychain.inventory.dto.fee;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 配送费查询,托管费查询DTO
 * 
 * <AUTHOR> 2018/2/2
 */
public class InventoryFeeQueryDTO implements Serializable {
    /**
     * skuId
     */
    private Long productSkuId;
    /**
     * 产品来源
     */
    private Integer source;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 渠道
     */
    private Integer channel;
    /**
     * 二级货主
     */
    private Long secOwnerId;
    /**
     * 数量
     */
    private BigDecimal count;
    /**
     * 提货时间
     */
    private Date getGoodsTime;

    /**
     * 货主Id
     */
    private Long ownerId;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 skuId
     *
     * @return productSkuId skuId
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 skuId
     *
     * @param productSkuId skuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 产品来源
     *
     * @return source 产品来源
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源
     *
     * @param source 产品来源
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 数量
     *
     * @return count 数量
     */
    public BigDecimal getCount() {
        return this.count;
    }

    /**
     * 设置 数量
     *
     * @param count 数量
     */
    public void setCount(BigDecimal count) {
        this.count = count;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 渠道
     *
     * @return channel 渠道
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 渠道
     *
     * @param channel 渠道
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 二级货主
     *
     * @return secOwnerId 二级货主
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主
     *
     * @param secOwnerId 二级货主
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取 提货时间
     *
     * @return getGoodsTime 提货时间
     */
    public Date getGetGoodsTime() {
        return this.getGoodsTime;
    }

    /**
     * 设置 提货时间
     *
     * @param getGoodsTime 提货时间
     */
    public void setGetGoodsTime(Date getGoodsTime) {
        this.getGoodsTime = getGoodsTime;
    }

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }
}
