package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;

/**
 * 移库变更记录
 *
 * <AUTHOR>
 * @date 2018/12/20 16:06
 */
public class PickUpChangeRecordDTO implements Serializable {
    private static final long serialVersionUID = 4041232706481251386L;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 单据id
     */
    private String orderId;

    /**
     * 单据编号
     */
    private String orderNo;

    /**
     * 单据类型
     */
    private Integer orderType;

    /**
     * 酒批事件类型
     */
    private Integer jiupiEventType;

    /**
     * 变更描述
     */
    private String description;

    /**
     * 操作人
     */
    private String createUser;

    /**
     * 库存属性（0：默认，1：自动转入
     */
    private Byte batchProperty;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    public Byte getBatchProperty() {
        return batchProperty;
    }

    public void setBatchProperty(Byte batchProperty) {
        this.batchProperty = batchProperty;
    }
}
