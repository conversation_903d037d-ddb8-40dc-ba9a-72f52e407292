package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 经销商仓配 申请入库产品及费用
 * 
 * @author: lidengfeng
 * @date 2018/9/29 14:03
 */
public class ProductStoreStockDTO implements Serializable {

    /**
     * 经销商id
     */
    private String dealerId;

    /**
     * 产品规格参数id
     */
    private Long productSpecificationId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 品牌
     */
    private String productBrand;

    /**
     * 业务城市
     */
    private String businessCity;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 经销商手机号
     */
    private String mobileNo;

    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 是否收取仓配费用
     */
    private Byte isGetWarehouseCharge;

    /**
     * 业务类型 0=正常业务 1=贷款业务
     */
    private Byte businessType;

    /**
     * 0=待设置产品费用，1=已设置产品费用
     */
    private Byte status;

    /**
     * 下车费
     */
    private BigDecimal unloadingCharge = BigDecimal.ZERO;

    /**
     * 分拣费
     */
    private BigDecimal sortingCharge = BigDecimal.ZERO;

    /**
     * 托管费
     */
    private BigDecimal custodianCharge = BigDecimal.ZERO;

    /**
     * 装车费
     */
    private BigDecimal loadingCharge = BigDecimal.ZERO;

    /**
     * 运输费
     */
    private BigDecimal transportCharge = BigDecimal.ZERO;

    /**
     * 卸货费
     */
    private BigDecimal landingCharge = BigDecimal.ZERO;

    /**
     * 首次入库时间
     */
    private String firstInStockTime;

    /**
     * 服务商id
     */
    private String facilitatorId;

    /**
     * 经销商名称
     */
    private String dealerName;

    /**
     * 仓库使用费
     */
    private BigDecimal warehouseCharge = BigDecimal.ZERO;

    /**
     * 平台使用费(按件)
     */
    private BigDecimal platFormCharge = BigDecimal.ZERO;

    /**
     * 平台使用费(按百分比)
     */
    private BigDecimal platFormFee = BigDecimal.ZERO;

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public String getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(String dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取 产品规格参数id
     * 
     * @return
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 产品规格参数id
     * 
     * @param productSpecificationId
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 产品名称
     * 
     * @return
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置产品名称
     * 
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 产品品牌
     * 
     * @return
     */
    public String getProductBrand() {
        return productBrand;
    }

    /**
     * 设置 产品品牌
     * 
     * @param productBrand
     */
    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    /**
     * 获取 业务城市
     * 
     * @return
     */
    public String getBusinessCity() {
        return businessCity;
    }

    /**
     * 设置 业务城市
     */
    public void setBusinessCity(String businessCity) {
        this.businessCity = businessCity;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 仓库名称
     * 
     * @return
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置 仓库名称
     * 
     * @return
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    /**
     * 获取 手机号
     * 
     * @return
     */
    public String getMobileNo() {
        return mobileNo;
    }

    /**
     * 设置 手机号
     * 
     * @param mobileNo
     */
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    /**
     * 获取 规格名称
     * 
     * @return
     */
    public String getSpecificationName() {
        return specificationName;
    }

    /**
     * 设置 规格名称
     * 
     * @param specificationName
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    /**
     * 获取 是否设置仓配费用
     * 
     * @return
     */
    public Byte getIsGetWarehouseCharge() {
        return isGetWarehouseCharge;
    }

    /**
     * 设置 是否设置仓配费用
     * 
     * @param isGetWarehouseCharge
     */
    public void setIsGetWarehouseCharge(Byte isGetWarehouseCharge) {
        this.isGetWarehouseCharge = isGetWarehouseCharge;
    }

    /**
     * 
     * /** 获取 业务类型
     * 
     * @return
     */
    public Byte getBusinessType() {
        return businessType;
    }

    /**
     * 设置 业务类型
     * 
     * @param businessType
     */
    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取 是否设置产品费用
     * 
     * @return
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置 是否设置产品费用
     * 
     * @param status
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取 下车费
     * 
     * @return
     */
    public BigDecimal getUnloadingCharge() {
        return unloadingCharge;
    }

    /**
     * 设置 下车费
     * 
     * @param unloadingCharge
     */
    public void setUnloadingCharge(BigDecimal unloadingCharge) {
        this.unloadingCharge = unloadingCharge;
    }

    /**
     * 获取 分拣费
     * 
     * @return
     */
    public BigDecimal getSortingCharge() {
        return sortingCharge;
    }

    /**
     * 设置 分拣费
     * 
     * @param sortingCharge
     */
    public void setSortingCharge(BigDecimal sortingCharge) {
        this.sortingCharge = sortingCharge;
    }

    /**
     * 获取 托管费
     * 
     * @return
     */
    public BigDecimal getCustodianCharge() {
        return custodianCharge;
    }

    /**
     * 设置托管费
     * 
     * @param custodianCharge
     */
    public void setCustodianCharge(BigDecimal custodianCharge) {
        this.custodianCharge = custodianCharge;
    }

    /**
     * 获取装车费
     * 
     * @return
     */
    public BigDecimal getLoadingCharge() {
        return loadingCharge;
    }

    /**
     * 设置 装车费
     * 
     * @param loadingCharge
     */
    public void setLoadingCharge(BigDecimal loadingCharge) {
        this.loadingCharge = loadingCharge;
    }

    /**
     * 获取 运输费
     * 
     * @return
     */
    public BigDecimal getTransportCharge() {
        return transportCharge;
    }

    /**
     * 设置 运输费
     * 
     * @param transportCharge
     */
    public void setTransportCharge(BigDecimal transportCharge) {
        this.transportCharge = transportCharge;
    }

    /**
     * 获取 卸货费
     * 
     * @return
     */
    public BigDecimal getLandingCharge() {
        return landingCharge;
    }

    /**
     * 设置卸货费
     * 
     * @param landingCharge
     */
    public void setLandingCharge(BigDecimal landingCharge) {
        this.landingCharge = landingCharge;
    }

    /**
     * 获取 首次入库时间
     * 
     * @return
     */
    public String getFirstInStockTime() {
        return firstInStockTime;
    }

    /**
     * 设置首次入库时间
     * 
     * @param firstInStockTime
     */
    public void setFirstInStockTime(String firstInStockTime) {
        this.firstInStockTime = firstInStockTime;
    }

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public String getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(String facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    /**
     * 获取 经销商名称
     * 
     * @return
     */
    public String getDealerName() {
        return dealerName;
    }

    /**
     * 设置 经销商名称
     * 
     * @param dealerName
     */
    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    /**
     * 获取 仓库使用费
     * 
     * @return
     */
    public BigDecimal getWarehouseCharge() {
        return warehouseCharge;
    }

    /**
     * 设置 仓库使用费
     * 
     * @param warehouseCharge
     */
    public void setWarehouseCharge(BigDecimal warehouseCharge) {
        this.warehouseCharge = warehouseCharge;
    }

    /**
     * 获取 平台服务费
     * 
     * @return
     */
    public BigDecimal getPlatFormCharge() {
        return platFormCharge;
    }

    /**
     * 设置 平台服务费
     * 
     * @param platFormCharge
     */
    public void setPlatFormCharge(BigDecimal platFormCharge) {
        this.platFormCharge = platFormCharge;
    }

    /**
     * 获取平台服务费(百分比)
     * 
     * @return
     */
    public BigDecimal getPlatFormFee() {
        return platFormFee;
    }

    /**
     * 设置平台服务费(百分比)
     * 
     * @return
     */
    public void setPlatFormFee(BigDecimal platFormFee) {
        this.platFormFee = platFormFee;
    }
}
