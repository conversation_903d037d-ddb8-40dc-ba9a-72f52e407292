package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;

public class BatchInventoryTransferCheckDTO implements Serializable {

    /**
     * 忽略生产日期
     */
    private Boolean isIgnoreProductionDate = false;

    /**
     * 忽略库存
     */
    private Boolean isIgnoreHasNotEnoughStore = false;

    public Boolean getIgnoreProductionDate() {
        return isIgnoreProductionDate;
    }

    public void setIgnoreProductionDate(Boolean ignoreProductionDate) {
        isIgnoreProductionDate = ignoreProductionDate;
    }

    public Boolean getIgnoreHasNotEnoughStore() {
        return isIgnoreHasNotEnoughStore;
    }

    public void setIgnoreHasNotEnoughStore(Boolean ignoreHasNotEnoughStore) {
        isIgnoreHasNotEnoughStore = ignoreHasNotEnoughStore;
    }
}
