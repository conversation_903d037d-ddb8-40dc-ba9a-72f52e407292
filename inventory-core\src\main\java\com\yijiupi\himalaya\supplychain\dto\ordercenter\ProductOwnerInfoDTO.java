package com.yijiupi.himalaya.supplychain.dto.ordercenter;

import java.io.Serializable;

public class ProductOwnerInfoDTO implements Serializable {
    /**
     * 一级货主ID
     */
    private Long ownerId;
    /**
     * 产品规格ID
     */
    private Long productSpecId;

    public static ProductOwnerInfoDTO of(Long ownerId, Long productSpecId) {
        ProductOwnerInfoDTO result = new ProductOwnerInfoDTO();
        result.setOwnerId(ownerId);
        result.setProductSpecId(productSpecId);
        return result;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }
}