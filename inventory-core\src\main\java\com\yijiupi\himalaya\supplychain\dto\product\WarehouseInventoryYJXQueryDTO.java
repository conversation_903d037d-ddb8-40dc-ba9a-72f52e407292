package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;

import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * 易经销查看仓库库存
 *
 * <AUTHOR>
 * @date 2018/12/27 20:39
 */
public class WarehouseInventoryYJXQueryDTO extends PageCondition implements Serializable {
    private static final long serialVersionUID = 4187899244101013722L;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 经销商id
     */
    private Long ownerId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 货主类型（酒批：0 合作商：1 入驻商：2）
     */
    private Byte ownerType;

    public Long getFacilitatorId() {
        return facilitatorId;
    }

    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Byte getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Byte ownerType) {
        this.ownerType = ownerType;
    }
}
