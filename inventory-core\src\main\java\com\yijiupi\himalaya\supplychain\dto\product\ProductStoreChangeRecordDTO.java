/**
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.util.Date;

public class ProductStoreChangeRecordDTO implements Serializable {

    /**
     * 主键id
     */
    private String id;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 事件类型
     */
    private Integer jiupiEventType;
    /**
     * 事件类型
     */
    private Integer erpEventType;
    /**
     * 单据类型
     */
    private Integer orderType;

    /**
     * 记录id
     */
    private String recordId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 产品信息规格id
     */
    private String specInfoId;

    /**
     * 扣或添加的库存的单位
     */
    private String maxUnit;

    /**
     * 扣或添加的库存的单位
     */
    private String minUnit;

    /**
     * erp单号
     */
    private String erpNo;

    /**
     * erp描述
     */
    private String erpDes;

    /**
     * 明细描述
     */
    private String des;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 新增数量
     */
    private ProductStoreCountDTO addStoreCountDTO;

    /**
     * 原数量
     */
    private ProductStoreCountDTO sourceStoreCountDTO;

    /**
     * 原数量
     */
    private ProductStoreCountDTO newStoreCountDTO;

    /**
     * 创建人
     */
    private String createUser;
    /**
     * 仓库类型 InventoryChangeTypes
     */
    private Integer storeType;

    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    public Integer getErpEventType() {
        return erpEventType;
    }

    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public ProductStoreCountDTO getNewStoreCountDTO() {
        return newStoreCountDTO;
    }

    public void setNewStoreCountDTO(ProductStoreCountDTO newStoreCountDTO) {
        this.newStoreCountDTO = newStoreCountDTO;
    }

    public ProductStoreCountDTO getAddStoreCountDTO() {
        return addStoreCountDTO;
    }

    public void setAddStoreCountDTO(ProductStoreCountDTO addStoreCountDTO) {
        this.addStoreCountDTO = addStoreCountDTO;
    }

    public ProductStoreCountDTO getSourceStoreCountDTO() {
        return sourceStoreCountDTO;
    }

    public void setSourceStoreCountDTO(ProductStoreCountDTO sourceStoreCountDTO) {
        this.sourceStoreCountDTO = sourceStoreCountDTO;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSpecInfoId() {
        return specInfoId;
    }

    public void setSpecInfoId(String specInfoId) {
        this.specInfoId = specInfoId;
    }

    public String getMaxUnit() {
        return maxUnit;
    }

    public void setMaxUnit(String maxUnit) {
        this.maxUnit = maxUnit;
    }

    public String getMinUnit() {
        return minUnit;
    }

    public void setMinUnit(String minUnit) {
        this.minUnit = minUnit;
    }

    public String getErpNo() {
        return erpNo;
    }

    public void setErpNo(String erpNo) {
        this.erpNo = erpNo;
    }

    public String getErpDes() {
        return erpDes;
    }

    public void setErpDes(String erpDes) {
        this.erpDes = erpDes;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

}
