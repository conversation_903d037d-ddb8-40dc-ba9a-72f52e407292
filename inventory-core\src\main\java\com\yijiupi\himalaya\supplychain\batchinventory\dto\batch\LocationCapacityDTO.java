package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品库存PO对象
 *
 * <AUTHOR>
 */
public class LocationCapacityDTO implements Serializable {
    /**
     * 货位ID
     */
    private Long location_id;
    private Long areaId;
    private String areaName;
    /**
     * 货区或货位类型：0:货位，1:货区
     */
    private Byte locationCategory;
    /**
     * 当前容量
     */
    private BigDecimal count;
    /**
     * 货位库存容量
     */
    private BigDecimal capacity;

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Byte getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(Byte locationCategory) {
        this.locationCategory = locationCategory;
    }

    public Long getLocation_id() {
        return location_id;
    }

    public void setLocation_id(Long location_id) {
        this.location_id = location_id;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public BigDecimal getCapacity() {
        return capacity;
    }

    public BigDecimal getLeftCapacity() {
        return capacity.subtract(count);
    }

    public void setCapacity(BigDecimal capacity) {
        this.capacity = capacity;
    }
}
