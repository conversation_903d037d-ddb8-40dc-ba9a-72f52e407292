package com.yijiupi.himalaya.supplychain.inventory.dto.instock;

import java.io.Serializable;
import java.math.BigDecimal;

public class InventoryOrderRelatedItemDTO implements Serializable {

    /**
     * 移动大单位数量
     */
    private BigDecimal packageCount;

    /**
     * 移动小单位数量
     */
    private BigDecimal unitCount;

    /**
     * 合计小单位数量
     */
    private BigDecimal unitTotalCount;

    /**
     * to货位id
     */
    private Long toLocationId;

    /**
     * to货位
     */
    private String toLocationName;

    /**
     * to库存渠道
     */
    private String toChannel;

    /**
     * 产品库存类型: 0:普通产品 1:残次品 2:陈列品
     */
    private Byte productStockType;

    /**
     * 备注
     */
    private String remark;

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public String getToLocationName() {
        return toLocationName;
    }

    public void setToLocationName(String toLocationName) {
        this.toLocationName = toLocationName;
    }

    public String getToChannel() {
        return toChannel;
    }

    public void setToChannel(String toChannel) {
        this.toChannel = toChannel;
    }

    public Byte getProductStockType() {
        return productStockType;
    }

    public void setProductStockType(Byte productStockType) {
        this.productStockType = productStockType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
