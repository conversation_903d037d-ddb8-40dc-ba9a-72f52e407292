package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 产品查询
 *
 * <AUTHOR>
 * @since 2018/8/23 14:13
 */
public class ProductSkuInfoSO implements Serializable {
    private static final long serialVersionUID = -7269576918362255005L;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库
     */
    private Integer warehouseId;

    /**
     * 产品名称
     */
    private String productSkuName;

    /**
     * 产品SKU列表
     */
    private List<Long> productSkuIdList;

    /**
     * 是否查询库存时抹去货主 0[null]:不抹去货主，其它则抹去货主
     *
     */
    private Integer eraseOwnerId;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 当前页
     */
    private Integer pageNum;

    /**
     * 产品来源 酒批：0 微酒：1 知花知果：2 易款连锁：3
     */
    private Integer source;

    /**
     * 库存 1：有库存 2：无库存
     */
    private Integer storeType;

    /**
     * 货主 0：易久批
     */
    private Integer ownerType;

    /**
     * 是否无关联货位
     */
    private Boolean withoutLocation;
    /**
     * 查询是否删除的sku：0 否，1 是
     */
    private List<Byte> deleted = Collections.singletonList((byte)0);

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getProductSkuName() {
        return productSkuName;
    }

    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public List<Long> getProductSkuIdList() {
        return productSkuIdList;
    }

    public void setProductSkuIdList(List<Long> productSkuIdList) {
        this.productSkuIdList = productSkuIdList;
    }

    public Integer getEraseOwnerId() {
        return eraseOwnerId;
    }

    public void setEraseOwnerId(Integer eraseOwnerId) {
        this.eraseOwnerId = eraseOwnerId;
    }

    public Boolean getWithoutLocation() {
        return withoutLocation;
    }

    public void setWithoutLocation(Boolean withoutLocation) {
        this.withoutLocation = withoutLocation;
    }

    /**
     * 获取 查询是否删除的sku：0 否，1 是
     *
     * @return deleted 查询是否删除的sku：0 否，1 是
     */
    public List<Byte> getDeleted() {
        return this.deleted;
    }

    /**
     * 设置 查询是否删除的sku：0 否，1 是
     *
     * @param deleted 查询是否删除的sku：0 否，1 是
     */
    public void setDeleted(List<Byte> deleted) {
        this.deleted = deleted;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }
}
