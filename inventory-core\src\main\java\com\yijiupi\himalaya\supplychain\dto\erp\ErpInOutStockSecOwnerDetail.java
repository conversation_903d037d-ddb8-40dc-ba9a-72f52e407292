package com.yijiupi.himalaya.supplychain.dto.erp;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-10-18 10:45
 **/
public class ErpInOutStockSecOwnerDetail implements Serializable {

    /**
     * 二级货主小单位总数量
     */
    private BigDecimal count;

    /**
     * erp 二级货主 id
     */
    private String erpSecProductOwnerId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public String getErpSecProductOwnerId() {
        return erpSecProductOwnerId;
    }

    public void setErpSecProductOwnerId(String erpSecProductOwnerId) {
        this.erpSecProductOwnerId = erpSecProductOwnerId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
}
