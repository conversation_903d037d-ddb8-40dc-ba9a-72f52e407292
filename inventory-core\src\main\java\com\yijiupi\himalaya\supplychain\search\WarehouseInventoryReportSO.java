package com.yijiupi.himalaya.supplychain.search;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * 仓库库存报表查询
 *
 * <AUTHOR>
 * @date 2019/4/26 18:11
 */
public class WarehouseInventoryReportSO extends PageCondition implements Serializable {

    private static final long serialVersionUID = 304259376938806358L;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 产品名称.
     */
    private String productName;

    /**
     * 货主id（1：表示久批）
     */
    private Long ownerId;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    /**
     * 品牌名称
     */
    private String productBrand;

    /**
     * 一级类目Id
     */
    private Long firstCategoryId;

    /**
     * 二级类目id
     */
    private Long secondCategoryId;
    /**
     * 产品sku列表
     */
    private List<Long> skuIds;
    /**
     * 仓库库存数量限制
     */
    private Byte storeLimit;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 是否有库存： null: 查所有 1：库存大于零 0：库存等于小于零 -1：库存小于零
     */
    private Byte hasRealStoreType;

    /**
     * 一级类目ID集合
     */
    private List<Long> firstCategoryIdList;

    public List<Long> getFirstCategoryIdList() {
        return firstCategoryIdList;
    }

    public void setFirstCategoryIdList(List<Long> firstCategoryIdList) {
        this.firstCategoryIdList = firstCategoryIdList;
    }

    public List<Long> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    public Byte getStoreLimit() {
        return storeLimit;
    }

    public void setStoreLimit(Byte storeLimit) {
        this.storeLimit = storeLimit;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    public Long getFirstCategoryId() {
        return firstCategoryId;
    }

    public void setFirstCategoryId(Long firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    public Long getSecondCategoryId() {
        return secondCategoryId;
    }

    public void setSecondCategoryId(Long secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
    }

    public Byte getHasRealStoreType() {
        return hasRealStoreType;
    }

    public void setHasRealStoreType(Byte hasRealStoreType) {
        this.hasRealStoreType = hasRealStoreType;
    }
}
