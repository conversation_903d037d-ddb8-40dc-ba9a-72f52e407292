package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.batch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.aspect.BatchInventorySendFaildMQ;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryManageBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.locationinventory.BatchTaskItemCompleteTransferBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event.BatchInventoryEventFireBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.DefectiveInventoryPriceDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.listener.SupplychainStoreChangeListener;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.ReplenishmentTaskItemDTO;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.CCPLimitSkuCheckDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductInfoQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2018/3/31
 */
@Service(timeout = 60000)
public class BatchInventoryManageServiceImpl implements IBatchInventoryManageService {

    private final static Logger LOGGER = LoggerFactory.getLogger(BatchInventoryManageServiceImpl.class);

    @Autowired
    private BatchInventoryManageBL batchInventoryManageBL;
    @Autowired
    private SupplychainStoreChangeListener supplychainStoreChangeListener;
    @Autowired
    private BatchTaskItemCompleteTransferBL batchTaskItemCompleteTransferBL;

    @Autowired
    private BatchInventoryEventFireBL batchInventoryEventFireBL;
    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Autowired
    private BatchInventorySendFaildMQ batchInventorySendFaildMQ;

    @Reference
    private IProductInfoQueryService productInfoQueryService;

    /**
     * 拣货移库校验-新
     *
     * @param pickUpDTOList
     * @return
     */
    @Override
    public List<PickUpDTO> checkInventoryTransferForPickUp(List<PickUpDTO> pickUpDTOList) {
        AssertUtils.notEmpty(pickUpDTOList, "拣货产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return null;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getCount(), "拣货数量不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        List<PickUpDTO> realPickUpDTOList = new ArrayList<>();
        batchTaskItemCompleteTransferBL.checkInventoryTransferForPickUp(pickUpDTOList, true, false, realPickUpDTOList);
        return realPickUpDTOList;
    }

    /**
     * 拣货校验移库
     */
    @Override
    public List<PickUpDTO> checkInventoryTransferByPickUp(List<PickUpDTO> pickUpDTOList) {
        AssertUtils.notEmpty(pickUpDTOList, "拣货产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return null;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getCount(), "拣货数量不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        List<PickUpDTO> realPickUpDTOList = new ArrayList<>();
        batchInventoryManageBL.checkInventoryTransfer(pickUpDTOList, true, false, realPickUpDTOList);
        return realPickUpDTOList;
    }

    /**
     * 拣货完成(按产品拣货) 从存储区移到周转区
     *
     * @param pickUpDTOList
     */
    @Override
    public List<PickUpDTO> pickupCompleteBySku(List<PickUpDTO> pickUpDTOList,
                                               PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        LOGGER.info("拣货完成:{}", JSON.toJSONString(pickUpDTOList));
        AssertUtils.notEmpty(pickUpDTOList, "拣货产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = false;
        Integer orgId = null;
        if (warehouseId != null) {
            WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(warehouseId);
            isOpenStock = warehouseConfigDTO.getIsOpenLocationStock();
            orgId = warehouseConfigDTO.getOrg_Id();
        }
        if (!isOpenStock) {
            return Collections.EMPTY_LIST;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getCount(), "拣货数量不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        // 返回实际移库数量
        List<PickUpDTO> realPickUpDTOList =
                batchInventoryManageBL.batchInventoryTransferBySku(pickUpDTOList, pickUpChangeRecordDTO, true, false);

        // 触发补货机制
        List<ReplenishmentTaskItemDTO> replenishmentTaskItemDTOS = new ArrayList<>();
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            ReplenishmentTaskItemDTO replenishmentTaskItemDTO = new ReplenishmentTaskItemDTO();
            replenishmentTaskItemDTO.setOrgId(orgId);
            replenishmentTaskItemDTO.setWarehouseId(pickUpDTO.getWarehouseId());
            replenishmentTaskItemDTO.setProductSkuId(pickUpDTO.getProductSkuId());
            replenishmentTaskItemDTO.setToLocationId(pickUpDTO.getFromLocationId());

            replenishmentTaskItemDTOS.add(replenishmentTaskItemDTO);
        }
        batchInventoryEventFireBL.sendAddReplenishmentEvent(replenishmentTaskItemDTOS);

        return realPickUpDTOList;
    }

    /**
     * 完成拣货任务明细
     *
     * @param pickUpDTOList
     * @param pickUpChangeRecordDTO
     * @return
     */
    @Override
    public List<PickUpDTO> batchTaskItemComplete(List<PickUpDTO> pickUpDTOList,
                                                 PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        LOGGER.info("拣货完成新:{}", JSON.toJSONString(pickUpDTOList));
        AssertUtils.notEmpty(pickUpDTOList, "拣货产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = false;
        Integer orgId = null;
        if (warehouseId != null) {
            WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(warehouseId);
            isOpenStock = warehouseConfigDTO.getIsOpenLocationStock();
            orgId = warehouseConfigDTO.getOrg_Id();
        }
        if (!isOpenStock) {
            return Collections.emptyList();
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getCount(), "拣货数量不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        // 返回实际移库数量
        List<PickUpDTO> realPickUpDTOList =
                batchTaskItemCompleteTransferBL.batchTaskItemComplete(pickUpDTOList, pickUpChangeRecordDTO, true, false);

        // 触发补货机制
        List<ReplenishmentTaskItemDTO> replenishmentTaskItemDTOS = new ArrayList<>();
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            ReplenishmentTaskItemDTO replenishmentTaskItemDTO = new ReplenishmentTaskItemDTO();
            replenishmentTaskItemDTO.setOrgId(orgId);
            replenishmentTaskItemDTO.setWarehouseId(pickUpDTO.getWarehouseId());
            replenishmentTaskItemDTO.setProductSkuId(pickUpDTO.getProductSkuId());
            replenishmentTaskItemDTO.setToLocationId(pickUpDTO.getFromLocationId());

            replenishmentTaskItemDTOS.add(replenishmentTaskItemDTO);
        }
        batchInventoryEventFireBL.sendAddReplenishmentEvent(replenishmentTaskItemDTOS);

        return realPickUpDTOList;
    }

    /**
     * 上架移库
     *
     * @param pickUpDTOList
     */
    @Override
    public void batchInventoryTransferBySku(List<PickUpDTO> pickUpDTOList,
                                            PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        LOGGER.info("上架移库:{}", JSON.toJSONString(pickUpDTOList));
        AssertUtils.notEmpty(pickUpDTOList, "上架拣货产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        batchInventoryManageBL.batchInventoryTransferBySku(pickUpDTOList, pickUpChangeRecordDTO, false, false);
    }

    /**
     * 上架批量【多个上架任务】移库
     */
    @Override
    public void putAwayTaskBatchPickUp(List<PickUpRecordDTO> pickUpChangeRecordList, Integer warehouseId) {
        LOGGER.info("上架批量【多个上架任务】移库参数 : {}", JSON.toJSONString(pickUpChangeRecordList));
        AssertUtils.notEmpty(pickUpChangeRecordList, "上架拣货产品不能为空");
        List<PickUpDTO> pickUpDTOS =
                pickUpChangeRecordList.stream().filter(e -> e != null && CollectionUtils.isNotEmpty(e.getPickUpDTOList()))
                        .flatMap(e -> e.getPickUpDTOList().stream()).collect(Collectors.toList());
        AssertUtils.notEmpty(pickUpDTOS, "上架批量【多个上架任务】移库信息不能为空！");
        for (PickUpDTO pickUpDTO : pickUpDTOS) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        batchInventoryManageBL.putAwayTaskBatchPickUp(pickUpChangeRecordList, warehouseId);
    }

    /**
     * 修改出库位
     */
    @Override
    public void updateLocationByCk(List<PickUpDTO> pickUpDTOList, PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        LOGGER.info("修改出库位:{}", JSON.toJSONString(pickUpDTOList));
        AssertUtils.notEmpty(pickUpDTOList, "出库位产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getCount(), "移库数量不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "原出库位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "出库位不能为空");
        }
        batchInventoryManageBL.batchInventoryTransferBySku(pickUpDTOList, pickUpChangeRecordDTO, true, true);
    }

    /**
     * 修改货位库存（只修改数量）
     */
    @Override
    public void updateProductStoreBatch(List<ProductStoreBatchDTO> productStoreBatchDTOS, String operaterUser) {
        AssertUtils.notNull(productStoreBatchDTOS, "参数不能为空");
        productStoreBatchDTOS.forEach(p -> {
            AssertUtils.notNull(p.getId(), "货位库存id不能为空");
            AssertUtils.notNull(p.getTotalCount(), "货位库存数量不能为空");
            AssertUtils.notNull(p.getBatchAttributeInfoNo(), "批次号不能为空");
        });
        batchInventoryManageBL.updateProductStoreBatch(productStoreBatchDTOS, operaterUser);
    }

    /**
     * 批次库存异常重试
     *
     * @param json
     */
    @Override
    public void processBatchInventoryByJson(String json) {
        LOGGER.info("批次库存异常重试参数：{}", JSON.toJSONString(json));
        List<ProductInventoryChangeRecordPO> recordPOList = JSON.parseArray(json, ProductInventoryChangeRecordPO.class);
        try {
            supplychainStoreChangeListener.processStoreChangeMsg(recordPOList);
        } catch (Exception e) {
            LOGGER.error("批次库存异常重试异常，错误信息：", e);
            batchInventorySendFaildMQ.mqSendFaild(JSON.toJSONString(recordPOList), "mq.supplychain.inventory.productstorechange", e);
        }
    }

    /**
     * 移库
     *
     * @param pickUpDTOList
     */
    @Override
    public List<PickUpDTO> batchInventoryTransfer(List<PickUpDTO> pickUpDTOList,
                                                  PickUpChangeRecordDTO pickUpChangeRecordDTO, BatchInventoryTransferCheckDTO batchInventoryTransferCheckDTO) {
        LOGGER.info("移库:{}", JSON.toJSONString(pickUpDTOList));
        AssertUtils.notEmpty(pickUpDTOList, "产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return null;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        return batchInventoryManageBL.batchInventoryTransferBySku(pickUpDTOList, pickUpChangeRecordDTO,
                batchInventoryTransferCheckDTO.getIgnoreProductionDate(),
                batchInventoryTransferCheckDTO.getIgnoreHasNotEnoughStore());
    }

    @Override
    public List<BatchInventoryInfoUpdateDTO>
    updateBatchInventoryInfo(List<BatchInventoryInfoUpdateDTO> batchInventoryInfoUpdateDTOS) {
        return batchInventoryManageBL.updateBatchInventoryInfo(batchInventoryInfoUpdateDTOS, false);
    }

    @Override
    public void processLocationInventory(ProductInventoryChangeDTO productInventoryChangeDTO) {
        AssertUtils.notNull(productInventoryChangeDTO, "参数不能为空");
        AssertUtils.notNull(productInventoryChangeDTO.getProductStoreId(), "库存id不能为空");
        batchInventoryManageBL.processLocationInventory(productInventoryChangeDTO);
    }

    /**
     * 未开启货位库存变更产品批次属性
     */
    @Override
    public void nonOpenStockChangeBatchInventoryByStoreCheck(StoreCheckUpdateBatchInventoryDTO changeDTO) {
        batchInventoryManageBL.nonOpenStockChangeBatchInventoryByStoreCheck(changeDTO);
    }

    @Override
    public void clearBeforeProductionDate(List<Integer> warehouseIdList, Date endProductionDate) {
        AssertUtils.notEmpty(warehouseIdList, "仓库ID不能为空");
        AssertUtils.notNull(endProductionDate, "截止生产日期不能为空");
        batchInventoryManageBL.clearBeforeProductionDate(warehouseIdList, endProductionDate);
    }

    @Override
    public DefectiveInventoryPriceDTO calCcpPriceBySkuCount(Integer orgId, Integer warehouseId,
                                                            Map<Long, BigDecimal> skuCount) {
        CCPLimitSkuCheckDTO skuCheckDTO = new CCPLimitSkuCheckDTO();
        skuCheckDTO.setWarehouseId(warehouseId);
        skuCheckDTO.setLstSkuId(new ArrayList<>(skuCount.keySet()));
        productInfoQueryService.checkCCPBySkuIds(skuCheckDTO);
        return batchInventoryManageBL.calCcpPriceBySkuCount(orgId, warehouseId, skuCount);
    }

    @Override
    public DefectiveInventoryPriceDTO saveCcpPriceByInventory(Integer orgId, Integer warehouseId) {
        return batchInventoryManageBL.saveCcpPriceByInventory(orgId, warehouseId);
    }

    @Override
    public DefectiveInventoryPriceDTO saveCcpPriceBySkuCount(Integer orgId, Integer warehouseId,
                                                             Map<Long, BigDecimal> skuCount) {
        return batchInventoryManageBL.saveCcpPriceBySkuCount(orgId, warehouseId, skuCount);
    }

    @Override
    public DefectiveInventoryPriceDTO getCcpPrice(Integer orgId, Integer warehouseId) {
        return batchInventoryManageBL.getCcpPrice(orgId, warehouseId);
    }

    @Override
    public void updateProductionDateBySpec(BatchInventoryDTO updateDTO) {
        batchInventoryManageBL.updateProductionDateBySpec(updateDTO);
    }

    @Override
    public void clearBeforeProductionDateUtil(BatchInventoryDTO updateDTO) {
        batchInventoryManageBL.clearBeforeProductionDate(Arrays.asList(updateDTO.getWarehouseId()),
                updateDTO.getProductionDate());
    }
}
