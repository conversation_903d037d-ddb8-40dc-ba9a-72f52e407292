package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * 自有托管仓库返回DTO
 * 
 * @author: lidengfeng
 * @date 2018/10/12 9:53
 */
public class WarehouseChooseReturnDTO implements Serializable {

    /**
     * 仓库id
     */
    private Integer id;

    /**
     * 仓库名称
     */
    private String name;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 区
     */
    private String county;

    /**
     * 街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String detailaddress;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getDetailaddress() {
        return detailaddress;
    }

    public void setDetailaddress(String detailaddress) {
        this.detailaddress = detailaddress;
    }

    public Integer getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(Integer warehouseType) {
        this.warehouseType = warehouseType;
    }

    /**
     * 获取 城市id
     * 
     * @return
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市id
     * 
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }
}
