package com.yijiupi.himalaya.supplychain.dto.standard;

import java.io.Serializable;
import java.math.BigDecimal;

public class ProductWarehouseStoreDTO extends ProductCityWarehouseStoreDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long productSkuId;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 包装规格名称
     *
     * @return specificationName 包装规格名称
     */
    public String getSpecificationName() {
        return this.specificationName;
    }

    /**
     * 设置 包装规格名称
     *
     * @param specificationName 包装规格名称
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    /**
     * 获取 包装规格大单位
     *
     * @return packageName 包装规格大单位
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 包装规格大单位
     *
     * @param packageName 包装规格大单位
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 包装规格小单位
     *
     * @return specUnitName 包装规格小单位
     */
    public String getSpecUnitName() {
        return this.unitName;
    }

    /**
     * 设置 包装规格小单位
     *
     * @param specUnitName 包装规格小单位
     */
    public void setSpecUnitName(String specUnitName) {
        this.unitName = specUnitName;
    }

    /**
     * 获取 包装规格大小单位转换系数
     *
     * @return packageQuantity 包装规格大小单位转换系数
     */
    public BigDecimal getPackageQuantity() {
        return this.packageQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     *
     * @param packageQuantity 包装规格大小单位转换系数
     */
    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @return source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    @Override
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @param source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    @Override
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 二级货主Id
     *
     * @return secOwnerId 二级货主Id
     */
    @Override
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主Id
     *
     * @param secOwnerId 二级货主Id
     */
    @Override
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }
}
