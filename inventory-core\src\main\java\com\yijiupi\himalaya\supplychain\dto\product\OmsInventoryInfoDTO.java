package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;

public class OmsInventoryInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer orgId;
    private Integer warehouseId;
    private Long specificationId;
    private Long ownerId;
    private Long secOwnerId;
    private String ownerName;
    private String productName;
    private BigDecimal saleInventoryCount;
    @Deprecated
    private Long productSkuId;
    private String specificationName;
    private String packageName;
    private String unitName;
    private BigDecimal packageQuantity;
    private String internalKey;

    public OmsInventoryInfoDTO() {}

    public Long getProductSkuId() {
        return this.productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getSpecificationName() {
        return this.specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getPackageName() {
        return this.packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return this.unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackageQuantity() {
        return this.packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Integer getOrgId() {
        return this.orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSpecificationId() {
        return this.specificationId;
    }

    public void setSpecificationId(Long specificationId) {
        this.specificationId = specificationId;
    }

    public Long getOwnerId() {
        return this.ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return this.ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getProductName() {
        return this.productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getSaleInventoryCount() {
        return this.saleInventoryCount;
    }

    public void setSaleInventoryCount(BigDecimal saleInventoryCount) {
        this.saleInventoryCount = saleInventoryCount;
    }

    public void setInternalKey(String internalKey) {
        this.internalKey = internalKey;
    }

    public String getInternalKey() {
        return this.internalKey;
    }

    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }
}
