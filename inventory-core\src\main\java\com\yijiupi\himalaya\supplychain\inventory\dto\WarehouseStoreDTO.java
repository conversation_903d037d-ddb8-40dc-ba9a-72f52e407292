package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 存放相关产品库存信息DTO
 *
 * <AUTHOR>
 */
public class WarehouseStoreDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID.
     */
    private String id;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 产品信息规格id
     */
    private Long productSpecId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 所属人ID
     */
    private Long ownerId;
    /**
     * 所属人类型
     */
    private Integer ownerType;
    /**
     * 仓库库存按照小单位换算的数量(文本)
     */
    private BigDecimal warehouseTotalCount;
    /**
     * 销售库存按照小单位换算的数量(文本)
     */
    private BigDecimal saleStoreTotalCount;
    /**
     * 渠道
     */
    private Integer channel;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 二级货主名称
     */
    private String secOwnerName;

    public String getSecOwnerName() {
        return secOwnerName;
    }

    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 销售模式
     */
    private Integer saleModel;

    /**
     * 产品信息Id
     */
    private Long productInfoId;

    /**
     * 一级类目ID
     */
    private Long statisticsClass;

    /**
     * 一级类目名称
     */
    private String statisticsClassName;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 关联外部二级货主Id
     */
    private String refSecOwnerId;

    /**
     * 生产日期
     */
    private Date manufactureTime;

    public String getRefSecOwnerId() {
        return refSecOwnerId;
    }

    public void setRefSecOwnerId(String refSecOwnerId) {
        this.refSecOwnerId = refSecOwnerId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getSaleModel() {
        return saleModel;
    }

    public void setSaleModel(Integer saleModel) {
        this.saleModel = saleModel;
    }

    public Long getProductInfoId() {
        return productInfoId;
    }

    public void setProductInfoId(Long productInfoId) {
        this.productInfoId = productInfoId;
    }

    public Long getStatisticsClass() {
        return statisticsClass;
    }

    public void setStatisticsClass(Long statisticsClass) {
        this.statisticsClass = statisticsClass;
    }

    public String getStatisticsClassName() {
        return statisticsClassName;
    }

    public void setStatisticsClassName(String statisticsClassName) {
        this.statisticsClassName = statisticsClassName;
    }

    public BigDecimal getSaleStoreTotalCount() {
        return saleStoreTotalCount;
    }

    public void setSaleStoreTotalCount(BigDecimal saleStoreTotalCount) {
        this.saleStoreTotalCount = saleStoreTotalCount;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public BigDecimal getWarehouseTotalCount() {
        return warehouseTotalCount;
    }

    public void setWarehouseTotalCount(BigDecimal warehouseTotalCount) {
        this.warehouseTotalCount = warehouseTotalCount;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Date getManufactureTime() {
        return manufactureTime;
    }

    public void setManufactureTime(Date manufactureTime) {
        this.manufactureTime = manufactureTime;
    }
}
