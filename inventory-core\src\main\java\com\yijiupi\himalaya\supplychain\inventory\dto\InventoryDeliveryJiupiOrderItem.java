package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单配送项
 *
 * <AUTHOR>
 */
public class InventoryDeliveryJiupiOrderItem implements Serializable {
    private static final long serialVersionUID = -2895192242798725147L;
    /**
     * 产品SKUID.
     */
    private Long productSkuId;
    /**
     * 订单购买数量.
     */
    private BigDecimal buyCount;
    /**
     * 配送出库数量.
     */
    private BigDecimal deliverCount;
    /**
     * 实际收货数量.
     */
    private BigDecimal takeCount;
    /**
     * 产品销售规格系数.
     */
    private BigDecimal saleSpecQuantity;
    /**
     * 订单明细ID
     */
    private Long orderItem_Id;

    /**
     * 货主Id
     */
    private Long ownerId;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    /**
     * 规格Id
     */
    private Long productSpecification_Id;

    /**
     * 入库单【入库货位】
     */
    private Long locationId;

    private String locationName;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 明细id
     */
    private Long orderItemDetailId;

    /**
     * 明细id
     */
    private BigDecimal unitTotalCount;

    /**
     * oms订单项id
     */
    private Long omsOrderItemId;

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 订单项detail
     */
    private List<InventoryDeliveryJiupiOrderItemDetailDTO> secOwnerDetail;

    public List<InventoryDeliveryJiupiOrderItemDetailDTO> getSecOwnerDetail() {
        return secOwnerDetail;
    }

    public void setSecOwnerDetail(List<InventoryDeliveryJiupiOrderItemDetailDTO> secOwnerDetail) {
        this.secOwnerDetail = secOwnerDetail;
    }

    /**
     * WMS系统单据明细ID
     */
    private Long relationOrderItemId;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 原始明细id
     */
    private Long originOrderItemDetailId;

    /**
     * 是否残次品，true：是
     */
    private Boolean defective;

    /**
     * 是否临期0:否，1：是
     */
    private Byte isAdvent;

    public Long getProductSpecification_Id() {
        return productSpecification_Id;
    }

    public void setProductSpecification_Id(Long productSpecification_Id) {
        this.productSpecification_Id = productSpecification_Id;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 产品SKUID.
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    public Long getOrderItem_Id() {
        return orderItem_Id;
    }

    public void setOrderItem_Id(Long orderItem_Id) {
        this.orderItem_Id = orderItem_Id;
    }

    /**
     * 设置 产品SKUID.
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 订单购买数量.
     */
    public BigDecimal getBuyCount() {
        return buyCount;
    }

    /**
     * 设置 订单购买数量.
     */
    public void setBuyCount(BigDecimal buyCount) {
        this.buyCount = buyCount;
    }

    /**
     * 获取 配送出库数量.
     */
    public BigDecimal getDeliverCount() {
        return deliverCount;
    }

    /**
     * 设置 配送出库数量.
     */
    public void setDeliverCount(BigDecimal deliverCount) {
        this.deliverCount = deliverCount;
    }

    /**
     * 获取 实际收货数量.
     */
    public BigDecimal getTakeCount() {
        return takeCount;
    }

    /**
     * 设置 实际收货数量.
     */
    public void setTakeCount(BigDecimal takeCount) {
        this.takeCount = takeCount;
    }

    /**
     * 获取 产品销售规格系数.
     */
    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    /**
     * 设置 产品销售规格系数.
     */
    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getRelationOrderItemId() {
        return relationOrderItemId;
    }

    public void setRelationOrderItemId(Long relationOrderItemId) {
        this.relationOrderItemId = relationOrderItemId;
    }

    public Long getOrderItemDetailId() {
        return orderItemDetailId;
    }

    public void setOrderItemDetailId(Long orderItemDetailId) {
        this.orderItemDetailId = orderItemDetailId;
    }

    public Long getOmsOrderItemId() {
        return omsOrderItemId;
    }

    public void setOmsOrderItemId(Long omsOrderItemId) {
        this.omsOrderItemId = omsOrderItemId;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Long getOriginOrderItemDetailId() {
        return originOrderItemDetailId;
    }

    public void setOriginOrderItemDetailId(Long originOrderItemDetailId) {
        this.originOrderItemDetailId = originOrderItemDetailId;
    }

    public Boolean getDefective() {
        return defective;
    }

    public void setDefective(Boolean defective) {
        this.defective = defective;
    }

    public Byte getIsAdvent() {
        return isAdvent;
    }

    public void setIsAdvent(Byte isAdvent) {
        this.isAdvent = isAdvent;
    }
}