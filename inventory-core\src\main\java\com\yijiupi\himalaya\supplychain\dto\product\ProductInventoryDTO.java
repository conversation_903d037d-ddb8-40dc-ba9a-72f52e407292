package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProductInventoryDTO implements Serializable {
    /**
     * 主键ID
     */
    private String id;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 库存总量,按最小单位累计
     */
    private BigDecimal totalCountMinUnit;
    /**
     * 库存所属类型
     */
    private Integer ownerType;
    /**
     * 库存所属人ID
     */
    private Long ownerId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
    /**
     * 产品信息规格ID
     */
    private Long productSpecificationId;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * skuid
     */
    private Long productSkuId;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 货物渠道
     */
    private Integer channel;
    /**
     * 库存变化值(最小单位)
     */
    private BigDecimal changeCount;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 仓库托管费（每件每月，单位为元）
     */
    private BigDecimal warehouseCustodyFee;
    /**
     * 配送费（单件配送费）
     */
    private BigDecimal deliveryFee;
    /**
     * 配送费支付方式（0：固定价格，1：百分比）
     */
    private Integer deliveryPayType;
    /**
     * 配送中数量
     */
    private BigDecimal deliveryedCount;
    /**
     * 出入库类型. 入库单(1),出库单(2)
     */
    private Integer outInType;
    /**
     * 配送方式（0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    private Integer deliveryMode;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 销售模式
     */
    private Integer saleModel;

    /**
     * 产品信息Id
     */
    private Long productInfoId;

    /**
     * 一级类目ID
     */
    private Long statisticsClass;

    /**
     * 一级类目名称
     */
    private String statisticsClassName;

    /**
     * 二级货主名称
     */
    private String secOwnerName;

    public String getSecOwnerName() {
        return secOwnerName;
    }

    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    public Long getProductInfoId() {
        return productInfoId;
    }

    public void setProductInfoId(Long productInfoId) {
        this.productInfoId = productInfoId;
    }

    public Long getStatisticsClass() {
        return statisticsClass;
    }

    public void setStatisticsClass(Long statisticsClass) {
        this.statisticsClass = statisticsClass;
    }

    public String getStatisticsClassName() {
        return statisticsClassName;
    }

    public void setStatisticsClassName(String statisticsClassName) {
        this.statisticsClassName = statisticsClassName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getSaleModel() {
        return saleModel;
    }

    public void setSaleModel(Integer saleModel) {
        this.saleModel = saleModel;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getTotalCountMinUnit() {
        return totalCountMinUnit;
    }

    public void setTotalCountMinUnit(BigDecimal totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public BigDecimal getChangeCount() {
        return changeCount;
    }

    public void setChangeCount(BigDecimal changeCount) {
        this.changeCount = changeCount;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public BigDecimal getWarehouseCustodyFee() {
        return warehouseCustodyFee;
    }

    public void setWarehouseCustodyFee(BigDecimal warehouseCustodyFee) {
        this.warehouseCustodyFee = warehouseCustodyFee;
    }

    public BigDecimal getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(BigDecimal deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    public Integer getDeliveryPayType() {
        return deliveryPayType;
    }

    public void setDeliveryPayType(Integer deliveryPayType) {
        this.deliveryPayType = deliveryPayType;
    }

    public BigDecimal getDeliveryedCount() {
        return deliveryedCount;
    }

    public void setDeliveryedCount(BigDecimal deliveryedCount) {
        this.deliveryedCount = deliveryedCount;
    }

    public Integer getOutInType() {
        return outInType;
    }

    public void setOutInType(Integer outInType) {
        this.outInType = outInType;
    }

    public Integer getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(Integer deliveryMode) {
        this.deliveryMode = deliveryMode;
    }
}
