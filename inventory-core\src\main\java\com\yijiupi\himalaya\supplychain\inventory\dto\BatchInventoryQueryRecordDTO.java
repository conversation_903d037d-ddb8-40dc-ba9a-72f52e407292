package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: BatchInventoryQueryRecoredDTO
 * @description:
 * @date 2023-05-23 16:36
 */
public class BatchInventoryQueryRecordDTO implements Serializable {
    /**
     * 订单号列表
     */
    private List<String> orderNos;
    /**
     * 规格id列表
     */
    private List<Long> specificationIds;


    /**
     * 获取 订单号列表
     *
     * @return orderNos 订单号列表
     */
    public List<String> getOrderNos() {
        return this.orderNos;
    }

    /**
     * 设置 订单号列表
     *
     * @param orderNos 订单号列表
     */
    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    /**
     * 获取 规格id列表
     *
     * @return specificationIds 规格id列表
     */
    public List<Long> getSpecificationIds() {
        return this.specificationIds;
    }

    /**
     * 设置 规格id列表
     *
     * @param specificationIds 规格id列表
     */
    public void setSpecificationIds(List<Long> specificationIds) {
        this.specificationIds = specificationIds;
    }
}
