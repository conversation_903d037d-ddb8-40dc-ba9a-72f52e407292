package com.yijiupi.himalaya.supplychain.inventory.dto.outstock;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.supplychain.inventory.dto.FetchTaskDataDTO;

/**
 * 虚仓实配出库
 *
 */
public class BatchOutStockConfirmDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库Id
     */
    private Integer warehouseId;

    /**
     * 更新人Id
     */
    private Integer updateUserId;
    private String updateUserName;

    /**
     * 取货任务列表
     */
    private List<FetchTaskDataDTO> fetchTaskList;

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public List<FetchTaskDataDTO> getFetchTaskList() {
        return fetchTaskList;
    }

    public void setFetchTaskList(List<FetchTaskDataDTO> fetchTaskList) {
        this.fetchTaskList = fetchTaskList;
    }

    public Integer getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Integer updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
