package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 2018/4/10
 */
public class BatchAttributeTemplateRelationReturnDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 字典表主键
     */
    private Long dicId;
    /**
     * 属性名称
     */
    private String attributeName;
    /**
     * 属性类型
     */
    private Byte attributeType;
    /**
     * 有效位数
     */
    private Integer effectiveDigit;
    /**
     * 是否必须
     */
    private Boolean required;
    /**
     * 属性可选值(字典表的)
     */
    private String attributeValue;

    /**
     * 是否参与批属性计算,不参与(0),参与计算(1)
     */
    private Byte isCalculation;

    /**
     * 是否参与盘点,不参与(0),参与(1)
     */
    private Byte isStoreCheck;

    /**
     * 用户录入批属性ID batchattributeinfo 表中 AttributeValue_Id
     */
    private Long dicAttributeValueId;

    /**
     * 用户录入属性名称 batchattributeinfo 表中 AttributeValueName
     */
    private String dicAttributeValueName;

    /**
     * 批次编号 batchattributeinfo 表中 batchAttributeInfoNo
     */
    private String batchAttributeInfoNo;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 获取 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 字典表主键
     */
    public Long getDicId() {
        return this.dicId;
    }

    /**
     * 设置 字典表主键
     */
    public void setDicId(Long dicId) {
        this.dicId = dicId;
    }

    /**
     * 获取 属性名称
     */
    public String getAttributeName() {
        return this.attributeName;
    }

    /**
     * 设置 属性名称
     */
    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    /**
     * 获取 属性类型
     */
    public Byte getAttributeType() {
        return this.attributeType;
    }

    /**
     * 设置 属性类型
     */
    public void setAttributeType(Byte attributeType) {
        this.attributeType = attributeType;
    }

    /**
     * 获取 有效位数
     */
    public Integer getEffectiveDigit() {
        return this.effectiveDigit;
    }

    /**
     * 设置 有效位数
     */
    public void setEffectiveDigit(Integer effectiveDigit) {
        this.effectiveDigit = effectiveDigit;
    }

    /**
     * 获取 是否必须
     */
    public Boolean getRequired() {
        return this.required;
    }

    /**
     * 设置 是否必须
     */
    public void setRequired(Boolean required) {
        this.required = required;
    }

    public String getAttributeValue() {
        return attributeValue;
    }

    public void setAttributeValue(String attributeValue) {
        this.attributeValue = attributeValue;
    }

    public Byte getIsCalculation() {
        return isCalculation;
    }

    public void setIsCalculation(Byte isCalculation) {
        this.isCalculation = isCalculation;
    }

    public Byte getIsStoreCheck() {
        return isStoreCheck;
    }

    public void setIsStoreCheck(Byte isStoreCheck) {
        this.isStoreCheck = isStoreCheck;
    }

    public Long getDicAttributeValueId() {
        return dicAttributeValueId;
    }

    public void setDicAttributeValueId(Long dicAttributeValueId) {
        this.dicAttributeValueId = dicAttributeValueId;
    }

    public String getDicAttributeValueName() {
        return dicAttributeValueName;
    }

    public void setDicAttributeValueName(String dicAttributeValueName) {
        this.dicAttributeValueName = dicAttributeValueName;
    }

    public String getBatchAttributeInfoNo() {
        return batchAttributeInfoNo;
    }

    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}
