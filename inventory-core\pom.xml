<?xml version="1.0"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yijiupi</groupId>
        <artifactId>himalaya-supplychain-microservice-inventory</artifactId>
        <version>2.20.0</version>
    </parent>

    <artifactId>himalaya-supplychain-microservice-inventory-core</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-serviceutils</artifactId>
            <version>0.0.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-outstockordersync-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-warehouseproduct-core</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
