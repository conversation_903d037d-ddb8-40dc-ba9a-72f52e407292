package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class StockOrderItemInventoryDTO implements Serializable {

    /** 城市ID，分库用（第三方订单使用一个新的自定义CityId） */
    private Integer orgId;

    /** 商品名称 */
    private String productName;

    /** skuId（赠品SKUId可能为null） */
    private Long skuId;

    /** 品牌 */
    private String productBrand;

    /** 类目 */
    private String categoryName;

    /** 包装规格 */
    private String specName;

    /** 包装规格大小单位转换系数 */
    private BigDecimal specQuantity;

    /** 销售规格名称 */
    private String saleSpec;

    /** 销售规格系数 */
    private BigDecimal saleSpecQuantity;

    /** 大单位名称 */
    private String packageName;

    /** 大单位数量 */
    private BigDecimal packageCount;

    /** 小单位名称 */
    private String unitName;

    /** 小单位数量 */
    private BigDecimal unitCount;

    /** 小单位总数量 */
    private BigDecimal unitTotalCount;

    /** 创建时间 */
    private Date createTime;

    /** 货位id */
    private Long locationid;

    /** 货位名称 */
    private String locationname;

    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source = 0;

    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel = 0;

    /**
     * 所属人(货主)id
     */
    private Long ownerId;

    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 批次时间
     */
    private Date batchTime;

    /** 缺货数量 */
    private BigDecimal lackUnitCount;

    /**
     * 任务项Id
     */
    private Long fetchTaskItemId;

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getSaleSpec() {
        return saleSpec;
    }

    public void setSaleSpec(String saleSpec) {
        this.saleSpec = saleSpec;
    }

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public BigDecimal getLackUnitCount() {
        return lackUnitCount;
    }

    public void setLackUnitCount(BigDecimal lackUnitCount) {
        this.lackUnitCount = lackUnitCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getLocationid() {
        return locationid;
    }

    public void setLocationid(Long locationid) {
        this.locationid = locationid;
    }

    public String getLocationname() {
        return locationname;
    }

    public void setLocationname(String locationname) {
        this.locationname = locationname;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getFetchTaskItemId() {
        return fetchTaskItemId;
    }

    public void setFetchTaskItemId(Long fetchTaskItemId) {
        this.fetchTaskItemId = fetchTaskItemId;
    }
}
