package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import com.yijiupi.himalaya.base.search.PageCondition;
import com.yijiupi.himalaya.supplychain.batchinventory.constant.StockAgeInventoryQueryType;

import java.math.BigDecimal;

public class StockAgeInventoryQueryDTO extends PageCondition {

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 保存条件 0:常温 1:冷藏 2:冷冻
     */
    private Byte storageType;

    /**
     * 库龄天数
     */
    private Long stockAge;

    /**
     * 超期天数
     */
    private Long overdue;

    /**
     * 超期剩余天数
     */
    private Long overdueSurplus;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 查询类型
     */
    private Byte stockAgeInventoryQueryType = StockAgeInventoryQueryType.全部.getType();

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Byte getStorageType() {
        return storageType;
    }

    public void setStorageType(Byte storageType) {
        this.storageType = storageType;
    }

    public Long getStockAge() {
        return stockAge;
    }

    public void setStockAge(Long stockAge) {
        this.stockAge = stockAge;
    }

    public Long getOverdue() {
        return overdue;
    }

    public void setOverdue(Long overdue) {
        this.overdue = overdue;
    }

    public Long getOverdueSurplus() {
        return overdueSurplus;
    }

    public void setOverdueSurplus(Long overdueSurplus) {
        this.overdueSurplus = overdueSurplus;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Byte getStockAgeInventoryQueryType() {
        return stockAgeInventoryQueryType;
    }

    public void setStockAgeInventoryQueryType(Byte stockAgeInventoryQueryType) {
        this.stockAgeInventoryQueryType = stockAgeInventoryQueryType;
    }
}
