package com.yijiupi.himalaya.supplychain.inventory.dto.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存报表-变更记录
 * 
 * @author: tangkun
 * @date: 2017年3月30日 下午8:21:15
 */
public class StoreChangeInfoDTO implements Serializable {
    private static final long serialVersionUID = -279476524591011950L;
    /**
     * 单据ID
     */
    private String orderId;
    /**
     * 单据号
     */
    private String orderNo;
    /**
     * 变更时间
     */
    private String createTime;
    /**
     * 变更数量
     */
    private BigDecimal changeCount;
    /**
     * 备注
     */
    private String description;
    /**
     * 变更类型
     */
    private String orderTypeName;
    /**
     * 单位
     */
    private String packageName;
    /**
     * 单位
     */
    private String unitName;
    /**
     * 变更数量小
     */
    private BigDecimal changeCountMax;
    /**
     * 变更数量大
     */
    private BigDecimal changeCountMin;
    /**
     * 原库存
     */
    private BigDecimal sourceStoreCountMax;
    /**
     * 原库存
     */
    private BigDecimal sourceStoreCountMin;
    /**
     * 新库存
     */
    private BigDecimal newStoreCountMax;
    /**
     * 新库存
     */
    private BigDecimal newStoreCountMin;
    /**
     * 事件类型
     */
    private String jiupiEventTypeName;
    /**
     * 创建人
     */
    private String createUser;

    /**
     * erp事件类型
     */
    private String erpEventTypeName;

    private Integer orderType;

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getJiupiEventTypeName() {
        return jiupiEventTypeName;
    }

    public void setJiupiEventTypeName(String jiupiEventTypeName) {
        this.jiupiEventTypeName = jiupiEventTypeName;
    }

    public BigDecimal getSourceStoreCountMax() {
        return sourceStoreCountMax;
    }

    public void setSourceStoreCountMax(BigDecimal sourceStoreCountMax) {
        this.sourceStoreCountMax = sourceStoreCountMax;
    }

    public BigDecimal getSourceStoreCountMin() {
        return sourceStoreCountMin;
    }

    public void setSourceStoreCountMin(BigDecimal sourceStoreCountMin) {
        this.sourceStoreCountMin = sourceStoreCountMin;
    }

    public BigDecimal getNewStoreCountMax() {
        return newStoreCountMax;
    }

    public void setNewStoreCountMax(BigDecimal newStoreCountMax) {
        this.newStoreCountMax = newStoreCountMax;
    }

    public BigDecimal getNewStoreCountMin() {
        return newStoreCountMin;
    }

    public void setNewStoreCountMin(BigDecimal newStoreCountMin) {
        this.newStoreCountMin = newStoreCountMin;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getChangeCountMax() {
        return changeCountMax;
    }

    public void setChangeCountMax(BigDecimal changeCountMax) {
        this.changeCountMax = changeCountMax;
    }

    public BigDecimal getChangeCountMin() {
        return changeCountMin;
    }

    public void setChangeCountMin(BigDecimal changeCountMin) {
        this.changeCountMin = changeCountMin;
    }

    public String getOrderTypeName() {
        return orderTypeName;
    }

    public void setOrderTypeName(String orderTypeName) {
        this.orderTypeName = orderTypeName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getChangeCount() {
        return changeCount;
    }

    public void setChangeCount(BigDecimal changeCount) {
        this.changeCount = changeCount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getErpEventTypeName() {
        return erpEventTypeName;
    }

    public void setErpEventTypeName(String erpEventTypeName) {
        this.erpEventTypeName = erpEventTypeName;
    }
}
