package com.yijiupi.himalaya.supplychain.inventory.dto.fee;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存存储信息
 */
public class InventoryStorageDTO implements Serializable {

    /**
     * 批次库存变更记录id
     */
    private String batchChangeRecordId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单类型
     */
    private Byte orderType;

    /**
     * 事件类型
     */
    private Byte jiuPiEventType;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * 扣减数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 订单出库时间
     */
    private Date orderOutStockTime;

    /**
     * 入库批次时间
     */
    private Date batchTime;

    /**
     * 规格id
     */
    private Long productSpecificationId;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 存储天数
     */
    private Double storageTime;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Byte getOrderType() {
        return orderType;
    }

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    public Byte getJiuPiEventType() {
        return jiuPiEventType;
    }

    public void setJiuPiEventType(Byte jiuPiEventType) {
        this.jiuPiEventType = jiuPiEventType;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Date getOrderOutStockTime() {
        return orderOutStockTime;
    }

    public void setOrderOutStockTime(Date orderOutStockTime) {
        this.orderOutStockTime = orderOutStockTime;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Double getStorageTime() {
        if (storageTime == null) {
            long difference = (orderOutStockTime.getTime() - batchTime.getTime()) / 86400000;
            storageTime = Math.ceil(Math.abs(difference));
        }
        return storageTime;
    }

    public void setStorageTime(Double storageTime) {
        this.storageTime = storageTime;
    }

    public String getBatchChangeRecordId() {
        return batchChangeRecordId;
    }

    public void setBatchChangeRecordId(String batchChangeRecordId) {
        this.batchChangeRecordId = batchChangeRecordId;
    }
}
