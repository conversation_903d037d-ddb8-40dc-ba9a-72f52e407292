package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.List;

import com.yijiupi.himalaya.supplychain.inventory.dto.DeliveryProductInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;

/**
 * 批次出库 变更库存
 */
public interface IBatchOutBoundChangeInventoryService {

    /**
     * 批次确认出库
     * 
     * @return
     */
    List<InventoryDeliveryJiupiOrder> affirmOutStock(List<OrderDTO> orders);

    /**
     * 校验发货库存数量
     */
    List<DeliveryProductInventoryDTO> validateOrderDeliveryProductInventory(List<OrderDTO> orders,
        String orderDeliveryOpType);

}
