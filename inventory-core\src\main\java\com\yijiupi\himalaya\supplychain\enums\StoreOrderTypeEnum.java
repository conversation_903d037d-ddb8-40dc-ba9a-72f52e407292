package com.yijiupi.himalaya.supplychain.enums;

/**
 * 仓库订单类型
 */
public enum StoreOrderTypeEnum {
    /**
     * 订单
     */
    订单(0),
    /**
     * 合作商订单
     */
    合作商订单(1),
    /**
     * 退货单
     */
    退货单(2),
    /**
     * 入库单
     */
    入库单(3),
    /**
     * 退货出库单
     */
    退货出库单(4),
    /**
     * 销售出库单
     */
    销售出库单(5),
    /**
     * 退货入库单
     */
    退货入库单(6),
    /**
     * 库存盘点单
     */
    库存盘点单(7),
    /**
     * 破损出库单
     */
    破损出库单(8),
    /**
     * 其他出库单
     */
    其他出库单(9),
    /**
     * 物料调拨单
     */
    物料调拨单(10),
    /**
     * 兑奖单
     */
    兑奖单(11),
    /**
     * 易经销配送单
     */
    易经销配送单(12),
    /**
     * 易经销出库单
     */
    易经销出库单(13),
    /**
     * 处理品转入
     */
    处理品转入(15),
    /**
     * 处理品转出
     */
    处理品转出(16),
    /**
     * 盘点单
     */
    盘点单(17),
    /**
     * 其他入库单
     */
    其他入库单(18),
    /**
     * 易酒批零团购订单
     */
    易酒批零团购订单(24),
    /**
     * 兑奖配送单
     */
    兑奖配送单(25),
    /**
     * 临期产品订单
     */
    临期产品订单(30),
    /**
     * 大商转配送
     */
    大商转配送(31),
    /**
     * 轻加盟订单
     */
    轻加盟订单(32),
    /**
     * 经销商订单
     */
    经销商订单(33),
    /**
     * 团购直营
     */
    团购直营(34),
    /**
     * 拼团订单
     */
    拼团订单(35),
    /**
     * 第三方入库
     */
    第三方入库(50),
    /**
     * 第三方出库
     */
    第三方出库(51),
    /**
     * 同城调拨转入
     */
    同城调拨转入(52),
    /**
     * 同城调拨转出
     */
    同城调拨转出(53),
    /**
     * 陈列品转出
     */
    陈列品转出(58),
    /**
     * 陈列品转入
     */
    陈列品转入(61),
    /**
     * 知花知果加工单
     */
    知花知果加工单(80),
    /**
     * 知花知果订单
     */
    知花知果订单(81),
    /**
     * 矫正库存
     */
    矫正库存(98),
    /**
     * ERP手动处理
     */
    ERP手动处理(99),

    /**
     * OP手动处理(100)
     */
    OP_MANUAL_ORDER(100),

    /**
     * 第三方出库
     */
    TRANSFER_OUT_ORDER(101),

    /**
     * 第三方入库
     */
    TRANSFER_IN_ORDER(102),

    /**
     * 微酒出库
     */
    WJ_TRANSFER_OUT_ORDER(103),

    /**
     * 微酒入库
     */
    WJ_TRANSFER_IN_ORDER(104),

    调拨出库(105), 易款便利线下单(116), 易款便利线下退货单(118), 移库单(119);

    private Integer type;

    StoreOrderTypeEnum(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    /**
     * 返回枚举
     *
     * @param type
     * @return
     */
    public static StoreOrderTypeEnum getEnum(Integer type) {
        StoreOrderTypeEnum e = null;

        if (type != null) {
            for (StoreOrderTypeEnum o : values()) {
                if (o.getType().equals(type)) {
                    e = o;
                }
            }
        }
        return e;
    }

    @Override
    public String toString() {
        return String.valueOf(this.type);
    }
}
