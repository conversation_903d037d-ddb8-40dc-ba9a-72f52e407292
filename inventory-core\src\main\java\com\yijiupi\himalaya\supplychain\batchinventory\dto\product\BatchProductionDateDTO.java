package com.yijiupi.himalaya.supplychain.batchinventory.dto.product;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品批次生产日期
 */
public class BatchProductionDateDTO implements Serializable {

    private static final long serialVersionUID = 2592874958284457113L;
    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * 货主ID
     */
    private Long ownerId;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    /**
     * 货主类型： 易酒批 = 0, 合作商 = 1, 入驻商 = 2, 微酒 = 3, 知花知果 = 4, 易款连锁 = 5
     */
    private Byte ownerType;

    /**
     * 生产日期你
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Byte getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Byte ownerType) {
        this.ownerType = ownerType;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }
}
