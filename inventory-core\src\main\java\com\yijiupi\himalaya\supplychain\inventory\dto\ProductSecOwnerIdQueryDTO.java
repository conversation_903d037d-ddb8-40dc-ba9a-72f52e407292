package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 根据规格+货主查找对应的二级货主
 *
 * <AUTHOR>
 * @date 2020-09-10 14:20
 */
public class ProductSecOwnerIdQueryDTO implements Serializable {

    private static final long serialVersionUID = 3337567276691287511L;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 规格id和ownerId集合
     */
    private List<ProductSpecAndOwnerIdDTO> specAndOwnerIds;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<ProductSpecAndOwnerIdDTO> getSpecAndOwnerIds() {
        return specAndOwnerIds;
    }

    public void setSpecAndOwnerIds(List<ProductSpecAndOwnerIdDTO> specAndOwnerIds) {
        this.specAndOwnerIds = specAndOwnerIds;
    }
}
