package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 2018/4/10
 */
public class BatchAttributeRuleReturnDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 模板id
     */
    private Long templateId;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 适用仓库仓库
     */
    private List<BatchAttributeRuleRelationDTO> warehouseRelationList;
    /**
     * 适用货主
     */
    private List<BatchAttributeRuleRelationDTO> ownerRelationList;
    /**
     * 适用类目
     */
    private List<BatchAttributeRuleRelationDTO> categoryRelationList;
    /**
     * 适用品牌
     */
    private List<BatchAttributeRuleRelationDTO> brandRelationList;

    /**
     * 适用服务商
     */
    private List<BatchAttributeRuleRelationDTO> serviceProviderList;

    /**
     * 获取 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 模板id
     */
    public Long getTemplateId() {
        return this.templateId;
    }

    /**
     * 设置 模板id
     */
    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    /**
     * 获取 模板名称
     */
    public String getTemplateName() {
        return this.templateName;
    }

    /**
     * 设置 模板名称
     */
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    /**
     * 获取 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 适用仓库仓库
     */
    public List<BatchAttributeRuleRelationDTO> getWarehouseRelationList() {
        return this.warehouseRelationList;
    }

    /**
     * 设置 适用仓库仓库
     */
    public void setWarehouseRelationList(List<BatchAttributeRuleRelationDTO> warehouseRelationList) {
        this.warehouseRelationList = warehouseRelationList;
    }

    /**
     * 获取 适用货主
     */
    public List<BatchAttributeRuleRelationDTO> getOwnerRelationList() {
        return this.ownerRelationList;
    }

    /**
     * 设置 适用货主
     */
    public void setOwnerRelationList(List<BatchAttributeRuleRelationDTO> ownerRelationList) {
        this.ownerRelationList = ownerRelationList;
    }

    /**
     * 获取 适用类目
     */
    public List<BatchAttributeRuleRelationDTO> getCategoryRelationList() {
        return this.categoryRelationList;
    }

    /**
     * 设置 适用类目
     */
    public void setCategoryRelationList(List<BatchAttributeRuleRelationDTO> categoryRelationList) {
        this.categoryRelationList = categoryRelationList;
    }

    /**
     * 获取 适用品牌
     */
    public List<BatchAttributeRuleRelationDTO> getBrandRelationList() {
        return this.brandRelationList;
    }

    /**
     * 设置 适用品牌
     */
    public void setBrandRelationList(List<BatchAttributeRuleRelationDTO> brandRelationList) {
        this.brandRelationList = brandRelationList;
    }

    public List<BatchAttributeRuleRelationDTO> getServiceProviderList() {
        return serviceProviderList;
    }

    public void setServiceProviderList(List<BatchAttributeRuleRelationDTO> serviceProviderList) {
        this.serviceProviderList = serviceProviderList;
    }

    /**
     * 获取 创建时间
     */
    public String getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}
