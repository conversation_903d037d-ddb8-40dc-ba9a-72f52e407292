package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 入库费用
 * 
 * @author: lidengfeng
 * @date 2018/10/9 19:42
 */
public class InStockCharge implements Serializable {

    /**
     * 分拣费
     */
    private BigDecimal sortingfee;

    /**
     * 下货费
     */
    private BigDecimal shipGoodsAmount;

    public BigDecimal getSortingfee() {
        return sortingfee;
    }

    public void setSortingfee(BigDecimal sortingfee) {
        this.sortingfee = sortingfee;
    }

    public BigDecimal getShipGoodsAmount() {
        return shipGoodsAmount;
    }

    public void setShipGoodsAmount(BigDecimal shipGoodsAmount) {
        this.shipGoodsAmount = shipGoodsAmount;
    }
}
