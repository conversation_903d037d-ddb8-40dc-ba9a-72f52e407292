package com.yijiupi.himalaya.supplychain.inventory.dto;

/**
 * 库存报表-历史变更记录
 * 
 * @author: cheng<PERSON>
 * @date: 2022年9月6日
 */
public class FindStoreChangePageDTO {
    private Integer userId;
    /**
     * 产品库存ID
     */
    private String productStoreId;
    /**
     * 变更开始时间
     */
    private String startTime;
    /**
     * 变更结束时间
     */
    private String endTime;
    /**
     * 变更类型
     */
    private Integer orderType;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     * 产品SKUID
     */
    private Long productSkuId;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
