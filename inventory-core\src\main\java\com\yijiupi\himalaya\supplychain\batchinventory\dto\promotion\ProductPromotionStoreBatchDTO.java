package com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 促销批次库存
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
public class ProductPromotionStoreBatchDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 批属性编号
     */
    private String batchAttributeInfoNo;
    /**
     * 仓id
     */
    private Integer warehouseId;
    /**
     * skuid
     */
    private Long skuId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;
    /**
     * 是否删除
     */
    private Byte isDelete;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后修改人
     */
    private String lastUpdateUser;
    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 批次库存ids
     */
    private List<String> storeBatchIds;

    /**
     * 是否新增产品关联货位待办
     */
    private boolean addLocationTodoTask;

    /**
     * 分仓属性
     */
    private Byte storageAttribute;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBatchAttributeInfoNo() {
        return batchAttributeInfoNo;
    }

    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Byte getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getIdentityKey() {
        return String.format("%s-%s", getWarehouseId(), getBatchAttributeInfoNo());
    }

    public List<String> getStoreBatchIds() {
        return storeBatchIds;
    }

    public void setStoreBatchIds(List<String> storeBatchIds) {
        this.storeBatchIds = storeBatchIds;
    }

    public boolean getAddLocationTodoTask() {
        return addLocationTodoTask;
    }

    public void setAddLocationTodoTask(boolean addLocationTodoTask) {
        this.addLocationTodoTask = addLocationTodoTask;
    }

    public Byte getStorageAttribute() {
        return storageAttribute;
    }

    public void setStorageAttribute(Byte storageAttribute) {
        this.storageAttribute = storageAttribute;
    }
}
