package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 配送单.
 *
 * <AUTHOR>
 */
public class InventoryDeliveryJiupiOrder implements Serializable {
    private static final long serialVersionUID = 771306548606709780L;

    /**
     * 配送单ID.
     */
    private Long id;
    /**
     * oms订单id
     */
    private Long omsOrderId;
    /**
     * 订单ID.
     */
    private Long orderId;
    /**
     * 订单NO.
     */
    private String orderNo;
    /**
     * 发货城市ID.
     */
    private Integer cityId;
    /**
     * 下单城市Id
     */
    private Integer fromCityId;
    /**
     * 发货仓库ID
     */
    private Integer warehouseId;
    /**
     * 下单仓库ID
     */
    private Integer fromWarehouseId;
    /**
     * 配送项.
     */
    private List<InventoryDeliveryJiupiOrderItem> items;

    /**
     * 创建人id
     */
    private String createUserId;
    /**
     * 创建人名称
     */
    private String createUserName;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 酒批订单类型（ordertype为酒批订单的，细分订单类型）
     */
    private Integer jiupiOrderType;
    /**
     * 酒批事件类型
     */
    private Integer jiupiEventType;
    /**
     * 酒批=0,合作商=1
     */
    private Integer pickupType;

    /**
     * 库存标识key
     */
    private String inventoryOptKey;

    /**
     * 配送方式（0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    private Integer deliveryMode;

    /**
     * 是否自提
     */
    private Boolean selfLifting;

    /**
     * 订单属性
     */
    private Byte packageAttribute;
    /**
     * 货主ID.
     */
    private Long ownerId;

    /**
     * 备注
     */
    private String description;

    /**
     * WMS系统单据ID
     */
    private Long relationOrderId;

    /**
     * 是否需要提前拣货
     */
    private Boolean needWavePicking;

    /**
     * 调拨类型
     */
    private Byte allotType;

    /**
     * 是否跨库
     */
    private Boolean crossWareHouse;

    /**
     * 出库模式(0:城际，1：本地)
     */
    private Byte outStockMode;

    /**
     * 订单能力类型
     */
    private Byte capabilityType;

    /**
     * 订单来源 {@link com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.SourceType}
     */
    private Byte orderSourceType;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getOmsOrderId() {
        return omsOrderId;
    }

    public void setOmsOrderId(Long omsOrderId) {
        this.omsOrderId = omsOrderId;
    }

    public Boolean getSelfLifting() {
        return selfLifting;
    }

    public void setSelfLifting(Boolean selfLifting) {
        this.selfLifting = selfLifting;
    }

    public String getInventoryOptKey() {
        return inventoryOptKey;
    }

    public void setInventoryOptKey(String inventoryOptKey) {
        this.inventoryOptKey = inventoryOptKey;
    }

    /**
     * 获取订单在库存处理过程中的主键
     *
     * @return
     */
    public String getOrderInventoryIdentityKey(String messageType) {
        return messageType + "" + getOrderId();
    }

    public Integer getJiupiOrderType() {
        return jiupiOrderType;
    }

    public void setJiupiOrderType(Integer jiupiOrderType) {
        this.jiupiOrderType = jiupiOrderType;
    }

    /**
     * 获取 订单ID.
     */
    public Long getOrderId() {
        return orderId;
    }

    /**
     * 设置 订单ID.
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 订单NO.
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * 设置 订单NO.
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 城市ID.
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市ID.
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库ID.
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库ID.
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 配送项.
     */
    public List<InventoryDeliveryJiupiOrderItem> getItems() {
        return items;
    }

    /**
     * 设置 配送项.
     */
    public void setItems(List<InventoryDeliveryJiupiOrderItem> items) {
        this.items = items;
    }

    /**
     * 获取 配送单ID.
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 配送单ID.
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 创建人名称
     *
     * @return createUserName 创建人名称
     */
    public String getCreateUserName() {
        return this.createUserName;
    }

    /**
     * 设置 创建人名称
     *
     * @param createUserName 创建人名称
     */
    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    /**
     * 获取 创建人id
     *
     * @return createUserId 创建人id
     */
    public String getCreateUserId() {
        return this.createUserId;
    }

    /**
     * 设置 创建人id
     *
     * @param createUserId 创建人id
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取 酒批=0合作商=1
     *
     * @return pickupType 酒批=0合作商=1
     */
    public Integer getPickupType() {
        return this.pickupType;
    }

    /**
     * 设置 酒批=0合作商=1
     *
     * @param pickupType 酒批=0合作商=1
     */
    public void setPickupType(Integer pickupType) {
        this.pickupType = pickupType;
    }

    public Integer getFromCityId() {
        return fromCityId;
    }

    public void setFromCityId(Integer fromCityId) {
        this.fromCityId = fromCityId;
    }

    /**
     * 获取 酒批事件类型
     */
    public Integer getJiupiEventType() {
        return this.jiupiEventType;
    }

    /**
     * 设置 酒批事件类型
     */
    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    /**
     * 获取 配送方式（0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    public Integer getDeliveryMode() {
        return this.deliveryMode;
    }

    /**
     * 设置 配送方式（0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    public void setDeliveryMode(Integer deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    /**
     * 获取 订单类型
     */
    public Integer getOrderType() {
        return this.orderType;
    }

    /**
     * 设置 订单类型
     */
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Byte getPackageAttribute() {
        return packageAttribute;
    }

    public void setPackageAttribute(Byte packageAttribute) {
        this.packageAttribute = packageAttribute;
    }

    /**
     * @return the 货主ID.
     */
    public Long getOwnerId() {
        return ownerId;
    }

    /**
     * @param 货主ID. the ownerId to set
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getRelationOrderId() {
        return relationOrderId;
    }

    public void setRelationOrderId(Long relationOrderId) {
        this.relationOrderId = relationOrderId;
    }

    public Boolean getNeedWavePicking() {
        return needWavePicking;
    }

    public void setNeedWavePicking(Boolean needWavePicking) {
        this.needWavePicking = needWavePicking;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public Integer getFromWarehouseId() {
        return fromWarehouseId;
    }

    public void setFromWarehouseId(Integer fromWarehouseId) {
        this.fromWarehouseId = fromWarehouseId;
    }

    public String getOrderInventoryIdentityKey(Integer warehouseId, String messageType) {
        return messageType + "" + warehouseId + getOrderNo();
    }

    public Boolean getCrossWareHouse() {
        return crossWareHouse;
    }

    public void setCrossWareHouse(Boolean crossWareHouse) {
        this.crossWareHouse = crossWareHouse;
    }

    public Byte getOutStockMode() {
        return outStockMode;
    }

    public void setOutStockMode(Byte outStockMode) {
        this.outStockMode = outStockMode;
    }

    public Byte getCapabilityType() {
        return capabilityType;
    }

    public void setCapabilityType(Byte capabilityType) {
        this.capabilityType = capabilityType;
    }

    public Byte getOrderSourceType() {
        return orderSourceType;
    }

    public void setOrderSourceType(Byte orderSourceType) {
        this.orderSourceType = orderSourceType;
    }
}