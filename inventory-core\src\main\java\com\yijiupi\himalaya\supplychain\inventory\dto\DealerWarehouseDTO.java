package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * 经销商仓配过滤返回DTO
 * 
 * @author: lidengfeng
 * @date 2018/9/27 15:46
 */
public class DealerWarehouseDTO implements Serializable {

    /**
     * 经销商id
     */
    private String dealerId;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 地址
     */
    private String detailAddress;

    /**
     * @Fields isGetWarehouseCharge 是否收取仓配费用 1为收取 0为不收取
     */
    private Byte isGetWarehouseCharge;
    /**
     * @Fields businessType 业务类型 0=正常业务 1=贷款业务
     */
    private Byte businessType;
    /**
     * @Fields status 状态 0=停用 1=启用
     */
    private Byte status;
    /**
     * @Fields mobileNo 经销商手机号
     */
    private String mobileNo;

    /**
     * 首次服务时间
     */
    private String firstServiceTime;

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public String getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(String dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取 城市id
     * 
     * @return
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市id
     * 
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 仓库名称
     * 
     * @return
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置 仓库名称
     * 
     * @param warehouseName
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    /**
     * 获取 详细地址
     * 
     * @return
     */
    public String getDetailAddress() {
        return detailAddress;
    }

    /**
     * 设置 详细地址
     * 
     * @param detailAddress
     */
    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    /**
     * 获取 首次服务时间
     * 
     * @return
     */
    public String getFirstServiceTime() {
        return firstServiceTime;
    }

    /**
     * 设置 首次服务时间
     * 
     * @param firstServiceTime
     */
    public void setFirstServiceTime(String firstServiceTime) {
        this.firstServiceTime = firstServiceTime;
    }

    /**
     * 获取 是否收取仓配费用 1为收取 0为不收取
     */
    public Byte getIsGetWarehouseCharge() {
        return isGetWarehouseCharge;
    }

    /**
     * 设置 是否收取仓配费用 1为收取 0为不收取
     */
    public void setIsGetWarehouseCharge(Byte isGetWarehouseCharge) {
        this.isGetWarehouseCharge = isGetWarehouseCharge;
    }

    /**
     * 获取 业务类型 0=正常业务 1=贷款业务
     */
    public Byte getBusinessType() {
        return businessType;
    }

    /**
     * 设置 业务类型 0=正常业务 1=贷款业务
     */
    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取 状态 0=停用 1=启用
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置 状态 0=停用 1=启用
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取 经销商手机号
     */
    public String getMobileNo() {
        return mobileNo;
    }

    /**
     * 设置 经销商手机号
     */
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    /**
     * 获取 城市
     * 
     * @return
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置 城市
     * 
     * @param city
     */
    public void setCity(String city) {
        this.city = city;
    }
}
