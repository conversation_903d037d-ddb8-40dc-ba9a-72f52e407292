package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * 仓库管理 仓库库存查询入参
 * 
 * @author: lidengfeng
 * @date 2018/12/5 14:36
 */
public class WarehouseServiceStoreQuery implements Serializable {

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 经销商手机号
     */
    private String mobileNo;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页的数量
     */
    private Integer pageSize;

    /**
     * 获取手机号
     * 
     * @return
     */
    public String getMobileNo() {
        return mobileNo;
    }

    /**
     * 设置手机号
     * 
     * @param mobileNo
     */
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    /**
     * 获取城市id
     * 
     * @return
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置城市id
     * 
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取页码
     * 
     * @return
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 设置页码
     * 
     * @param pageNum
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 获取每页的数量
     * 
     * @return
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 设置每页的数量
     * 
     * @param pageSize
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 获取服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }
}
