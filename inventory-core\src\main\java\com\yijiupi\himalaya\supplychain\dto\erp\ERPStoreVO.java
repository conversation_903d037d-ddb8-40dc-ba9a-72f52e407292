package com.yijiupi.himalaya.supplychain.dto.erp;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by 余明 on 2018-07-30.
 */
public class ERPStoreVO implements Serializable {
    private static final long serialVersionUID = 6361351188081918591L;
    /**
     * 未审核采购入库单数量
     */
    private BigDecimal buyCount;

    /**
     * 未审核采购退货单数量
     */
    private BigDecimal buyReturnCount;

    /**
     * 处理品数量
     */
    private BigDecimal disposedOutCount;

    /**
     * ERP库存列表显示数量
     */
    private BigDecimal erpDisplayCount;

    /**
     * 经计算后，ERP实际库存数量
     */
    private BigDecimal erpRealCount;

    /**
     * 其他入库数量（暂时都为0，保留字段）
     */
    private BigDecimal otherInCount;

    /**
     * 其他出库数量（破损、招待、客情、福利等）
     */
    private BigDecimal otherOutCount;

    /**
     * sku 包装数量
     */
    private BigDecimal packageQuantity;

    /**
     * skuid （2.0）
     */
    private Long productSkuId;

    /**
     * 未审核销售出库单数量
     */
    private BigDecimal saleCount;

    /**
     * 未审核退货入库单数量
     */
    private BigDecimal saleReturnCount;

    /**
     * 同城调拨 调入数量
     */
    private BigDecimal storeHouseAllocationInCount;

    /**
     * 同城调拨 调出数量
     */
    private BigDecimal storeHouseAllocationOutCount;

    /**
     * 未下推销售出库单数量
     */
    private BigDecimal unCreateNoteSaleCount;

    /**
     * 未下推退货入库单数量
     */
    private BigDecimal unCreateNoteSaleReturnCount;

    /**
     * 仓库Id（2.0）
     */
    private Integer warehouseId;

    private Long productSpecificationId;

    private Long ownerId;

    private String secOwnerId;

    /**
     * 产品名称
     */
    private String productName;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(String secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public BigDecimal getBuyCount() {
        return buyCount;
    }

    public void setBuyCount(BigDecimal buyCount) {
        this.buyCount = buyCount;
    }

    public BigDecimal getBuyReturnCount() {
        return buyReturnCount;
    }

    public void setBuyReturnCount(BigDecimal buyReturnCount) {
        this.buyReturnCount = buyReturnCount;
    }

    public BigDecimal getDisposedOutCount() {
        return disposedOutCount;
    }

    public void setDisposedOutCount(BigDecimal disposedOutCount) {
        this.disposedOutCount = disposedOutCount;
    }

    public BigDecimal getErpDisplayCount() {
        return erpDisplayCount;
    }

    public void setErpDisplayCount(BigDecimal erpDisplayCount) {
        this.erpDisplayCount = erpDisplayCount;
    }

    public BigDecimal getErpRealCount() {
        return erpRealCount;
    }

    public void setErpRealCount(BigDecimal erpRealCount) {
        this.erpRealCount = erpRealCount;
    }

    public BigDecimal getOtherInCount() {
        return otherInCount;
    }

    public void setOtherInCount(BigDecimal otherInCount) {
        this.otherInCount = otherInCount;
    }

    public BigDecimal getOtherOutCount() {
        return otherOutCount;
    }

    public void setOtherOutCount(BigDecimal otherOutCount) {
        this.otherOutCount = otherOutCount;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public BigDecimal getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(BigDecimal saleCount) {
        this.saleCount = saleCount;
    }

    public BigDecimal getSaleReturnCount() {
        return saleReturnCount;
    }

    public void setSaleReturnCount(BigDecimal saleReturnCount) {
        this.saleReturnCount = saleReturnCount;
    }

    public BigDecimal getStoreHouseAllocationInCount() {
        return storeHouseAllocationInCount;
    }

    public void setStoreHouseAllocationInCount(BigDecimal storeHouseAllocationInCount) {
        this.storeHouseAllocationInCount = storeHouseAllocationInCount;
    }

    public BigDecimal getStoreHouseAllocationOutCount() {
        return storeHouseAllocationOutCount;
    }

    public void setStoreHouseAllocationOutCount(BigDecimal storeHouseAllocationOutCount) {
        this.storeHouseAllocationOutCount = storeHouseAllocationOutCount;
    }

    public BigDecimal getUnCreateNoteSaleCount() {
        return unCreateNoteSaleCount;
    }

    public void setUnCreateNoteSaleCount(BigDecimal unCreateNoteSaleCount) {
        this.unCreateNoteSaleCount = unCreateNoteSaleCount;
    }

    public BigDecimal getUnCreateNoteSaleReturnCount() {
        return unCreateNoteSaleReturnCount;
    }

    public void setUnCreateNoteSaleReturnCount(BigDecimal unCreateNoteSaleReturnCount) {
        this.unCreateNoteSaleReturnCount = unCreateNoteSaleReturnCount;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    @Override
    public String toString() {
        return "SkuId：" + getProductSkuId() + "\t仓库Id：" + getWarehouseId() + "\t包装数量：" + getPackageQuantity()
            + "\tERP实际库存数量：" + getErpRealCount() + "\t库存列表显示数量：" + getErpDisplayCount() + "\t未审核采购入库单数量："
            + getBuyCount() + "\t未审核采购退货单数量：" + getBuyReturnCount() + "\t处理品数量：" + getDisposedOutCount()
            + "\t未审核销售出库单数量：" + getSaleCount() + "\t未审核退货入库单数量：" + getSaleReturnCount() + "\t同城调拨调入数量："
            + getStoreHouseAllocationInCount() + "\t调出数量：" + getStoreHouseAllocationOutCount() + "\t未下推销售出库单数量："
            + getUnCreateNoteSaleCount() + "\t未下推退货入库单数量：" + getUnCreateNoteSaleReturnCount() + "\t其他入库数量："
            + getOtherInCount() + "\t其他出库数量（破损、招待、客情、福利等）：" + getOtherOutCount();
    }
}
