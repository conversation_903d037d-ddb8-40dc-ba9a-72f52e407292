package com.yijiupi.himalaya.supplychain.search.standard;

import java.io.Serializable;
import java.util.List;

/**
 * 获取产品对应城市库存
 * 
 * @author: tangkun
 * @date: 2017年4月14日 上午10:22:41
 */
public class ProductSpecCityListStoreSO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<Integer> cityIds;

    private Long productSpecId;

    private Integer channel;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    public List<Integer> getCityIds() {
        return cityIds;
    }

    public void setCityIds(List<Integer> cityIds) {
        this.cityIds = cityIds;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

}
