package com.yijiupi.himalaya.supplychain.batchinventory.service;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.BatchProductInfoDTO;

import java.util.List;

/**
 * 批属性模板管理
 *
 * <AUTHOR> 2018/4/9
 */
public interface IBatchAttributeTemplateService {
    /**
     * 新增批属性模板管理
     *
     * @param batchAttributeTemplateDTO
     */
    void addBatchAttributeTemplate(BatchAttributeTemplateDTO batchAttributeTemplateDTO);

    /**
     * 编辑
     *
     * @param batchAttributeTemplateDTO
     */
    void updateBatchAttributeTemplate(BatchAttributeTemplateDTO batchAttributeTemplateDTO);

    /**
     * 批属性模板管理列表
     *
     * @param batchAttributeTemplateQueryDTO
     * @return
     */
    PageList<BatchAttributeTemplateReturnDTO>
        findBatchAttributeTemplateList(BatchAttributeTemplateQueryDTO batchAttributeTemplateQueryDTO);

    /**
     * 停用,启用
     *
     * @param batchAttributeTemplateDTO
     */
    void updateBatchAttributeTemplateState(BatchAttributeTemplateDTO batchAttributeTemplateDTO);

    /**
     * 删除
     */
    void deleteById(Long id);

    /**
     * 根据产品信息查询批属性应填属性
     *
     * @param batchProductInfoDTO
     * @return
     */
    @Deprecated
    List<BatchAttributeTemplateRelationReturnDTO>
        findBatchAttributeTemplateRelation(BatchProductInfoDTO batchProductInfoDTO);

    /**
     * 查询类目和仓库绑定的批属性字典(可能查不到)
     *
     * @param batchAttributeEnableQueryDTO
     * @return
     */
    List<BatchAttributeTemplateRelationReturnDTO>
        findBatchAttributeEnable(BatchAttributeEnableQueryDTO batchAttributeEnableQueryDTO);

    /**
     * 根据配置类型和属性值Id查询批属性字典
     */
    List<BatchAttributeTemplateRelationReturnDTO>
        findAttributeTemplateByRuleType(BatchAttributeTemplateRuleTypeQueryDTO ruleTypeQueryDTO);
}
