package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 库存对账结果记录
 */
public class InventorySyncRecordDTO implements Serializable {
    private static final long serialVersionUID = -5077755734080284846L;

    /**
     * 记录id
     */
    private Long id;

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * TMS已发货数量
     */
    private BigDecimal tmsDeliveryedCount;

    /**
     * WMS已发货数量
     */
    private BigDecimal wmsDeliveryedCount;

    /**
     * ERP计算结果
     */
    private BigDecimal erpRealCount;

    /**
     * 校正前总库存
     */
    private BigDecimal storeCountMinUnit;

    /**
     * 库存差异总数量
     */
    private BigDecimal diffTotalCount;

    /**
     * 差异库存数量(大单位)
     */
    private BigDecimal diffMaxCount;

    /**
     * 差异库存数量(小单位)
     */
    private BigDecimal diffMinCount;

    /**
     * 产品SKUID
     */
    private Long productSkuId;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * SKU名称
     */
    private String productName;

    /**
     * 包装规格名称
     */
    private String specName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 经销商或者合作商id
     */
    private Long ownerId;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 校正库存类型: 0. 销售库存 1.仓库库存
     */
    private Byte storeType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    private Integer createUserId;

    /**
     * 是否差异: 0 没差异 1 有差异
     */
    private Byte diff;

    /**
     * 校正状态: 0 不需要处理 1 未处理 2 已处理
     */
    private Byte state;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    private String ownerName;

    private String secOwnerName;

    private String refSecOwnerId;

    private String productStoreId;

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public String getRefSecOwnerId() {
        return refSecOwnerId;
    }

    public void setRefSecOwnerId(String refSecOwnerId) {
        this.refSecOwnerId = refSecOwnerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getSecOwnerName() {
        return secOwnerName;
    }

    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Byte getDiff() {
        return diff;
    }

    public void setDiff(Byte diff) {
        this.diff = diff;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getTmsDeliveryedCount() {
        return tmsDeliveryedCount;
    }

    public void setTmsDeliveryedCount(BigDecimal tmsDeliveryedCount) {
        this.tmsDeliveryedCount = tmsDeliveryedCount;
    }

    public BigDecimal getWmsDeliveryedCount() {
        return wmsDeliveryedCount;
    }

    public void setWmsDeliveryedCount(BigDecimal wmsDeliveryedCount) {
        this.wmsDeliveryedCount = wmsDeliveryedCount;
    }

    public BigDecimal getErpRealCount() {
        return erpRealCount;
    }

    public void setErpRealCount(BigDecimal erpRealCount) {
        this.erpRealCount = erpRealCount;
    }

    public BigDecimal getStoreCountMinUnit() {
        return storeCountMinUnit;
    }

    public void setStoreCountMinUnit(BigDecimal storeCountMinUnit) {
        this.storeCountMinUnit = storeCountMinUnit;
    }

    public BigDecimal getDiffTotalCount() {
        return diffTotalCount;
    }

    public void setDiffTotalCount(BigDecimal diffTotalCount) {
        this.diffTotalCount = diffTotalCount;
    }

    public BigDecimal getDiffMaxCount() {
        return diffMaxCount;
    }

    public void setDiffMaxCount(BigDecimal diffMaxCount) {
        this.diffMaxCount = diffMaxCount;
    }

    public BigDecimal getDiffMinCount() {
        return diffMinCount;
    }

    public void setDiffMinCount(BigDecimal diffMinCount) {
        this.diffMinCount = diffMinCount;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Byte getStoreType() {
        return storeType;
    }

    public void setStoreType(Byte storeType) {
        this.storeType = storeType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }
}