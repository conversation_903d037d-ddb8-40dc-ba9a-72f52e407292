package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class ProductInventoryCheckDTO implements Serializable {

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 规格id
     */
    private Long specId;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 是否分配计算
     */
    private Boolean allocationCalculation;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Boolean getAllocationCalculation() {
        return allocationCalculation;
    }

    public void setAllocationCalculation(Boolean allocationCalculation) {
        this.allocationCalculation = allocationCalculation;
    }
}
