package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * 校正销售库存
 *
 * <AUTHOR>
 * @date 2019/1/14 17:45
 */
public class WarehouseInventoryCheckDTO implements Serializable {
    private static final long serialVersionUID = 8108466183765703931L;

    /**
     * 产品skuId
     */
    private Long productSkuId;

    /**
     * 所属人(货主)id
     */
    private Long ownerId;

    /**
     * 产品规格ID
     */
    private Integer productSpecificationId;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Integer productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }
}
