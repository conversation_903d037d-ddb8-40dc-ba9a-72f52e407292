<?xml version="1.0"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yijiupi</groupId>
        <artifactId>himalaya-supplychain-microservice-inventory</artifactId>
        <version>2.20.0</version>
    </parent>

    <artifactId>himalaya-supplychain-microservice-inventory-provider</artifactId>
    <properties>
        <start-class>com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp</start-class>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-uuid</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi.xxljob</groupId>
            <version>1.0.0-SNAPSHOT</version>
            <artifactId>basic-other-xxljob</artifactId>
        </dependency>

        <!--二方-->
        <dependency>
            <groupId>com.yijiupi.himalaya</groupId>
            <artifactId>business-audit</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>supplychain-lock-starter</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-inventory-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-orgsettings-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-warehouseproduct-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-outstockordersync-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-settings-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-storecheck-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-goodspursueoperate-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-waves-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-wmsdubbop-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-warehouse-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-user-core</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.yijiupi</groupId>-->
        <!--            <artifactId>himalaya-supplychain-microservice-productowner-core</artifactId>-->
        <!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.yijiupi</groupId>-->
<!--            <artifactId>himalaya-supplychain-microservice-productsync-core</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-sqlreport-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-assignment-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--三方-->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>24.0-jre</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-config</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-logger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-data-mysql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-allot-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-serviceutils</artifactId>
            <version>0.0.5-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-finance-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-supplychain-microservice-omsorderquery-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.22</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>ordercenter-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>himalaya-junit-test</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>3.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>microservice-project-proxy</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yijiupi</groupId>
            <artifactId>framework-rabbit</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>
                    src/main/resources
                </directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <attach>false</attach>
                    <descriptors>
                        <descriptor>src/main/assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>