package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;

/**
 * <AUTHOR> 库存变更dto
 */
public class ChangeInventoryDTO implements Serializable {
    private static final long serialVersionUID = -3120575651118789232L;
    /**
     * 城市id
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 订单列表
     */
    private List<OrderDTO> orderList;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<OrderDTO> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<OrderDTO> orderList) {
        this.orderList = orderList;
    }
}
