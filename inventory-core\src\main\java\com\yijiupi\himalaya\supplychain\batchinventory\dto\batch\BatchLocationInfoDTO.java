package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * 批次库存货区货位信息dto
 *
 * <AUTHOR> 2018/3/28
 */
public class BatchLocationInfoDTO implements Serializable {
    /**
     * 批次库存主键id
     */
    private String storeBatchId;
    /**
     * 产品skuId
     */
    private Long productSkuId;
    /**
     * 关联库存表Id
     */
    private String productStoreId;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位顺序
     */
    private Integer sequence;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * 货区或货位类型：0:货位，1:货区
     */
    private Byte locationCategory;
    /**
     * 货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）
     */
    private Byte subcategory;
    /**
     * 货区id
     */
    private Long areaId;
    /**
     * 货区名称
     */
    private String areaName;
    /**
     * 过期时间
     */
    private Date expireTime;
    /**
     * 批次时间
     */
    private Date batchTime;
    /**
     * 库存数量（按照小单位计算）
     */
    private BigDecimal totalCount;

    private String batchAttributeInfoNo;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 生产日期和入库日期之间的最小日期
     */
    private Date compareDate;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 所属人(货主)id
     */
    private Long ownerId;

    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 库存主键id
     */
    private String storeId;

    /**
     * 库存属性（0：默认，1：自动转入）
     */
    private Byte batchProperty;

    /**
     * 业务类型（0：默认，1：生产日期治理任务）
     */
    private Byte businessType;

    public Byte getBatchProperty() {
        return batchProperty;
    }

    public void setBatchProperty(Byte batchProperty) {
        this.batchProperty = batchProperty;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Date getCompareDate() {
        if (productionDate == null) {
            // 生产日期和入库日期都为空时，返回空
            if (batchTime == null) {
                return null;
                // 生产日期为空，入库日期不为空时，返回入库日期
            } else {
                return batchTime;
            }
            // 生产日期不为空，入库日期为空时，返回生产日期
        } else if (batchTime == null) {
            return productionDate;
            // 生产日期和入库日期都不为空时，返回最小值
        } else {
            return batchTime.before(productionDate) ? batchTime : productionDate;
        }
    }

    public void setCompareDate(Date compareDate) {
        this.compareDate = compareDate;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public String getProductionDateStr() {
        return productionDate == null ? "" : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(productionDate);
    }

    public String getBatchTimeStr() {
        return batchTime == null ? "" : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(batchTime);
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public String getBatchAttributeInfoNo() {
        return batchAttributeInfoNo;
    }

    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * 获取 关联库存表Id
     */
    public String getProductStoreId() {
        return this.productStoreId;
    }

    /**
     * 设置 关联库存表Id
     */
    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    /**
     * 获取 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 货区或货位类型：0:货位，1:货区
     */
    public Byte getLocationCategory() {
        return this.locationCategory;
    }

    /**
     * 设置 货区或货位类型：0:货位，1:货区
     */
    public void setLocationCategory(Byte locationCategory) {
        this.locationCategory = locationCategory;
    }

    /**
     * 货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）
     */
    public Byte getSubcategory() {
        return this.subcategory;
    }

    /**
     * 货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）
     */
    public void setSubcategory(Byte subcategory) {
        this.subcategory = subcategory;
    }

    /**
     * 获取 过期时间
     */
    public Date getExpireTime() {
        return this.expireTime;
    }

    /**
     * 设置 过期时间
     */
    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    /**
     * 获取 批次时间
     */
    public Date getBatchTime() {
        return this.batchTime;
    }

    /**
     * 设置 批次时间
     */
    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    /**
     * 获取 库存数量（按照小单位计算）
     */
    public BigDecimal getTotalCount() {
        return this.totalCount;
    }

    /**
     * 设置 库存数量（按照小单位计算）
     */
    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取 产品skuId
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品skuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 批次库存主键id
     */
    public String getStoreBatchId() {
        return this.storeBatchId;
    }

    /**
     * 设置 批次库存主键id
     */
    public void setStoreBatchId(String storeBatchId) {
        this.storeBatchId = storeBatchId;
    }

    /**
     * 获取 货位顺序
     */
    public Integer getSequence() {
        return this.sequence;
    }

    /**
     * 设置 货位顺序
     */
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    @Override
    public int hashCode() {
        return Objects.hash(locationId);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        final BatchLocationInfoDTO other = (BatchLocationInfoDTO)obj;
        return Objects.equals(this.locationId, other.locationId);
    }
}
