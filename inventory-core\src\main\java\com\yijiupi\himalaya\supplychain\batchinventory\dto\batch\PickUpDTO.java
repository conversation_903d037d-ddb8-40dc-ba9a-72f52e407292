package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 按产品拣货(上架下架)dto
 *
 * <AUTHOR> 2018/4/2
 */
public class PickUpDTO implements Serializable {
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 产品ID
     */
    private Long productSkuId;
    /**
     * 来源库存渠道,0:酒批，1:大宗产品
     */
    private Integer fromChannel = 0;
    /**
     * 目标库存渠道,0:酒批，1:大宗产品
     */
    private Integer toChannel = 0;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer fromSource = 0;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer toSource = 0;
    /**
     * 拣货数量
     */
    private BigDecimal count = BigDecimal.ZERO;
    /**
     * 来源货位id
     */
    private Long fromLocationId;
    /**
     * 目标货位id
     */
    private Long locationId;
    /**
     * 批次时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;
    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    /**
     * 是否自动分配库存 否：false 是：true
     */
    private Boolean autoAllotFlag = false;

    /**
     * 业务ID（用于区分分配库存）
     */
    private String businessId;

    /**
     * 来源货位名称
     */
    private String fromLocationName;

    /**
     * 目标货位名称
     */
    private String locationName;

    /**
     * 是否排除负库存 否：false 是：true
     */
    private Boolean excludeNegativeFlag;

    /**
     * 货位转货位组的操作中使用，关联转换前后的pickUpDTO
     */
    private String tempId;

    /**
     * 是否按实际数量移动 否：false 是：true
     */
    private Boolean moveByRealFlag = false;
    /**
     * 订单号
     */
    private String businessNo;
    /**
     * 产品信息规格id
     */
    private Long productSpecificationId;

    public String getTempId() {
        return tempId;
    }

    public void setTempId(String tempId) {
        this.tempId = tempId;
    }

    public Boolean getExcludeNegativeFlag() {
        return excludeNegativeFlag;
    }

    public void setExcludeNegativeFlag(Boolean excludeNegativeFlag) {
        this.excludeNegativeFlag = excludeNegativeFlag;
    }

    public String getFromLocationName() {
        return fromLocationName;
    }

    public void setFromLocationName(String fromLocationName) {
        this.fromLocationName = fromLocationName;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public Boolean getAutoAllotFlag() {
        return autoAllotFlag;
    }

    public void setAutoAllotFlag(Boolean autoAllotFlag) {
        this.autoAllotFlag = autoAllotFlag;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public String getProductionDateStr() {
        return productionDate == null ? "" : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(productionDate);
    }

    public String getBatchTimeStr() {
        return batchTime == null ? "" : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(batchTime);
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    /**
     * 获取 仓库ID
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库ID
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品ID
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品ID
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 拣货数量
     */
    public BigDecimal getCount() {
        return this.count;
    }

    /**
     * 设置 拣货数量
     */
    public void setCount(BigDecimal count) {
        this.count = count;
    }

    /**
     * 获取 来源货位id
     */
    public Long getFromLocationId() {
        return this.fromLocationId;
    }

    /**
     * 设置 来源货位id
     */
    public void setFromLocationId(Long fromLocationId) {
        this.fromLocationId = fromLocationId;
    }

    /**
     * 获取 目标货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 目标货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 来源库存渠道,0:酒批，1:大宗产品
     */
    public Integer getFromChannel() {
        return this.fromChannel;
    }

    /**
     * 设置 来源库存渠道,0:酒批，1:大宗产品
     */
    public void setFromChannel(Integer fromChannel) {
        this.fromChannel = fromChannel;
    }

    /**
     * 获取 目标库存渠道,0:酒批，1:大宗产品
     */
    public Integer getToChannel() {
        return this.toChannel;
    }

    /**
     * 设置 目标库存渠道,0:酒批，1:大宗产品
     */
    public void setToChannel(Integer toChannel) {
        this.toChannel = toChannel;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public Integer getFromSource() {
        return this.fromSource;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public void setFromSource(Integer fromSource) {
        this.fromSource = fromSource;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public Integer getToSource() {
        return this.toSource;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public void setToSource(Integer toSource) {
        this.toSource = toSource;
    }

    public Boolean getMoveByRealFlag() {
        return moveByRealFlag;
    }

    public void setMoveByRealFlag(Boolean moveByRealFlag) {
        this.moveByRealFlag = moveByRealFlag;
    }

    @Override
    public String toString() {
        return "PickUpDTO{" + "warehouseId=" + warehouseId + ", productSkuId=" + productSkuId + ", fromChannel="
            + fromChannel + ", toChannel=" + toChannel + ", fromSource=" + fromSource + ", toSource=" + toSource
            + ", count=" + count + ", fromLocationId=" + fromLocationId + ", locationId=" + locationId + '}';
    }

    /**
     * 数据拼接校验
     */
    public String toStringForCheck() {
        return "PickUpDTO{" + "warehouseId=" + warehouseId + ", productSkuId=" + productSkuId + ", fromChannel="
            + fromChannel + ", toChannel=" + toChannel + ", fromSource=" + fromSource + ", toSource=" + toSource
            + ", productionDateStr=" + getProductionDateStr() + ", batchTimeStr=" + getBatchTimeStr()
            + ", fromLocationId=" + fromLocationId + ", locationId=" + locationId + ", secOwnerId=" + secOwnerId
            + ", businessId=" + businessId + '}';
    }

    /**
     * 获取 订单号
     *
     * @return businessNo 订单号
     */
    public String getBusinessNo() {
        return this.businessNo;
    }

    /**
     * 设置 订单号
     *
     * @param businessNo 订单号
     */
    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    /**
     * 获取 产品信息规格id
     *
     * @return productSpecificationId 产品信息规格id
     */
    public Long getProductSpecificationId() {
        return this.productSpecificationId;
    }

    /**
     * 设置 产品信息规格id
     *
     * @param productSpecificationId 产品信息规格id
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }
}
