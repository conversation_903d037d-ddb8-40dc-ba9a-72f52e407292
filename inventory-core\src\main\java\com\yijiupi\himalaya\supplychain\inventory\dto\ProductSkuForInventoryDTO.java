package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * <AUTHOR> 2017/11/30
 */
public class ProductSkuForInventoryDTO implements Serializable {
    /**
     * 产品skuid
     */
    private Long productSkuId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 库存渠道,0:酒批，1:大宗产品.
     */
    private Integer channel;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 产品来源
     */
    private Integer source;

    /**
     * 获取 产品skuid
     *
     * @return productSkuId 产品skuid
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品skuid
     *
     * @param productSkuId 产品skuid
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 产品名称
     *
     * @return productName 产品名称
     */
    public String getProductName() {
        return this.productName;
    }

    /**
     * 设置 产品名称
     *
     * @param productName 产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 城市id
     *
     * @return cityId 城市id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市id
     *
     * @param cityId 城市id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 库存渠道0:酒批，1:大宗产品
     *
     * @return channel 库存渠道0:酒批，1:大宗产品
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 库存渠道0:酒批，1:大宗产品
     *
     * @param channel 库存渠道0:酒批，1:大宗产品
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 二级货主Id
     *
     * @return secOwnerId 二级货主Id
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主Id
     *
     * @param secOwnerId 二级货主Id
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取 产品来源
     *
     * @return source 产品来源
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源
     *
     * @param source 产品来源
     */
    public void setSource(Integer source) {
        this.source = source;
    }
}
