package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 批次库存转移dto
 *
 * <AUTHOR> 2018/3/31
 */
public class CheckBatchInventoryDTO implements Serializable {
    /**
     * 产品SkuId
     */
    private Long productSkuId;
    /**
     * 目标货位id
     */
    private Long locationId;
    /**
     * 目标货位名称
     */
    private String locationName;
    /**
     * 目标'货区或货位类型：0:货位，1:货区',
     */
    private Integer locationCategory;
    /**
     * 目标货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    private Integer subcategory;
    /**
     * 渠道
     */
    private Integer channel;
    /**
     * 批次时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;
    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 城市ID
     */
    private Integer cityId;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    /**
     * 获取 目标货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 目标货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 目标货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 目标货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 目标'货区或货位类型：0:货位，1:货区',
     */
    public Integer getLocationCategory() {
        return this.locationCategory;
    }

    /**
     * 设置 目标'货区或货位类型：0:货位，1:货区',
     */
    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    /**
     * 目标货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    public Integer getSubcategory() {
        return this.subcategory;
    }

    /**
     * 目标货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    public void setSubcategory(Integer subcategory) {
        this.subcategory = subcategory;
    }

    /**
     * 获取 来源渠道
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 来源渠道
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

}
