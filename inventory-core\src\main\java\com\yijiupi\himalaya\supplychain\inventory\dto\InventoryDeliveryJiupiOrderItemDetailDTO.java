/**
 * Copyright © 2020 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: yanpin
 * @date: 2020年9月8日 上午11:44:25
 */
public class InventoryDeliveryJiupiOrderItemDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long omsOrderId;
    private Long omsOrderItemId;
    private Long secProductOwnerId;
    private String erpSecProductOwnerId;
    /**
     * 规格id
     */
    private Long specId;
    /**
     * 一级货主id
     */
    private Long ownerId;
    /**
     * 数量
     */
    private BigDecimal count;
    private BigDecimal addCount;
    /**
     * skuId
     */
    private Long productSkuId;
    private BigDecimal saleSpecQuantity;
    /**
     * 二级货主id
     */
    private Long secOwnerId;
    /**
     * 配送数量
     */
    private BigDecimal dispatchCount;

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    @Override
    public String toString() {
        return "OrderItemProductOwnerDTO [id=" + id + ", omsOrderId=" + omsOrderId + ", omsOrderItemId="
            + omsOrderItemId + ", secProductOwnerId=" + secProductOwnerId + ", specId=" + specId + ", ownerId="
            + ownerId + ", count=" + count + "]";
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOmsOrderId() {
        return omsOrderId;
    }

    public void setOmsOrderId(Long omsOrderId) {
        this.omsOrderId = omsOrderId;
    }

    public Long getOmsOrderItemId() {
        return omsOrderItemId;
    }

    public void setOmsOrderItemId(Long omsOrderItemId) {
        this.omsOrderItemId = omsOrderItemId;
    }

    public Long getSecProductOwnerId() {
        return secProductOwnerId;
    }

    public void setSecProductOwnerId(Long secProductOwnerId) {
        this.secProductOwnerId = secProductOwnerId;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    /**
     * 获取erpSecProductOwnerId
     * 
     * @return erpSecProductOwnerId erpSecProductOwnerId
     */
    public String getErpSecProductOwnerId() {
        return erpSecProductOwnerId;
    }

    /**
     * 设置erpSecProductOwnerId
     * 
     * @param erpSecProductOwnerId erpSecProductOwnerId
     */
    public void setErpSecProductOwnerId(String erpSecProductOwnerId) {
        this.erpSecProductOwnerId = erpSecProductOwnerId;
    }

    /**
     * 获取addCount
     * 
     * @return addCount addCount
     */
    public BigDecimal getAddCount() {
        return addCount;
    }

    /**
     * 设置addCount
     * 
     * @param addCount addCount
     */
    public void setAddCount(BigDecimal addCount) {
        this.addCount = addCount;
    }

    public BigDecimal getDispatchCount() {
        return dispatchCount;
    }

    public void setDispatchCount(BigDecimal dispatchCount) {
        this.dispatchCount = dispatchCount;
    }
}
