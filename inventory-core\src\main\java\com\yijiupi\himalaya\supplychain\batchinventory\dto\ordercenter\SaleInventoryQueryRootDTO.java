package com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/23
 */
public class SaleInventoryQueryRootDTO implements Serializable {

    private List<OrderCenterSaleInventoryQueryDTO> RowKeyQueryDTO;

    /**
     * 获取
     *
     * @return RowKeyQueryDTO
     */
    public List<OrderCenterSaleInventoryQueryDTO> getRowKeyQueryDTO() {
        return this.RowKeyQueryDTO;
    }

    /**
     * 设置
     *
     * @param RowKeyQueryDTO
     */
    public void setRowKeyQueryDTO(List<OrderCenterSaleInventoryQueryDTO> RowKeyQueryDTO) {
        this.RowKeyQueryDTO = RowKeyQueryDTO;
    }
}
