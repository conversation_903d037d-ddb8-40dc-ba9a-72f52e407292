package com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved. </br>
 * 促销产品货位检查
 *
 * <AUTHOR>
 * @date 2025/5/12
 */
public class PromotionLocationCheckDTO implements Serializable {
    /**
     * 仓库
     */
    private Integer warehouseId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 产品名称
     */
    private Long productName;

    /**
     * 货位Id
     */
    private Long locationId;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getProductName() {
        return productName;
    }

    public void setProductName(Long productName) {
        this.productName = productName;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }
}
