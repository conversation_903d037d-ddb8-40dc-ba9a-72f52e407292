package com.yijiupi.himalaya.supplychain.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 校正库存是否差异 0 无差异 1 有差异
 *
 * <AUTHOR>
 * @date 2018/1/10 11:50
 */
public enum CheckDiffEnum {
    /**
     * 无差异
     */
    无差异((byte)0),
    /**
     * 有差异
     */
    有差异((byte)1);

    private byte type;

    CheckDiffEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    public void setType(byte type) {
        this.type = type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnmuName(Byte value) {
        String name = null;
        if (value != null) {
            name = cache.get(value);
        }
        return name;
    }

    private static Map<Byte, String> cache =
        EnumSet.allOf(CheckDiffEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p.name()));

}
