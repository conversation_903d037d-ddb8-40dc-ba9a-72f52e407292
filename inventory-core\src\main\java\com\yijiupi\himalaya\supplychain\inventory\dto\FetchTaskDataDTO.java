package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/6
 */
public class FetchTaskDataDTO implements Serializable {
    private static final long serialVersionUID = 3756336469960588304L;
    /**
     * 出库单号
     */
    private List<String> refOrderNoList;
    /**
     * 取货任务类型
     */
    private Byte fetchTaskType;
    /**
     * 取货任务ID
     */
    private Long fetchTaskId;

    public List<String> getRefOrderNoList() {
        return refOrderNoList;
    }

    public void setRefOrderNoList(List<String> refOrderNoList) {
        this.refOrderNoList = refOrderNoList;
    }

    public Byte getFetchTaskType() {
        return fetchTaskType;
    }

    public void setFetchTaskType(Byte fetchTaskType) {
        this.fetchTaskType = fetchTaskType;
    }

    public Long getFetchTaskId() {
        return fetchTaskId;
    }

    public void setFetchTaskId(Long fetchTaskId) {
        this.fetchTaskId = fetchTaskId;
    }
}
