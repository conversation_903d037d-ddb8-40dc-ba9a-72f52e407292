package com.yijiupi.himalaya.supplychain.batchinventory.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProductionDatePriceDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 成本价小单位名称
     */
    private String priceUnit;

    /**
     * 批次库存小数量
     */
    private BigDecimal batchStoreCount;

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public BigDecimal getBatchStoreCount() {
        return batchStoreCount;
    }

    public void setBatchStoreCount(BigDecimal batchStoreCount) {
        this.batchStoreCount = batchStoreCount;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public String getPriceUnit() {
        return priceUnit;
    }

    public void setPriceUnit(String priceUnit) {
        this.priceUnit = priceUnit;
    }
}
