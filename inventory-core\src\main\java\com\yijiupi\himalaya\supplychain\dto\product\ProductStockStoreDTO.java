package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: lidengfeng
 * @date 2018/12/7 13:42
 */
public class ProductStockStoreDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 库存数量
     */
    private BigDecimal totalCountMinUnit;
    /**
     * 规格id
     */
    private Long productSpecificationId;
    /**
     * 类型 0 酒批;1 合作商;2 入驻商
     */
    private Integer ownerType;
    /**
     * 所属人id
     */
    private Long ownerId;

    /**
     * 获取 城市id
     * 
     * @return
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市id
     * 
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 库存总数量
     * 
     * @return
     */
    public BigDecimal getTotalCountMinUnit() {
        return totalCountMinUnit;
    }

    /**
     * 设置 库存总数量
     * 
     * @param totalCountMinUnit
     */
    public void setTotalCountMinUnit(BigDecimal totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }

    /**
     * 获取 规格参数id
     * 
     * @return
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 规格参数id
     * 
     * @param productSpecificationId
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 所属人类型
     * 
     * @return
     */
    public Integer getOwnerType() {
        return ownerType;
    }

    /**
     * 设置 所属人类型
     * 
     * @param ownerType
     */
    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    /**
     * 获取 所属人
     * 
     * @return
     */
    public Long getOwnerId() {
        return ownerId;
    }

    /**
     * 设置 所属人
     * 
     * @param ownerId
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }
}
