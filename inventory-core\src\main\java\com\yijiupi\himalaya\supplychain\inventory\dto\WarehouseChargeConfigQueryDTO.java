package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * 仓库标准费率查询DTO
 * 
 * @author: lidengfeng
 * @date 2018/9/15 10:31
 */
public class WarehouseChargeConfigQueryDTO implements Serializable {

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页数
     */
    private Integer pageSize;

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
