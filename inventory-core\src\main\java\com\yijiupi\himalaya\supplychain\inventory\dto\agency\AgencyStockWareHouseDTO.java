package com.yijiupi.himalaya.supplychain.inventory.dto.agency;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 经销商出入库选择仓库
 * 
 * @author: lidengfeng
 * @date 2018/9/29 10:33
 */
public class AgencyStockWareHouseDTO implements Serializable {

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库地址
     */
    private String detailAddress;

    /**
     * 服务商id
     */
    private Integer shopId;

    /**
     * 托管费
     */
    private BigDecimal custodiancharge = BigDecimal.ZERO;

    /**
     * 配送费
     */
    private BigDecimal transportcharge = BigDecimal.ZERO;

    /**
     * 获取 城市id
     * 
     * @return
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市id
     * 
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 城市名
     * 
     * @return
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置 城市名
     * 
     * @param city
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 仓库名称
     * 
     * @return
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置 仓库名称
     * 
     * @param warehouseName
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    /**
     * 获取 详细地址
     * 
     * @return
     */
    public String getDetailAddress() {
        return detailAddress;
    }

    /**
     * 设置 详细地址
     * 
     * @param detailAddress
     */
    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public Integer getShopId() {
        return shopId;
    }

    /**
     * 设置 经销商id
     * 
     * @param shopId
     */
    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    /**
     * 获取 托管费
     * 
     * @return
     */
    public BigDecimal getCustodiancharge() {
        return custodiancharge;
    }

    /**
     * 设置 托管费
     * 
     * @param custodiancharge
     */
    public void setCustodiancharge(BigDecimal custodiancharge) {
        this.custodiancharge = custodiancharge;
    }

    /**
     * 获取 运输费
     * 
     * @return
     */
    public BigDecimal getTransportcharge() {
        return transportcharge;
    }

    /**
     * 设置 运输费
     * 
     * @param transportcharge
     */
    public void setTransportcharge(BigDecimal transportcharge) {
        this.transportcharge = transportcharge;
    }
}
