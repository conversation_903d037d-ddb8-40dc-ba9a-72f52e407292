package com.yijiupi.himalaya.supplychain.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 库存属性（0：默认，1：自动转入）
 *
 */
public enum ProductStoreBatchPropertyEnum {
    /**
     * 默认
     */
    默认((byte)0),
    /**
     * 自动转入
     */
    自动转入((byte)1);

    private byte type;

    ProductStoreBatchPropertyEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    public void setType(byte type) {
        this.type = type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnmuName(Byte value) {
        String name = null;
        if (value != null) {
            name = cache.get(value);
        }
        return name;
    }

    private static Map<Byte, String> cache =
        EnumSet.allOf(ProductStoreBatchPropertyEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p.name()));

}
