package com.yijiupi.himalaya.supplychain.batchinventory.constant;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 是/否条件状态
 */
public enum ConditionStateEnum {
    /**
     * 否
     */
    否((byte)0),
    /**
     * 是
     */
    是((byte)1);

    private Byte type;

    ConditionStateEnum(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     *
     * @param value
     * @return
     */
    public static String getEnmuName(Byte value) {
        String name = null;
        if (value != null) {
            name = cache.get(value);
        }
        return name;
    }

    private static Map<Byte, String> cache =
        EnumSet.allOf(ConditionStateEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p.name()));
}
