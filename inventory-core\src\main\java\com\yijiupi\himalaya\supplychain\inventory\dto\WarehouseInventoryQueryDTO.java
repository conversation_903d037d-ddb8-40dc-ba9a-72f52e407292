package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * 仓库库存查询DTO.
 *
 * <AUTHOR>
 */
public class WarehouseInventoryQueryDTO implements Serializable {
    /**
     * 产品名称.
     */
    private String productName;
    /**
     * 城市ID
     */
    private Integer cityId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 产品ID
     */
    private Long productSkuId;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 获取 产品名称.
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置 产品名称.
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 城市ID
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市ID
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库ID
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库ID
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品ID
     *
     * @return productSkuId 产品ID
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品ID
     *
     * @param productSkuId 产品ID
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 库存渠道0:酒批，1:大宗产品
     *
     * @return channel 库存渠道0:酒批，1:大宗产品
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 库存渠道0:酒批，1:大宗产品
     *
     * @param channel 库存渠道0:酒批，1:大宗产品
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @return source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @param source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }
}
