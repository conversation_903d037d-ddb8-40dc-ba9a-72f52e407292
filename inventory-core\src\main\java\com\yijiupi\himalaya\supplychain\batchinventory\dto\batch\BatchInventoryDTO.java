package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 批次库存dto
 *
 * <AUTHOR> 2018/3/28
 */
public class BatchInventoryDTO implements Serializable {
    /**
     * 批属性编号
     */
    private String batchAttributeInfoNo;
    /**
     * 批次库存表主键
     */
    private String storeBatchId;
    /**
     * 产品库存ID
     */
    private String productStoreId;
    /**
     * 商品sku
     */
    private Long productSkuId;
    /**
     * 商品名称
     */
    private String productSkuName;
    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 所属人ID
     */
    private Long ownerId;
    /**
     * 所属人类型
     */
    private Integer ownerType;
    /**
     * 所属人类型名称(客户端显示)
     */
    private String storeOwnerTypeName;
    /**
     * 所属人名称
     */
    private String ownerName;
    /**
     * 库存总数量
     */
    private BigDecimal storeTotalCount;
    /**
     * 库存大数量
     */
    private BigDecimal storeCountMax;
    /**
     * 库存小数量
     */
    private BigDecimal storeCountMin;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * 批次时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;
    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 货位/货区
     */
    private Integer locationCategory;

    /**
     * 货位类别
     */
    private Integer locationSubcategory;

    /**
     * 货位类别名称
     */
    private String locationSubcategoryName;

    /**
     * 货位顺序
     */
    private Integer locationSequence;

    /**
     * 货区名称
     */
    private String area;

    /**
     * 销售模式类型
     */
    private Byte saleModel;

    /**
     * 销售模式
     */
    private String saleModelName;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * 二级所属人名称
     */
    private String secOwnerName;

    /**
     * 商品特征:1:大件,2:小件
     */
    private Byte productFeature;

    /**
     * 补货上限
     */
    private BigDecimal maxReplenishment;

    /**
     * 补货下限
     */
    private BigDecimal minReplenishment;

    /**
     * 商品品牌
     */
    private String productBrand;

    /**
     * 仓库库存小单位总数量
     */
    private BigDecimal unitTotolCount;

    /**
     * 产品的保质期
     */
    private Integer monthOfShelfLife;

    /**
     * 保质期单位(1：年 2：月 3：日）
     */
    private Integer shelfLifeUnit;

    /**
     * 产品状态 下架(0), 作废(1), 上架(2)
     */
    private Integer productState;

    /**
     * 产品一级类目
     */
    private String firstCategoryName;

    /**
     * 产品二级类目
     */
    private String secondCategoryName;

    /**
     * 保存条件 0:常温 1:冷藏 2:冷冻
     */
    private Byte storageType;

    /**
     * 库龄天数
     */
    private Long stockAge;

    /**
     * 超期天数
     */
    private Long overdue;

    /**
     * 货区类型
     */
    private Byte areaSubcategory;

    /**
     * 是否展示库存为0的批次库存 true=展示，false=不展示
     */
    private Boolean isDisplayZero;

    /**
     * 效期名称
     */
    private String shelfLifeTypeName;

    /**
     * 入库单编号
     */
    private String inStockOrderNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 托盘规格系数
     */
    private BigDecimal palletQuantity;

    /**
     * 是否最近2个采购/调拨批次
     */
    private Boolean lastTwoBatch;

    /**
     * 保质期是否为长期
     */
    private Boolean shelfLifeLongTime;

    /**
     * 促销批次库存id
     */
    private Long promotionBatchId;

    /**
     * 货位业务类型，1：赠品 2:促销
     *
     */
    private Byte locationBusinessType;

    /**
     * 库存属性（0：默认，1：自动转入）
     */
    private Byte batchProperty;

    /**
     * 业务类型（0：默认，1：生产日期治理任务）
     */
    private Byte businessType;

    /**
     * 分仓属性
     */
    private Byte storageAttribute;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;

    /**
     * 分仓属性名称
     */
    private String warehouseAllocationTypeName;

    public Boolean getLastTwoBatch() {
        return lastTwoBatch;
    }

    public void setLastTwoBatch(Boolean lastTwoBatch) {
        this.lastTwoBatch = lastTwoBatch;
    }

    public Byte getAreaSubcategory() {
        return areaSubcategory;
    }

    public void setAreaSubcategory(Byte areaSubcategory) {
        this.areaSubcategory = areaSubcategory;
    }

    public String getLocationSubcategoryName() {
        return locationSubcategoryName;
    }

    public void setLocationSubcategoryName(String locationSubcategoryName) {
        this.locationSubcategoryName = locationSubcategoryName;
    }

    public Integer getProductState() {
        return productState;
    }

    public void setProductState(Integer productState) {
        this.productState = productState;
    }

    public BigDecimal getUnitTotolCount() {
        return unitTotolCount;
    }

    public void setUnitTotolCount(BigDecimal unitTotolCount) {
        this.unitTotolCount = unitTotolCount;
    }

    public Integer getMonthOfShelfLife() {
        return monthOfShelfLife;
    }

    public void setMonthOfShelfLife(Integer monthOfShelfLife) {
        this.monthOfShelfLife = monthOfShelfLife;
    }

    public Integer getShelfLifeUnit() {
        return shelfLifeUnit;
    }

    public void setShelfLifeUnit(Integer shelfLifeUnit) {
        this.shelfLifeUnit = shelfLifeUnit;
    }

    public String getSecOwnerName() {
        return secOwnerName;
    }

    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Byte getSaleModel() {
        return saleModel;
    }

    public void setSaleModel(Byte saleModel) {
        this.saleModel = saleModel;
    }

    public String getSaleModelName() {
        return saleModelName;
    }

    public void setSaleModelName(String saleModelName) {
        this.saleModelName = saleModelName;
    }

    public Integer getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    public Integer getLocationSubcategory() {
        return locationSubcategory;
    }

    public void setLocationSubcategory(Integer locationSubcategory) {
        this.locationSubcategory = locationSubcategory;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    /**
     * 获取 产品库存ID
     */
    public String getProductStoreId() {
        return this.productStoreId;
    }

    /**
     * 设置 产品库存ID
     */
    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    /**
     * 获取 商品sku
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 商品sku
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 商品名称
     */
    public String getProductSkuName() {
        return this.productSkuName;
    }

    /**
     * 设置 商品名称
     */
    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    /**
     * 获取 仓库Id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 所属人ID
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置 所属人ID
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 所属人类型
     */
    public Integer getOwnerType() {
        return this.ownerType;
    }

    /**
     * 设置 所属人类型
     */
    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    /**
     * 获取 所属人名称
     */
    public String getOwnerName() {
        return this.ownerName;
    }

    /**
     * 设置 所属人名称
     */
    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    /**
     * 获取 库存总数量
     */
    public BigDecimal getStoreTotalCount() {
        return this.storeTotalCount;
    }

    /**
     * 设置 库存总数量
     */
    public void setStoreTotalCount(BigDecimal storeTotalCount) {
        this.storeTotalCount = storeTotalCount;
    }

    /**
     * 获取 库存大数量
     */
    public BigDecimal getStoreCountMax() {
        return this.storeCountMax;
    }

    /**
     * 设置 库存大数量
     */
    public void setStoreCountMax(BigDecimal storeCountMax) {
        this.storeCountMax = storeCountMax;
    }

    /**
     * 获取 库存小数量
     */
    public BigDecimal getStoreCountMin() {
        return this.storeCountMin;
    }

    /**
     * 设置 库存小数量
     */
    public void setStoreCountMin(BigDecimal storeCountMin) {
        this.storeCountMin = storeCountMin;
    }

    /**
     * 获取 库存渠道,0:酒批，1:大宗产品
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 库存渠道,0:酒批，1:大宗产品
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 包装规格名称
     */
    public String getSpecificationName() {
        return this.specificationName;
    }

    /**
     * 设置 包装规格名称
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    /**
     * 获取 包装规格大单位
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 包装规格大单位
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 包装规格小单位
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 包装规格小单位
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 包装规格大小单位转换系数
     */
    public BigDecimal getPackageQuantity() {
        return this.packageQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     */
    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 二级货主Id
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主Id
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 批次时间
     */
    public Date getBatchTime() {
        return this.batchTime;
    }

    /**
     * 设置 批次时间
     */
    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    /**
     * 获取 过期时间
     */
    public Date getExpireTime() {
        return this.expireTime;
    }

    /**
     * 设置 过期时间
     */
    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    /**
     * 获取 批次库存表主键
     */
    public String getStoreBatchId() {
        return this.storeBatchId;
    }

    /**
     * 设置 批次库存表主键
     */
    public void setStoreBatchId(String storeBatchId) {
        this.storeBatchId = storeBatchId;
    }

    /**
     * 获取 批属性编号
     */
    public String getBatchAttributeInfoNo() {
        return this.batchAttributeInfoNo;
    }

    /**
     * 设置 批属性编号
     */
    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    /**
     * 获取 仓库名称
     */
    public String getWarehouseName() {
        return this.warehouseName;
    }

    /**
     * 设置 仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    /**
     * 获取 所属人类型名称(客户端显示)
     */
    public String getStoreOwnerTypeName() {
        return this.storeOwnerTypeName;
    }

    /**
     * 设置 所属人类型名称(客户端显示)
     */
    public void setStoreOwnerTypeName(String storeOwnerTypeName) {
        this.storeOwnerTypeName = storeOwnerTypeName;
    }

    /**
     * 获取 生产日期
     */
    public Date getProductionDate() {
        return this.productionDate;
    }

    /**
     * 设置 生产日期
     */
    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Integer getLocationSequence() {
        return locationSequence;
    }

    public void setLocationSequence(Integer locationSequence) {
        this.locationSequence = locationSequence;
    }

    public Byte getProductFeature() {
        return productFeature;
    }

    public void setProductFeature(Byte productFeature) {
        this.productFeature = productFeature;
    }

    public BigDecimal getMaxReplenishment() {
        return maxReplenishment;
    }

    public void setMaxReplenishment(BigDecimal maxReplenishment) {
        this.maxReplenishment = maxReplenishment;
    }

    public BigDecimal getMinReplenishment() {
        return minReplenishment;
    }

    public void setMinReplenishment(BigDecimal minReplenishment) {
        this.minReplenishment = minReplenishment;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public void setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
    }

    public Byte getStorageType() {
        return storageType;
    }

    public void setStorageType(Byte storageType) {
        this.storageType = storageType;
    }

    public Long getStockAge() {
        return stockAge;
    }

    public void setStockAge(Long stockAge) {
        this.stockAge = stockAge;
    }

    public Long getOverdue() {
        return overdue;
    }

    public void setOverdue(Long overdue) {
        this.overdue = overdue;
    }

    public Boolean getIsDisplayZero() {
        return isDisplayZero;
    }

    public void setIsDisplayZero(Boolean displayZero) {
        isDisplayZero = displayZero;
    }

    public String getShelfLifeTypeName() {
        return shelfLifeTypeName;
    }

    public void setShelfLifeTypeName(String shelfLifeTypeName) {
        this.shelfLifeTypeName = shelfLifeTypeName;
    }

    public String getInStockOrderNo() {
        return inStockOrderNo;
    }

    public void setInStockOrderNo(String inStockOrderNo) {
        this.inStockOrderNo = inStockOrderNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getPalletQuantity() {
        return palletQuantity;
    }

    public void setPalletQuantity(BigDecimal palletQuantity) {
        this.palletQuantity = palletQuantity;
    }

    public Boolean getShelfLifeLongTime() {
        return shelfLifeLongTime;
    }

    public void setShelfLifeLongTime(Boolean shelfLifeLongTime) {
        this.shelfLifeLongTime = shelfLifeLongTime;
    }

    public Long getPromotionBatchId() {
        return promotionBatchId;
    }

    public void setPromotionBatchId(Long promotionBatchId) {
        this.promotionBatchId = promotionBatchId;
    }

    public Byte getLocationBusinessType() {
        return locationBusinessType;
    }

    public void setLocationBusinessType(Byte locationBusinessType) {
        this.locationBusinessType = locationBusinessType;
    }

    public Byte getBatchProperty() {
        return batchProperty;
    }

    public void setBatchProperty(Byte batchProperty) {
        this.batchProperty = batchProperty;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public Byte getStorageAttribute() {
        return storageAttribute;
    }

    public void setStorageAttribute(Byte storageAttribute) {
        this.storageAttribute = storageAttribute;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }

    public String getWarehouseAllocationTypeName() {
        return warehouseAllocationTypeName;
    }

    public void setWarehouseAllocationTypeName(String warehouseAllocationTypeName) {
        this.warehouseAllocationTypeName = warehouseAllocationTypeName;
    }
}
