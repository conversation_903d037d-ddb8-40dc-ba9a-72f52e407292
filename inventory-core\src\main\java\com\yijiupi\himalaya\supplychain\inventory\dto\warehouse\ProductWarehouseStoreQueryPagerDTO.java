package com.yijiupi.himalaya.supplychain.inventory.dto.warehouse;

import com.yijiupi.himalaya.base.search.PagerCondition;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/4/18
 */
public class ProductWarehouseStoreQueryPagerDTO extends PagerCondition {
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 组织机构id
     */
    private Integer cityId;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 组织机构id
     *
     * @return cityId 组织机构id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 组织机构id
     *
     * @param cityId 组织机构id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
}
