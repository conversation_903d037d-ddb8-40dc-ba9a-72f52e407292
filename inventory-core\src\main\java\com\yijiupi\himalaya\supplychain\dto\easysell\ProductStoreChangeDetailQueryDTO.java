package com.yijiupi.himalaya.supplychain.dto.easysell;

import java.io.Serializable;

/**
 * 商品出入库历史记录明细查询参数DTO
 * 
 * @author: lidengfeng
 * @date: 2018/8/14 11:22
 */
public class ProductStoreChangeDetailQueryDTO implements Serializable {
    /**
     * 规格id
     */
    private Long productSpecificationId;

    /**
     * 产品skuId
     */
    private Long productSkuId;
    /**
     * 经销商id
     */
    private Long shopId;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页数
     */
    private Integer pageSize;

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 获取 产品skuid
     * 
     * @return
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    /**
     * 设置 产品skuId
     * 
     * @param productSkuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }
}
