package com.yijiupi.himalaya.supplychain.inventory.dto.check;

import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/7/10
 */
public class CheckStoreInventoryByCityInfoDTO implements Serializable {
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 操作人
     */
    private Integer opUserId;
    /**
     * 是否是快照
     */
    private boolean isSnap;
    /**
     * 1 是老版本；2 是新版本
     */
    private Integer version = OLD_VERSION;

    public static Integer OLD_VERSION = 1;

    public static Integer NEW_VERSION = 2;

    public CheckStoreInventoryByCityInfoDTO(Integer cityId, Integer opUserId, boolean isSnap, Integer version) {
        this.cityId = cityId;
        this.opUserId = opUserId;
        this.isSnap = isSnap;
        this.version = version;
    }

    public CheckStoreInventoryByCityInfoDTO() {
    }

    /**
     * 获取 城市id
     *
     * @return cityId 城市id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市id
     *
     * @param cityId 城市id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 操作人
     *
     * @return opUserId 操作人
     */
    public Integer getOpUserId() {
        return this.opUserId;
    }

    /**
     * 设置 操作人
     *
     * @param opUserId 操作人
     */
    public void setOpUserId(Integer opUserId) {
        this.opUserId = opUserId;
    }

    /**
     * 获取 是否是快照
     *
     * @return isSnap 是否是快照
     */
    public boolean getIsIsSnap() {
        return this.isSnap;
    }

    /**
     * 设置 是否是快照
     *
     * @param isSnap 是否是快照
     */
    public void setIsSnap(boolean isSnap) {
        this.isSnap = isSnap;
    }

    /**
     * 获取 1 是老版本；2 是新版本
     *
     * @return version 1 是老版本；2 是新版本
     */
    public Integer getVersion() {
        return this.version;
    }

    /**
     * 设置 1 是老版本；2 是新版本
     *
     * @param version 1 是老版本；2 是新版本
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    public static boolean isNewVersion(Integer version, boolean isOpenCenter) {
        if (Objects.isNull(version)) {
            return Boolean.FALSE;
        }
        if (BooleanUtils.isFalse(isOpenCenter)) {
            return Boolean.FALSE;
        }
        if (NEW_VERSION.equals(version)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }
}
