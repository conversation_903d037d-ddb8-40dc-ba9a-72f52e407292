package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

public class BatchInventoryInfoUpdateDTO implements Serializable {

    /**
     * 批次库存编号
     */
    private String batchAttributeInfoNo;

    /**
     * 批次库存表主键
     */
    private String storeBatchId;

    /**
     * 批次库存id
     */
    private String productStoreId;

    /**
     * 批次入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 过期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;

    /**
     * 所属人类型
     */
    private Integer ownerType;

    /**
     * 修改人id
     */
    private Integer lastUpdateUserId;

    private boolean updateBatchNo;

    /**
     * 生产日期变更
     */
    private Boolean productionDateChangeNotify;

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public boolean isUpdateBatchNo() {
        return updateBatchNo;
    }

    public void setUpdateBatchNo(boolean updateBatchNo) {
        this.updateBatchNo = updateBatchNo;
    }

    public String getBatchAttributeInfoNo() {
        return batchAttributeInfoNo;
    }

    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public String getStoreBatchId() {
        return storeBatchId;
    }

    public void setStoreBatchId(String storeBatchId) {
        this.storeBatchId = storeBatchId;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    public Boolean getProductionDateChangeNotify() {
        return productionDateChangeNotify;
    }

    public void setProductionDateChangeNotify(Boolean productionDateChangeNotify) {
        this.productionDateChangeNotify = productionDateChangeNotify;
    }
}
