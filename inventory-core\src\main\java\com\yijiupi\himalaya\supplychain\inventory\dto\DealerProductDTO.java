package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 经销商托管产品返回DTO
 * 
 * @author: lidengfeng
 * @date 2018/9/28 16:55
 */
public class DealerProductDTO implements Serializable {

    /**
     * 产品skuid
     */
    private Long productSpecificationId;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 包装规格大单位
     */
    private String packageName;

    /**
     * 包装规格小单位
     */
    private String unitName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 库存小单位总数量
     */
    private BigDecimal totalCount;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 下车费
     */
    private BigDecimal unloadingCharge = BigDecimal.ZERO;

    /**
     * 分拣费
     */
    private BigDecimal sortingCharge = BigDecimal.ZERO;

    /**
     * 托管费
     */
    private BigDecimal custodianCharge = BigDecimal.ZERO;

    /**
     * 装车费
     */
    private BigDecimal loadingCharge = BigDecimal.ZERO;

    /**
     * 运输费
     */
    private BigDecimal transportCharge = BigDecimal.ZERO;

    /**
     * 卸货费
     */
    private BigDecimal landingCharge = BigDecimal.ZERO;

    /**
     * 首次入库时间
     */
    private String firstInStockTime;

    /**
     * 经销商id
     */
    private String dealerId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 获取 规格参数id
     * 
     * @return
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 规格参数id
     * 
     * @param productSpecificationId
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 产品名称
     * 
     * @return
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置 产品名称
     * 
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 规格名称
     * 
     * @return
     */
    public String getSpecificationName() {
        return specificationName;
    }

    /**
     * 设置 规格名称
     * 
     * @param specificationName
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    /**
     * 获取 大单位名称
     * 
     * @return
     */
    public String getPackageName() {
        return packageName;
    }

    /**
     * 设置 大单位名称
     * 
     * @param packageName
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 小单位名称
     * 
     * @return
     */
    public String getUnitName() {
        return unitName;
    }

    /**
     * 设置 小单位名称
     * 
     * @param unitName
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 单位转换系数
     * 
     * @return
     */
    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    /**
     * 设置 单位转换系数
     * 
     * @param packageQuantity
     */
    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 库存小单位总量
     * 
     * @return
     */
    public BigDecimal getTotalCount() {
        return totalCount;
    }

    /**
     * 设置 库存小单位总量
     * 
     * @param totalCount
     */
    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取 下车费
     * 
     * @return
     */
    public BigDecimal getUnloadingCharge() {
        return unloadingCharge;
    }

    /**
     * 设置 下车费
     * 
     * @param unloadingCharge
     */
    public void setUnloadingCharge(BigDecimal unloadingCharge) {
        this.unloadingCharge = unloadingCharge;
    }

    /**
     * 获取 分拣费
     * 
     * @return
     */
    public BigDecimal getSortingCharge() {
        return sortingCharge;
    }

    /**
     * 设置 分拣费
     * 
     * @param sortingCharge
     */
    public void setSortingCharge(BigDecimal sortingCharge) {
        this.sortingCharge = sortingCharge;
    }

    /**
     * 获取 托管费
     * 
     * @return
     */
    public BigDecimal getCustodianCharge() {
        return custodianCharge;
    }

    /**
     * 设置 托管费
     * 
     * @param custodianCharge
     */
    public void setCustodianCharge(BigDecimal custodianCharge) {
        this.custodianCharge = custodianCharge;
    }

    /**
     * 获取 装车费
     * 
     * @return
     */
    public BigDecimal getLoadingCharge() {
        return loadingCharge;
    }

    /**
     * 设置 装车费
     * 
     * @param loadingCharge
     */
    public void setLoadingCharge(BigDecimal loadingCharge) {
        this.loadingCharge = loadingCharge;
    }

    /**
     * 获取 运输费
     * 
     * @return
     */
    public BigDecimal getTransportCharge() {
        return transportCharge;
    }

    /**
     * 设置 运输费
     * 
     * @param transportCharge
     */
    public void setTransportCharge(BigDecimal transportCharge) {
        this.transportCharge = transportCharge;
    }

    /**
     * 获取 卸货费
     * 
     * @return
     */
    public BigDecimal getLandingCharge() {
        return landingCharge;
    }

    /**
     * 设置 卸货费
     * 
     * @param landingCharge
     */
    public void setLandingCharge(BigDecimal landingCharge) {
        this.landingCharge = landingCharge;
    }

    /**
     * 获取 首次入库时间
     * 
     * @return
     */
    public String getFirstInStockTime() {
        return firstInStockTime;
    }

    /**
     * 设置 首次入库时间
     * 
     * @param firstInStockTime
     */
    public void setFirstInStockTime(String firstInStockTime) {
        this.firstInStockTime = firstInStockTime;
    }

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public String getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(String dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }
}
