package com.yijiupi.himalaya.supplychain.inventory.service;

import com.yijiupi.himalaya.supplychain.inventory.dto.ConfirmOutStockDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.DeliveryProductInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InternalDistributionOrderDeliveryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.InStockCompleteDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.InventoryInStockOrderBatchDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.RecallDeliverDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.BatchOutStockConfirmDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.ZiTiConfirmDeliverDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreDTO;

import java.util.List;

/**
 * 库存: 订单业务服务.
 *
 * <AUTHOR>
 */
public interface IInventoryOrderBizService {

    /**
     * 订单缺货标记.（返销售库存）
     */
    void outOfStockRemark(List<InventoryDeliveryJiupiOrder> deliveryOrders);

    /**
     * 扣交易系统销售库存
     */
    void returnSaleInventoryByOrder(List<InventoryDeliveryJiupiOrder> deliveryOrders);

    /**
     * 批次发货.
     *
     * @return
     */
    List<InventoryDeliveryJiupiOrder> batchDeliver(List<InventoryDeliveryJiupiOrder> deliveryOrders);

    /**
     * 批次发货.
     */
    List<InventoryDeliveryJiupiOrder> batchDeliverNoValidateStore(List<InventoryDeliveryJiupiOrder> deliveryOrders);

    /**
     * 内配单发货.
     *
     * @return
     */
    List<InventoryDeliveryJiupiOrder>
    internalDistributionOrderDelivery(InternalDistributionOrderDeliveryDTO deliveryOrders);

    /**
     * 自提出库
     */
    @Deprecated
    void ziTiDeliver(List<Long> businessIdList, List<Long> productSourceCodeIdList, Integer userId);

    /**
     * 召回->发货批次
     */
    void recallDeliver(RecallDeliverDTO recallDeliverDTO);

    /**
     * 入库加库存(财务确认收款)
     */
    void inStockComplete(InStockCompleteDTO completeDTO);

    /**
     * 订单确认入库操作并提交财务
     */
    void affirmInStock(InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO);

    /**
     * 出入库客户端直接加库存
     */
    void processInventoryByErp(List<StockOrderStoreDTO> stockOrderStoreDTOS);

    /**
     * 出入库客户端直接加库存（异常重试）
     */
    void processInventoryByErpJson(String json);

    /**
     * ERP同步消息监听（异常重试）
     */
    void processErpMsgJson(String json);

    /**
     * 处理盘点单总库存无差异数据
     *
     * @param orderId
     */
    void handleStockCheckNoDiff(String orderId);

    /**
     * 内配单入库
     */
    void internalDeliveryInStock(InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO);

    /**
     * 内配单入库(整车转运)
     */
    void vehicleTransshipmentInStock(InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO);

    /**
     * 恢复出库单
     */
    void resetOrder(List<InventoryDeliveryJiupiOrder> deliveryOrders);

    /**
     * 按单号生成入库单
     */
    void createInStockOrderByOrderNo(InternalDistributionOrderDeliveryDTO deliveryOrders);

    /**
     * 根据类型校验发货库存数量
     */
    @Deprecated
    List<DeliveryProductInventoryDTO>
    validateOrderDeliveryProductInventory(List<InventoryDeliveryJiupiOrder> deliveryOrders);

    /**
     * 校验发货库存数量
     */
    List<DeliveryProductInventoryDTO> validateOrderDeliveryProductInventory(
            List<InventoryDeliveryJiupiOrder> deliveryOrders, String orderDeliveryOpType);

    /**
     * 内配单-发货入库信息异常重试信息
     */
    void retriesBatchAddInStockTransferOrder(String message);

    /**
     * 订单发货
     */
    void orderDelivery(List<InventoryDeliveryJiupiOrder> deliveryOrders);

    /**
     * SCM2-15145 虚仓实配确认出库 （处理库存及单据状态）
     */
    void confirmOutStockByOrder(ConfirmOutStockDTO confirmOutStockDTO);

    /**
     * 直接出库、直接入库
     */
    void directProcessingOrderSync(List<OrderDTO> orders);

    /**
     * 财务确认收款 直接加库存信息
     *
     * @param completeDTO
     */
    void financeCashInventoryChange(InStockCompleteDTO completeDTO);

    /**
     * 批量虚仓实配确认出库 （处理库存及单据状态）
     */
    void batchConfirmOutStockByOrder(BatchOutStockConfirmDTO confirmOutStockDTO);

    /**
     * 快递直发批次发货.
     *
     * @return
     */
    List<InventoryDeliveryJiupiOrder> batchDeliverByExpressOrder(List<InventoryDeliveryJiupiOrder> deliveryOrders);

    /**
     * 经销商订单出入库直接处理库存
     */
    void directCompleteInStockOrderByDealer(List<OrderDTO> orders);

    /**
     * 自提出库
     */
    void ziTiConfirmDeliver(ZiTiConfirmDeliverDTO dto);
}
