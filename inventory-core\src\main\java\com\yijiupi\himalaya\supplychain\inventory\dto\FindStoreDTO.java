package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * 库存报表查询
 * 
 * @author: cheng<PERSON>
 * @date: 2022年9月6日
 */
public class FindStoreDTO extends PageCondition implements Serializable {
    private static final long serialVersionUID = -1062911611613878263L;

    private Integer userId;
    /**
     * 商品名称
     */
    private String productSkuName;
    /**
     * 仓库
     */
    private List<Integer> warehouseIds;
    /**
     * 货位
     */
    private Integer goodsPositionId;
    /**
     * 供应商
     */
    private Long supplierId;
    /**
     * 库存分类
     */
    private Integer storeOwnerType;
    private Integer warehouseId;
    private Integer cityId;

    /**
     * 产品skuid集合
     */
    private List<Long> productSkuIds;

    /**
     * ProductTypeEnums
     * 产品类型：1.成品;2.半成品;3.包装材料
     */
    private List<Byte> productTypeList;

    private Boolean printLog = false;

    public Boolean getPrintLog() {
        return printLog;
    }

    public void setPrintLog(Boolean printLog) {
        this.printLog = printLog;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getProductSkuName() {
        return productSkuName;
    }

    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    public List<Integer> getWarehouseIds() {
        return warehouseIds;
    }

    public void setWarehouseIds(List<Integer> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    public Integer getGoodsPositionId() {
        return goodsPositionId;
    }

    public void setGoodsPositionId(Integer goodsPositionId) {
        this.goodsPositionId = goodsPositionId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getStoreOwnerType() {
        return storeOwnerType;
    }

    public void setStoreOwnerType(Integer storeOwnerType) {
        this.storeOwnerType = storeOwnerType;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public List<Long> getProductSkuIds() {
        return productSkuIds;
    }

    public void setProductSkuIds(List<Long> productSkuIds) {
        this.productSkuIds = productSkuIds;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }

    /**
     * 获取 ProductTypeEnums      产品类型：1.成品;2.半成品;3.包装材料
     *
     * @return productTypeList ProductTypeEnums      产品类型：1.成品;2.半成品;3.包装材料
     */
    public List<Byte> getProductTypeList() {
        return this.productTypeList;
    }

    /**
     * 设置 ProductTypeEnums      产品类型：1.成品;2.半成品;3.包装材料
     *
     * @param productTypeList ProductTypeEnums      产品类型：1.成品;2.半成品;3.包装材料
     */
    public void setProductTypeList(List<Byte> productTypeList) {
        this.productTypeList = productTypeList;
    }
}
