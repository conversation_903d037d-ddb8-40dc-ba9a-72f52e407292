package com.yijiupi.himalaya.supplychain.batchinventory.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.DefectiveInventoryPriceDTO;

/**
 * 批次库存(货区货位)操作
 *
 * <AUTHOR> 2018/3/29
 */
public interface IBatchInventoryManageService {

    // /**
    // * 单个/批量移库(有批次库存id)
    // *
    // * @param batchInventoryTransferDTOList
    // */
    // void batchInventoryTransfer(List<BatchInventoryTransferDTO> batchInventoryTransferDTOList);

    /**
     * 拣货移库校验-新
     * 
     * @param pickUpDTOList
     * @return
     */
    List<PickUpDTO> checkInventoryTransferForPickUp(List<PickUpDTO> pickUpDTOList);

    /**
     * 拣货校验移库
     */
    List<PickUpDTO> checkInventoryTransferByPickUp(List<PickUpDTO> pickUpDTOList);

    /**
     * 下架移库(拣货完成)
     */
    List<PickUpDTO> pickupCompleteBySku(List<PickUpDTO> pickUpDTOList, PickUpChangeRecordDTO pickUpChangeRecordDTO);

    /**
     * 完成拣货任务明细
     * 
     * @param pickUpDTOList
     * @param pickUpChangeRecordDTO
     * @return
     */
    List<PickUpDTO> batchTaskItemComplete(List<PickUpDTO> pickUpDTOList, PickUpChangeRecordDTO pickUpChangeRecordDTO);

    /**
     * 上架移库
     */
    void batchInventoryTransferBySku(List<PickUpDTO> pickUpDTOList, PickUpChangeRecordDTO pickUpChangeRecordDTO);

    /**
     * 上架批量【多个上架任务】移库
     */
    void putAwayTaskBatchPickUp(List<PickUpRecordDTO> pickUpChangeRecordList, Integer warehouseId);

    /**
     * 修改出库位
     */
    void updateLocationByCk(List<PickUpDTO> pickUpDTOList, PickUpChangeRecordDTO pickUpChangeRecordDTO);

    /**
     * 修改货位库存（只修改数量）
     */
    void updateProductStoreBatch(List<ProductStoreBatchDTO> productStoreBatchDTOS, String operaterUser);

    /**
     * 批次库存异常重试
     *
     * @param json
     */
    void processBatchInventoryByJson(String json);

    /**
     * 移库
     */
    List<PickUpDTO> batchInventoryTransfer(List<PickUpDTO> pickUpDTOList, PickUpChangeRecordDTO pickUpChangeRecordDTO,
        BatchInventoryTransferCheckDTO batchInventoryTransferCheckDTO);

    /**
     * 修改批次库存信息
     */
    List<BatchInventoryInfoUpdateDTO>
        updateBatchInventoryInfo(List<BatchInventoryInfoUpdateDTO> batchInventoryInfoUpdateDTOS);

    /**
     * 按货位处理库存
     */
    void processLocationInventory(ProductInventoryChangeDTO productInventoryChangeDTO);

    /**
     * 未开启货位库存变更产品批次属性
     */
    void nonOpenStockChangeBatchInventoryByStoreCheck(StoreCheckUpdateBatchInventoryDTO changeDTO);

    /**
     * 货位库存，置空小于2013年的生产日期，更新货位库存
     *
     * @param warehouseIdList 仓库ID列表
     * @param endProductionDate 截止生产日期（yyyy-MM-dd HH:mm:ss）
     */
    void clearBeforeProductionDate(List<Integer> warehouseIdList, Date endProductionDate);

    /**
     * 根据skuCount，计算破损区的残次品相关额度（只计算，不会存入redis）
     */
    DefectiveInventoryPriceDTO calCcpPriceBySkuCount(Integer orgId, Integer warehouseId,
        Map<Long, BigDecimal> skuCount);

    /**
     * 根据批次库存，计算破损区的残次品相关额度，并存入redis，并返回
     */
    DefectiveInventoryPriceDTO saveCcpPriceByInventory(Integer orgId, Integer warehouseId);

    /**
     * 根据skuCount，计算破损区的残次品相关额度，并存入redis，并返回
     */
    DefectiveInventoryPriceDTO saveCcpPriceBySkuCount(Integer orgId, Integer warehouseId,
        Map<Long, BigDecimal> skuCount);

    /**
     * 获取破损区的残次品相关额度
     */
    DefectiveInventoryPriceDTO getCcpPrice(Integer orgId, Integer warehouseId);

    /**
     * 根据产品规格、仓库id、货主修改批次库存生产日期
     */
    void updateProductionDateBySpec(BatchInventoryDTO updateDTO);

    /**
     * 货位库存，置空小于2013年的生产日期，更新货位库存
     *
     */
    void clearBeforeProductionDateUtil(BatchInventoryDTO updateDTO);
}
