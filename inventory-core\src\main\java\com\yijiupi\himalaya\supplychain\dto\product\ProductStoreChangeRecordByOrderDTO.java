package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单库存变更
 *
 * <AUTHOR>
 * @date 11/27/20 8:59 PM
 */
public class ProductStoreChangeRecordByOrderDTO implements Serializable {
    private static final long serialVersionUID = 370355894309959123L;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 产品SkuID
     */
    private Long productSkuId;

    /**
     * 产品名称.
     */
    private String productName;

    /**
     * 产品规格
     */
    private String specName;

    /**
     * 包装单位转换系数
     */
    private BigDecimal specQuantity;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    /**
     * 二级货主名称
     */
    private String secOwnerName;

    /**
     * 产品规格Id
     */
    private Long specId;

    /**
     * 库存id
     */
    private String storeId;

    /**
     * 变更记录id
     */
    private String storeRecordId;

    /**
     * 酒批事件类型
     */
    private Integer jiupiEventType;

    /**
     * 酒批事件类型名称
     */
    private String jiupiEventTypeName;

    /**
     * erp事件类型
     */
    private Integer erpEventType;

    /**
     * 单据类型
     */
    private Integer orderType;

    /**
     * 单据类型名称
     */
    private String orderTypeName;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 库存变更数量(大单位,设置为0)
     */
    private BigDecimal countMaxUnit;

    /**
     * 库存变更数量(小单位)
     */
    private BigDecimal countMinUnit;

    /**
     * 库存变更数量总计
     */
    private BigDecimal totalCount;

    /**
     * 变更前原库存
     */
    private BigDecimal sourceTotalCount;

    /**
     * 变更数量
     */
    private ProductStoreCountDTO addStoreCountDTO;

    /**
     * 原数量
     */
    private ProductStoreCountDTO sourceStoreCountDTO;

    /**
     * 新数量
     */
    private ProductStoreCountDTO newStoreCountDTO;

    /**
     * 描述
     */
    private String des;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 货主类型
     */
    private Integer ownerType;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public String getSecOwnerName() {
        return secOwnerName;
    }

    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getStoreRecordId() {
        return storeRecordId;
    }

    public void setStoreRecordId(String storeRecordId) {
        this.storeRecordId = storeRecordId;
    }

    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    public Integer getErpEventType() {
        return erpEventType;
    }

    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getCountMaxUnit() {
        return countMaxUnit;
    }

    public void setCountMaxUnit(BigDecimal countMaxUnit) {
        this.countMaxUnit = countMaxUnit;
    }

    public BigDecimal getCountMinUnit() {
        return countMinUnit;
    }

    public void setCountMinUnit(BigDecimal countMinUnit) {
        this.countMinUnit = countMinUnit;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getSourceTotalCount() {
        return sourceTotalCount;
    }

    public void setSourceTotalCount(BigDecimal sourceTotalCount) {
        this.sourceTotalCount = sourceTotalCount;
    }

    public ProductStoreCountDTO getAddStoreCountDTO() {
        return addStoreCountDTO;
    }

    public void setAddStoreCountDTO(ProductStoreCountDTO addStoreCountDTO) {
        this.addStoreCountDTO = addStoreCountDTO;
    }

    public ProductStoreCountDTO getSourceStoreCountDTO() {
        return sourceStoreCountDTO;
    }

    public void setSourceStoreCountDTO(ProductStoreCountDTO sourceStoreCountDTO) {
        this.sourceStoreCountDTO = sourceStoreCountDTO;
    }

    public ProductStoreCountDTO getNewStoreCountDTO() {
        return newStoreCountDTO;
    }

    public void setNewStoreCountDTO(ProductStoreCountDTO newStoreCountDTO) {
        this.newStoreCountDTO = newStoreCountDTO;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getJiupiEventTypeName() {
        return jiupiEventTypeName;
    }

    public void setJiupiEventTypeName(String jiupiEventTypeName) {
        this.jiupiEventTypeName = jiupiEventTypeName;
    }

    public String getOrderTypeName() {
        return orderTypeName;
    }

    public void setOrderTypeName(String orderTypeName) {
        this.orderTypeName = orderTypeName;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }
}
