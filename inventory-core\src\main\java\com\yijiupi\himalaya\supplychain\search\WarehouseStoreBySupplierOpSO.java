/*
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */

package com.yijiupi.himalaya.supplychain.search;

import java.io.Serializable;
import java.util.List;

/**
 * 供应商op库存搜索对象 Created by yangkang on 2016/12/1.
 */
public class WarehouseStoreBySupplierOpSO implements Serializable {

    /**
     * 产品信息规格Id
     */
    private Long productInfoSpecId;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 查询销售模式
     */
    private List<Integer> saleModes;

    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 渠道
     */
    private Integer channel;

    public List<Integer> getSaleModes() {
        return saleModes;
    }

    public void setSaleModes(List<Integer> saleModes) {
        this.saleModes = saleModes;
    }

    public Long getProductInfoSpecId() {
        return productInfoSpecId;
    }

    public void setProductInfoSpecId(Long productInfoSpecId) {
        this.productInfoSpecId = productInfoSpecId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }
}
