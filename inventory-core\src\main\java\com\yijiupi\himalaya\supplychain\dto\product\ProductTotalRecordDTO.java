package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreChangeDetailDTO;

/**
 * 库存明细页面带库存
 * 
 * @author: lideng<PERSON>
 * @date 2019/2/13 13:45
 */
public class ProductTotalRecordDTO implements Serializable {

    /**
     * 库存变更记录
     */
    private PageList<ProductStoreChangeDetailDTO> list;

    /**
     * 库存商品及库存
     */
    private ProductDetailsDTO productDetail;

    /**
     * 获取 库存变更记录
     * 
     * @return
     */
    public PageList<ProductStoreChangeDetailDTO> getList() {
        return list;
    }

    /**
     * 设置 库存变更记录
     * 
     * @param list
     */
    public void setList(PageList<ProductStoreChangeDetailDTO> list) {
        this.list = list;
    }

    /**
     * 获取 产品库存
     * 
     * @return
     */
    public ProductDetailsDTO getProductDetail() {
        return productDetail;
    }

    /**
     * 设置 产品库存
     * 
     * @param productDetail
     */
    public void setProductDetail(ProductDetailsDTO productDetail) {
        this.productDetail = productDetail;
    }
}
