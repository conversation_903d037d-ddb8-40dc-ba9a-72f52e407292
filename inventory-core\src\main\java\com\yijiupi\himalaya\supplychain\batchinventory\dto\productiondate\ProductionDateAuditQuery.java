package com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate;

import com.yijiupi.himalaya.base.search.PagerCondition;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-14 11:50
 **/
public class ProductionDateAuditQuery extends PagerCondition {

    /**
     * 仓库 id
     */
    private Integer warehouseId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品 skuId
     */
    private Long skuId;

    /**
     * 生产日期
     */
    private String productionDate;

    /**
     * 审核状态, 0=审核中 1=审核通过 2=审核拒绝
     */
    private List<Integer> state;

    public static ProductionDateAuditQuery of(Integer warehouseId, List<Integer> state) {
        ProductionDateAuditQuery query = new ProductionDateAuditQuery();
        query.setWarehouseId(warehouseId);
        query.setState(state);
        return query;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(String productionDate) {
        this.productionDate = productionDate;
    }

    public List<Integer> getState() {
        return state;
    }

    public void setState(List<Integer> state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "ProductionDateAuditQuery{" +
               "warehouseId=" + warehouseId +
               ", productName='" + productName + '\'' +
               ", skuId=" + skuId +
               ", productionDate='" + productionDate + '\'' +
               ", state=" + state +
               '}';
    }
}
