package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;

/**
 * @author: lid<PERSON><PERSON>
 * @date 2018/12/7 13:37
 */
public class ProductStockStoreQuery implements Serializable {

    /**
     * 经销商id
     */
    private Long dealerId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页的数量
     */
    private Integer pageSize;

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public Long getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(Long dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 页码
     * 
     * @return
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 设置 页码
     * 
     * @param pageNum
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 获取 每页的数量
     * 
     * @return
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 设置 每页的数量
     * 
     * @param pageSize
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
