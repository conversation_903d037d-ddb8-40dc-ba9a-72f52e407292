package com.yijiupi.himalaya.supplychain.batchinventory.constant;

/**
 * 订单标记状态
 */
public enum DeliveryOrderMarkStateType {
    /**
     * 未标记
     */
    未标记(-1),
    /**
     * 全部配送
     */
    全部配送(0),
    /**
     * 部分发货
     */
    部分发货(1),
    /**
     * 部分配送
     */
    部分配送(2),
    /**
     * 延迟配送
     */
    延迟配送(3),
    /**
     * 配送失败
     */
    配送失败(4),
    /**
     * 延迟配送已入库
     */
    延迟配送已入库(5);

    private Integer type;

    DeliveryOrderMarkStateType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    @Override
    public String toString() {
        return String.valueOf(this.type);
    }

    public static DeliveryOrderMarkStateType getEnum(Integer type) {
        DeliveryOrderMarkStateType e = null;

        if (type != null) {
            for (DeliveryOrderMarkStateType o : values()) {
                if (o.getType().equals(type)) {
                    e = o;
                }
            }
        }
        return e;
    }
}
