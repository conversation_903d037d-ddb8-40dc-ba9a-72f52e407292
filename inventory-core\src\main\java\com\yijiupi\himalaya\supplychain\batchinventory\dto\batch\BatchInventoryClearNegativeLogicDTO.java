package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.util.List;

/**
 * 清理负货位库存逻辑
 *
 * <AUTHOR>
 * @date 2020-05-21 10:39
 */
public class BatchInventoryClearNegativeLogicDTO implements Serializable {
    private static final long serialVersionUID = -9134397568854673382L;

    /**
     * 负货位库存数据
     */
    private List<BatchInventoryLogicDTO> negativeBatchInventoryList;

    /**
     * 变更的货位库存数据
     */
    private List<BatchInventoryLogicDTO> changeBatchInventoryList;

    public List<BatchInventoryLogicDTO> getNegativeBatchInventoryList() {
        return negativeBatchInventoryList;
    }

    public void setNegativeBatchInventoryList(List<BatchInventoryLogicDTO> negativeBatchInventoryList) {
        this.negativeBatchInventoryList = negativeBatchInventoryList;
    }

    public List<BatchInventoryLogicDTO> getChangeBatchInventoryList() {
        return changeBatchInventoryList;
    }

    public void setChangeBatchInventoryList(List<BatchInventoryLogicDTO> changeBatchInventoryList) {
        this.changeBatchInventoryList = changeBatchInventoryList;
    }
}
