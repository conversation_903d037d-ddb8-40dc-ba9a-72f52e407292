package com.yijiupi.himalaya.supplychain.dto.easysell;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 根据商品skuid,和经销商id查询不同仓库库存返回DTO
 * 
 * @author: lidengfeng
 * @date: 2018/8/14 14:45
 */
public class ProductInfoStoreDTO implements Serializable {

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 产品规格id
     */
    private Long productSpecificationId;

    /**
     * sku名称
     */
    private String name;

    /**
     * 库存总量，按最小单位统计
     */
    private BigDecimal totalcountMinunit;

    /**
     * 包装规格名称
     */
    private String specificationname;

    /**
     * 包装规格大单位
     */
    private String packagename;

    /**
     * 包装规格小单位
     */
    private String unitName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packagequantity;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getTotalcountMinunit() {
        return totalcountMinunit;
    }

    public void setTotalcountMinunit(BigDecimal totalcountMinunit) {
        this.totalcountMinunit = totalcountMinunit;
    }

    public String getSpecificationname() {
        return specificationname;
    }

    public void setSpecificationname(String specificationname) {
        this.specificationname = specificationname;
    }

    public String getPackagename() {
        return packagename;
    }

    public void setPackagename(String packagename) {
        this.packagename = packagename;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackagequantity() {
        return packagequantity;
    }

    public void setPackagequantity(BigDecimal packagequantity) {
        this.packagequantity = packagequantity;
    }

}
