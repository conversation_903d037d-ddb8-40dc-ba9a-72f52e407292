package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * 自定义批属性管理查询DTO
 *
 * <AUTHOR> 2018/4/9
 */
public class BatchAttributeDicQueryDTO extends PageCondition {
    /**
     * 属性类型
     */
    private Byte attributeType;
    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 获取 属性类型
     */
    public Byte getAttributeType() {
        return this.attributeType;
    }

    /**
     * 设置 属性类型
     */
    public void setAttributeType(Byte attributeType) {
        this.attributeType = attributeType;
    }

    /**
     * 获取 属性名称
     */
    public String getAttributeName() {
        return this.attributeName;
    }

    /**
     * 设置 属性名称
     */
    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }
}
