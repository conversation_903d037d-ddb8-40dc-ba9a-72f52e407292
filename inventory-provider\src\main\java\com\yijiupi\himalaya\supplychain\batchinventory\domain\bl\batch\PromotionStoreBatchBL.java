package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.assignment.dto.todo.LinQiLocationTaskDTO;
import com.yijiupi.himalaya.assignment.service.ILinQiLocationTaskService;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event.PromotionStoreBatchEventBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchInventoryConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.promotion.ProductPromotionStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.promotion.ProductPromotionStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryMoveDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.*;
import com.yijiupi.himalaya.supplychain.dto.SpecIdAndOwnerIdDTO;
import com.yijiupi.himalaya.supplychain.dto.WarehouseBatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.WarehouseBatchInventoryReportDTO;
import com.yijiupi.himalaya.supplychain.enums.ProductStoreBatchPropertyEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderItemDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferStateEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IStoreTransferOrderService;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.service.IWarehouseInventoryReportQueryService;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * 促销批次库存
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Service
public class PromotionStoreBatchBL {

    private final static Logger LOGGER = LoggerFactory.getLogger(PromotionStoreBatchBL.class);

    @Autowired
    private ProductPromotionStoreBatchMapper promotionStoreBatchMapper;

    @Autowired
    private BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;

    @Autowired
    private BatchInventoryQueryBL batchInventoryQueryBL;

    @Autowired
    private PromotionStoreBatchEventBL promotionStoreBatchEventBL;

    @Reference
    private ILocationService iLocationService;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Reference
    private IAdminUserQueryService iAdminUserQueryService;

    @Reference
    private IStoreTransferOrderService iStoreTransferOrderService;

    @Reference
    private IProductLocationService iProductLocationService;

    @Reference
    private ILinQiLocationTaskService iLinQiLocationTaskService;

    @Reference
    private IProductConfigService iProductConfigService;

    @Reference
    private IWarehouseQueryService iWwarehouseQueryService;

    @Reference
    private IWarehouseInventoryReportQueryService warehouseInventoryReportQueryService;

    /**
     * 获取存在促销批次库存产品数据
     *
     * @return
     */
    public List<PromotionStoreBatchResultDTO> listPromotionStoreBatchProduct(PromotionStoreBatchQueryDTO queryDTO) {
        LOGGER.info("获取存在促销批次库存产品入参:{}", JSON.toJSONString(queryDTO));
        List<PromotionStoreBatchResultDTO> resultDTOS =
            promotionStoreBatchMapper.listPromotionStoreBatchProduct(queryDTO);
        LOGGER.info("获取存在促销批次库存产品数据:{}", JSON.toJSONString(resultDTOS));
        return resultDTOS;
    }

    /**
     * 获取产品是否混合批次库存标识
     *
     * @return
     */
    public List<PromotionStoreBatchResultDTO> listProductMixedBatchFlag(PromotionStoreBatchQueryDTO queryDTO) {
        LOGGER.info("获取产品是否混合批次库存标识入参:{}", JSON.toJSONString(queryDTO));
        List<PromotionStoreBatchResultDTO> resultDTOS = promotionStoreBatchMapper.listProductMixedBatchFlag(queryDTO);
        LOGGER.info("获取产品是否混合批次库存标识结果:{}", JSON.toJSONString(resultDTOS));
        return resultDTOS;
    }

    /**
     * 批量新增促销批次库存
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ProductPromotionStoreBatchDTO> insertBatch(List<ProductPromotionStoreBatchDTO> addDTOS) {
        AssertUtils.notEmpty(addDTOS, "新增参数列表不能为空");
        addDTOS.forEach(p -> {
            AssertUtils.notNull(p.getWarehouseId(), "仓库ID不能为空");
            AssertUtils.notNull(p.getBatchAttributeInfoNo(), "批次编号不能为空");
            AssertUtils.notNull(p.getSkuId(), "skuID不能为空");
            AssertUtils.notNull(p.getProductionDate(), "生产日期不能为空");
        });

        LOGGER.info("批量新增促销批次库存入参:{}", JSON.toJSONString(addDTOS));
        Integer warehouseId = addDTOS.stream().findFirst().get().getWarehouseId();
        // 检查是否存在已存在相同仓库 + 批次编号 的数据
        PromotionStoreBatchQueryDTO queryDTO = new PromotionStoreBatchQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setBatchAttributeInfoNos(
            addDTOS.stream().map(p -> p.getBatchAttributeInfoNo()).distinct().collect(Collectors.toList()));
        List<ProductPromotionStoreBatchDTO> existPOS = promotionStoreBatchMapper.queryByCondition(queryDTO);
        if (CollectionUtils.isNotEmpty(existPOS)) {
            LOGGER.info("已存在相同仓库和批次编号的促销批次数据：{}", JSON.toJSONString(existPOS));
            // addDTOS过滤已存在getIdentityKey的数据
            List<String> existNOS =
                existPOS.stream().map(p -> p.getIdentityKey()).distinct().collect(Collectors.toList());
            addDTOS =
                addDTOS.stream().filter(dto -> !existNOS.contains(dto.getIdentityKey())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(addDTOS)) {
            return Collections.emptyList();
        }

        List<ProductPromotionStoreBatchPO> addPOS = new ArrayList<>();
        addDTOS.stream().forEach(dto -> {
            ProductPromotionStoreBatchPO po = new ProductPromotionStoreBatchPO();
            po.setId(UUIDGenerator.getUUID(po.getClass().getName()));
            po.setBatchAttributeInfoNo(dto.getBatchAttributeInfoNo());
            po.setWarehouseId(dto.getWarehouseId());
            po.setSkuId(dto.getSkuId());
            po.setProductName(dto.getProductName());
            po.setProductionDate(dto.getProductionDate());
            po.setIsDelete(YesOrNoEnum.NO.getValue().byteValue());
            po.setCreateUser(StringUtils.isEmpty(dto.getCreateUser()) ? "0" : dto.getCreateUser());
            po.setCreateTime(new Date());
            po.setLastUpdateUser(dto.getLastUpdateUser());
            po.setLastUpdateTime(new Date());
            addPOS.add(po);
        });

        promotionStoreBatchMapper.batchInsert(addPOS);
        LOGGER.info("批量新增促销批次库存成功");
        return addDTOS;
    }

    /**
     * 批量删除促销批次库存
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteByIds(List<Long> ids) {
        AssertUtils.notEmpty(ids, "ids不能为空");
        LOGGER.info("批量删除促销批次库存入参 :{}", JSON.toJSONString(ids));
        List<ProductPromotionStoreBatchPO> poList = promotionStoreBatchMapper.selectByIds(ids);
        if (CollectionUtils.isEmpty(poList)) {
            LOGGER.info("批量删除促销批次库存 ids不存在：{}", JSON.toJSONString(ids));
            return;
        }

        List<Long> deleteIds = poList.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
        promotionStoreBatchMapper.batchDeleteByIds(deleteIds);
        LOGGER.info("批量删除促销批次库存成功 ids :{}", JSON.toJSONString(deleteIds));
    }

    /**
     * 条件查询促销批次库存
     *
     * @return
     */
    public List<ProductPromotionStoreBatchDTO> queryByCondition(PromotionStoreBatchQueryDTO queryDTO) {
        LOGGER.info("条件查询促销批次库存入参:{}", JSON.toJSONString(queryDTO));
        List<ProductPromotionStoreBatchDTO> resultDTOS = promotionStoreBatchMapper.queryByCondition(queryDTO);
        LOGGER.info("条件查询促销批次库存结果:{}", JSON.toJSONString(resultDTOS));
        return resultDTOS;
    }

    /**
     * 根据批次编号检查删除促销批次库存
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @DistributeLock(conditions = "#deleteDTO.batchAttributeInfoNo", expireMills = 60000, sleepMills = 60000,
        key = "deleteByBatchAttributeInfoNo", lockType = DistributeLock.LockType.WAITLOCK)
    public void deleteByBatchAttributeInfoNo(ProductPromotionStoreBatchDTO deleteDTO) {
        AssertUtils.notNull(deleteDTO, "参数不能为空");
        AssertUtils.notNull(deleteDTO.getBatchAttributeInfoNo(), "批次编号不能为空");
        LOGGER.info("根据批次编号检查删除促销批次库存入参 :{}", JSON.toJSONString(deleteDTO));

        Long count = promotionStoreBatchMapper.getStoreBatchPromotionCount(deleteDTO.getWarehouseId(), null,
            deleteDTO.getBatchAttributeInfoNo());
        if (count > 0) {
            LOGGER.info("批次编号存在大于0促销批次库存无需删除, 批次编号 :{}", JSON.toJSONString(deleteDTO.getBatchAttributeInfoNo()));
            return;
        }

        PromotionStoreBatchQueryDTO queryDTO = new PromotionStoreBatchQueryDTO();
        queryDTO.setWarehouseId(deleteDTO.getWarehouseId());
        queryDTO.setBatchAttributeInfoNos(Arrays.asList(deleteDTO.getBatchAttributeInfoNo()));
        List<ProductPromotionStoreBatchDTO> resultDTOS = promotionStoreBatchMapper.queryByCondition(queryDTO);
        if (CollectionUtils.isEmpty(resultDTOS)) {
            return;
        }

        List<Long> deleteIds = resultDTOS.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
        promotionStoreBatchMapper.batchDeleteByIds(deleteIds);
        LOGGER.info("根据批次编号检查删除促销批次库存成功 ids :{}", JSON.toJSONString(deleteIds));
    }

    /**
     * 入库促销货位检查
     *
     * @return
     */
    public void checkInStockLocation(List<PromotionLocationCheckDTO> checkDTOS) {
        AssertUtils.notEmpty(checkDTOS, "参数不能为空");
        checkDTOS.stream().forEach(checkDTO -> {
            AssertUtils.notNull(checkDTO, "参数不能为空");
            AssertUtils.notNull(checkDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(checkDTO.getSkuId(), "skuid不能为空");
            AssertUtils.notNull(checkDTO.getLocationId(), "货位id不能为空");
            // AssertUtils.notNull(checkDTO.getProductionDate(), "生产日期不能为空");
        });

        LOGGER.info("入库促销货位检查入参 :{}", JSON.toJSONString(checkDTOS));
        Integer warehouseId = checkDTOS.stream().findFirst().get().getWarehouseId();
        List<Long> skuIdList = checkDTOS.stream().map(p -> p.getSkuId()).distinct().collect(Collectors.toList());
        PromotionStoreBatchQueryDTO queryDTO = new PromotionStoreBatchQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setSkuIdList(skuIdList);
        List<ProductPromotionStoreBatchDTO> resultDTOS = promotionStoreBatchMapper.queryByCondition(queryDTO);
        LOGGER.info("入库促销货位检查,促销批次库存查询结果:{}", JSON.toJSONString(resultDTOS));

        List<Long> locationIds = checkDTOS.stream().map(p -> p.getLocationId()).distinct().collect(Collectors.toList());
        Map<Long, LoactionDTO> loactionDTOMap = iLocationService.findLocationByIds(locationIds).stream()
            .filter(Objects::nonNull).collect(Collectors.toMap(p -> p.getId(), Function.identity(), (v1, v2) -> v2));

        // 相同业务类型的分拣位、零拣位、零拣区、拣货区只能关联一个， 促销货位只能是零拣位或分拣位
        List<Byte> subcategoryList =
            Arrays.asList(LocationEnum.分拣位.getType().byteValue(), LocationEnum.零拣位.getType().byteValue(),
                LocationAreaEnum.拣货区.getType().byteValue(), LocationAreaEnum.零拣区.getType().byteValue());

        checkDTOS.stream().forEach(checkDTO -> {
            LoactionDTO loactionDTO = loactionDTOMap.get(checkDTO.getLocationId());
            if (Objects.isNull(loactionDTO)) {
                throw new BusinessValidateException("入库货位不存在，货位id:" + checkDTO.getLocationId());
            }

            boolean isPromotion = CollectionUtils.isNotEmpty(resultDTOS)
                && resultDTOS.stream().anyMatch(p -> Objects.equals(checkDTO.getSkuId(), p.getSkuId())
                    && Objects.equals(checkDTO.getProductionDate(), p.getProductionDate()));
            if (isPromotion) {
                // 存在促销批次库存，入库货位必须是促销货位
                if (!Objects.equals(loactionDTO.getBusinessType(), LocationBusinessTypeEnum.促销.getType())) {
                    throw new BusinessValidateException(
                        String.format("产品 %s 存在促销批次库存，请确认生产日期或选择促销货位入库", checkDTO.getProductName()));
                }
                // 检查促销货位是否是零拣位或分拣位
                if (!subcategoryList.contains(loactionDTO.getSubcategory())) {
                    throw new BusinessValidateException(
                        String.format("产品 %s 入库促销货位必须是零拣位或分拣位", checkDTO.getProductName()));
                }
            } else {
                // 不存在促销批次库存，入库货位必须是非促销货位
                if (Objects.equals(loactionDTO.getBusinessType(), LocationBusinessTypeEnum.促销.getType())) {
                    throw new BusinessValidateException(
                        String.format("产品 %s 不存在促销批次库存，请确认生产日期或选择非促销货位入库)", checkDTO.getProductName()));
                }
            }
        });
    }

    /**
     * 检查并批量转移批次库存
     * 
     * @param batchInventoryDTOS
     * @param userId
     */
    public void transferStoreBatch(List<BatchInventoryDTO> batchInventoryDTOS, Integer userId) {
        if (CollectionUtils.isEmpty(batchInventoryDTOS)) {
            return;
        }

        LOGGER.info("检查并批量转移批次库存入参：{}", JSON.toJSONString(batchInventoryDTOS));
        // 更新前批次库存
        Map<String, BatchInventoryDTO> batchInventoryMap = batchInventoryDTOS.stream().filter(Objects::nonNull)
            .collect(Collectors.toMap(p -> p.getStoreBatchId(), Function.identity(), (v1, v2) -> v1));
        Integer warehouseId = batchInventoryDTOS.stream().findFirst().get().getWarehouseId();
        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(warehouseId);
        Integer cityId = warehouse.getCityId();
        String userName = userId != null ? iAdminUserQueryService.queryUserNameById(Long.valueOf(userId)) : null;
        List<Long> skuIds = batchInventoryDTOS.stream().filter(p -> p != null && p.getProductSkuId() != null)
            .map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());

        // 查询货位信息
        Map<Long, List<ProductLoactionItemDTO>> productLoactionMap = getProductLoactionMap(warehouseId, skuIds);
        // 获取促销批次编号
        List<String> promotionBatchNos = getPromotionBatchNosBySkuIds(warehouseId, skuIds);
        LOGGER.info("检查并批量转移批次库存 获取促销批次：{}", JSON.toJSONString(promotionBatchNos));

        List<String> deleteBatchNos = new ArrayList<>();
        List<StoreTransferOrderItemDTO> transferItems = new ArrayList<>();

        // 查询新的批次库存数据
        List<ProductStoreBatchPO> newStoreBatchPOS = batchInventoryProductStoreBatchMapper
            .findProductStoreBatchListById(Lists.newArrayList(batchInventoryMap.keySet()));
        LOGGER.info("检查并批量转移批次库存 更新后查询批次库存结果：{}", JSON.toJSONString(newStoreBatchPOS));

        for (ProductStoreBatchPO newPO : newStoreBatchPOS) {
            BatchInventoryDTO oldBatchInventory = batchInventoryMap.get(newPO.getId());
            if (oldBatchInventory == null) {
                continue;
            }

            // 判断并获取移库目标货位，记录待删除的促销批次
            ProductLoactionItemDTO toProductLoaction =
                getTransferToLocation(oldBatchInventory, newPO, promotionBatchNos, productLoactionMap, deleteBatchNos);
            if (toProductLoaction == null) {
                continue;
            }

            // 构造移库对象
            StoreTransferOrderItemDTO itemDTO =
                buildTransferOrderItemDTO(oldBatchInventory, newPO, toProductLoaction, cityId, userName);
            transferItems.add(itemDTO);
        }

        // 提交移库订单
        submitTransferOrder(transferItems, warehouseId, cityId, userId, userName, warehouse.getName());

        // 检查促销批次并删除
        deletePromotionBatches(deleteBatchNos);
    }

    private ProductLoactionItemDTO getTransferToLocation(BatchInventoryDTO old, ProductStoreBatchPO newPO,
        List<String> promotionBatchNos, Map<Long, List<ProductLoactionItemDTO>> locationMap,
        List<String> deleteBatchNos) {

        Long skuId = old.getProductSkuId();
        boolean isPromotionOld = promotionBatchNos.contains(old.getBatchAttributeInfoNo());
        boolean isPromotionNew = promotionBatchNos.contains(newPO.getBatchAttributeInfoNo());
        boolean isPromotionBusinessType = LocationBusinessTypeEnum.促销.getType().equals(old.getLocationBusinessType());

        if (isPromotionOld && !isPromotionNew) {
            // 老是促销，新不是促销 → 移到非促销货位
            deleteBatchNos.add(old.getBatchAttributeInfoNo());
            // 货位是非促销货位不处理
            if (!isPromotionBusinessType) {
                // 货位是非促销货位不处理
                return null;
            }

            return locationMap.getOrDefault(skuId, Collections.emptyList()).stream()
                .filter(loc -> !LocationBusinessTypeEnum.促销.getType().equals(loc.getBusinessType())).findFirst()
                .orElse(null);

        } else if (!isPromotionOld && isPromotionNew) {
            // 老不是促销，新是促销 → 移到促销货位
            if (isPromotionBusinessType) {
                // 新货位是促销货位不处理
                return null;
            }

            return locationMap.getOrDefault(skuId, Collections.emptyList()).stream()
                .filter(loc -> LocationBusinessTypeEnum.促销.getType().equals(loc.getBusinessType())).findFirst()
                .orElse(null);
        }

        return null;
    }

    private StoreTransferOrderItemDTO buildTransferOrderItemDTO(BatchInventoryDTO old, ProductStoreBatchPO newPO,
        ProductLoactionItemDTO targetLocation, Integer cityId, String userName) {
        StoreTransferOrderItemDTO item = new StoreTransferOrderItemDTO();
        BigDecimal[] count = newPO.getTotalCount().divideAndRemainder(old.getPackageQuantity());

        item.setOrg_id(cityId);
        item.setState(StoreTransferStateEnum.待移库.getType());
        item.setOwnerName(old.getOwnerName());
        item.setSkuId(old.getProductSkuId());
        item.setProductName(old.getProductSkuName());
        item.setSpecName(old.getSpecificationName());
        item.setSpecQuantity(old.getPackageQuantity());
        item.setPackageName(old.getPackageName());
        item.setUnitName(old.getUnitName());
        item.setPackageCount(count[0]);
        item.setUnitCount(count[1]);
        item.setUnitTotalCount(newPO.getTotalCount());
        item.setFromLocation_id(newPO.getLocationId());
        item.setFromLocationName(newPO.getLocationName());
        item.setToLocation_id(targetLocation.getLocationId());
        item.setToLocationName(targetLocation.getLocationName());
        item.setToChannel(null != old.getChannel() ? old.getChannel().toString() : "");
        item.setCreateUser(userName);
        item.setLastupdateuser(userName);
        item.setProductionDate(newPO.getProductionDate());
        item.setBatchTime(newPO.getBatchTime());
        item.setProductSpecificationId(old.getProductSpecificationId());
        item.setSecOwnerId(old.getSecOwnerId());
        item.setBatchAttributeInfoNo(newPO.getBatchAttributeInfoNo());
        item.setProductStoreId(newPO.getProductStoreId());
        item.setProductStoreBatchId(newPO.getId());

        return item;
    }

    private void submitTransferOrder(List<StoreTransferOrderItemDTO> items, Integer warehouseId, Integer cityId,
        Integer userId, String userName, String warehouseName) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        StoreTransferOrderDTO transferOrderDTO = new StoreTransferOrderDTO();
        transferOrderDTO.setOrg_id(cityId);
        transferOrderDTO.setState(StoreTransferStateEnum.待移库.getType());
        transferOrderDTO.setTransferType(StoreTransferEnum.人工移库.getType());
        transferOrderDTO.setStartTime(new Date());
        transferOrderDTO.setWarehouse_Id(warehouseId);
        transferOrderDTO.setWarehouseName(warehouseName);
        transferOrderDTO.setSorter_id(userId);
        transferOrderDTO.setSorterName(userName);
        transferOrderDTO.setRemark("促销批次变更移库");
        transferOrderDTO.setCreateUser(String.valueOf(userId));
        transferOrderDTO.setLastupdateuser(userName);
        transferOrderDTO.setStoreTransferOrderItemDTOS(items);

        LOGGER.info("检查并批量转移批次库存 提交移库数据：{}", JSON.toJSONString(transferOrderDTO));
        iStoreTransferOrderService.updateStoreTranferNoOrder(transferOrderDTO);
    }

    public void deletePromotionBatches(List<String> batchNos) {
        if (CollectionUtils.isEmpty(batchNos)) {
            return;
        }

        LOGGER.info("检查并批量转移批次库存 删除批次编号：{}", JSON.toJSONString(batchNos));
        batchNos.forEach(batchNo -> {
            ProductPromotionStoreBatchDTO deleteDTO = new ProductPromotionStoreBatchDTO();
            deleteDTO.setBatchAttributeInfoNo(batchNo);
            deleteByBatchAttributeInfoNo(deleteDTO);
        });
    }

    private Map<Long, List<ProductLoactionItemDTO>> getProductLoactionMap(Integer warehouseId, List<Long> skuIds) {
        return Optional.ofNullable(iProductLocationService.findLocationBySkuId(warehouseId, skuIds))
            .orElse(Collections.emptyList()).stream().filter(Objects::nonNull)
            .collect(Collectors.groupingBy(ProductLoactionItemDTO::getProductSkuId));
    }

    private List<String> getPromotionBatchNosBySkuIds(Integer warehouseId, List<Long> skuIds) {
        // 获取促销批次编号
        PromotionStoreBatchQueryDTO queryDTO = new PromotionStoreBatchQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setSkuIdList(skuIds);
        List<String> batchNos = promotionStoreBatchMapper.queryByCondition(queryDTO).stream()
            .filter(p -> p != null && org.springframework.util.StringUtils.hasText(p.getBatchAttributeInfoNo()))
            .map(p -> p.getBatchAttributeInfoNo()).distinct().collect(Collectors.toList());
        return batchNos;
    }

    /**
     * 根据批次库存批量新增促销批次库存记录,并判断是否新增关联货位代办
     *
     * @param addDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertByStoreBatchIds(ProductPromotionStoreBatchDTO addDTO) {
        AssertUtils.notNull(addDTO, "参数不能为空");
        AssertUtils.notEmpty(addDTO.getStoreBatchIds(), "批次库存id不能为空");
        LOGGER.info("根据批次库存批量新增促销记录入参：{}", JSON.toJSONString(addDTO));

        // 查询批次库存
        BatchInventoryQueryDTO queryDTO = new BatchInventoryQueryDTO();
        queryDTO.setWarehouseId(addDTO.getWarehouseId());
        queryDTO.setStoreBatchIds(addDTO.getStoreBatchIds());
        queryDTO.setLimitSku((byte)1);
        PageResult<BatchInventoryPO> pageResult =
            batchInventoryProductStoreBatchMapper.findBatchInventoryList(queryDTO, null, null);
        LOGGER.info("根据批次库存批量新增促销记录 查询批次库存结果：{}", JSON.toJSONString(pageResult));
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        List<BatchInventoryPO> batchInventoryPOS = batchInventoryPOPageList.getDataList().stream()
            .filter(p -> p != null && p.getProductionDate() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchInventoryPOS)) {
            LOGGER.info("不存在数量大于0且存在生产日期的批次库存 ids：{}", JSON.toJSONString(addDTO.getStoreBatchIds()));
            return;
        }

        List<ProductPromotionStoreBatchDTO> addDTOS = batchInventoryPOS.stream().map(p -> {
            ProductPromotionStoreBatchDTO dto = new ProductPromotionStoreBatchDTO();
            dto.setWarehouseId(p.getWarehouseId());
            dto.setSkuId(p.getProductSkuId());
            dto.setProductName(p.getProductSkuName());
            dto.setProductionDate(p.getProductionDate());
            dto.setBatchAttributeInfoNo(p.getBatchAttributeInfoNo());
            dto.setCreateUser(addDTO.getCreateUser());
            dto.setStorageAttribute(p.getStorageAttribute());
            return dto;
        }).collect(Collectors.toList());

        // 批量新增
        insertBatch(addDTOS);
        // 创建关联货位任务
        if (addDTO.getAddLocationTodoTask()) {
            createLocationTaskTask(addDTOS);
        }
    }

    public void createLocationTaskTask(List<ProductPromotionStoreBatchDTO> addDTOS) {
        LOGGER.info("促销生成产品关联货位任务 入参：{}", JSON.toJSONString(addDTOS));

        // 存在混合库存的促销批次才创建任务
        List<ProductPromotionStoreBatchDTO> mixedBatchDTOS = getMixedBatch(addDTOS);
        if (CollectionUtils.isEmpty(mixedBatchDTOS)) {
            return;
        }

        Integer warehouseId = addDTOS.stream().findFirst().get().getWarehouseId();
        List<Long> skuIds = mixedBatchDTOS.stream().map(ProductPromotionStoreBatchDTO::getSkuId).distinct()
            .collect(Collectors.toList());
        // 根据sku查询产品关联货位
        Map<Long, List<ProductLoactionItemDTO>> productLoactionMap = getProductLoactionMap(warehouseId, skuIds);

        List<LinQiLocationTaskDTO> locationTaskDTOS = new ArrayList<>();
        for (ProductPromotionStoreBatchDTO dto : mixedBatchDTOS) {

            ProductLoactionItemDTO productLoactionItem =
                productLoactionMap.getOrDefault(dto.getSkuId(), Collections.emptyList()).stream()
                    .filter(loc -> Objects.equals(LocationBusinessTypeEnum.促销.getType(), loc.getBusinessType()))
                    .findFirst().orElse(null);
            if (Objects.nonNull(productLoactionItem)) {
                return;
            }

            LinQiLocationTaskDTO taskDTO = new LinQiLocationTaskDTO();
            taskDTO.setWarehouseId(dto.getWarehouseId());
            taskDTO.setSkuId(dto.getSkuId());
            taskDTO.setProductName(dto.getProductName());
            taskDTO.setTaskProperty(dto.getStorageAttribute() != null ? dto.getStorageAttribute().intValue() : 0);
            // taskDTO.setUserId(addDTO.getCreateUser());
            taskDTO.setProductionDate(DateUtils.getDateFormat(dto.getProductionDate()));
            locationTaskDTOS.add(taskDTO);
        }

        if (CollectionUtils.isEmpty(locationTaskDTOS)) {
            return;
        }

        LOGGER.info("促销生成产品关联货位任务参数：{}", JSON.toJSONString(locationTaskDTOS));
        iLinQiLocationTaskService.createTask(locationTaskDTOS);
    }

    /**
     * 自动转残次品
     *
     * @param moveDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void storeBatchTransferToDefective(BatchInventoryMoveDTO moveDTO) {
        AssertUtils.notNull(moveDTO, "参数不能为空");
        AssertUtils.notNull(moveDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(moveDTO.getStoreBatchIds(), "批次库存id不能为空");
        LOGGER.info("自动转残次品入参：{}", JSON.toJSONString(moveDTO));
        Integer warehouseId = moveDTO.getWarehouseId();
        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(warehouseId);
        Integer cityId = warehouse.getCityId();
        String userName = moveDTO.getUserId() != null
            ? iAdminUserQueryService.queryUserNameById(Long.valueOf(moveDTO.getUserId())) : null;

        BatchInventoryQueryDTO queryDTO = new BatchInventoryQueryDTO();
        queryDTO.setWarehouseId(moveDTO.getWarehouseId());
        queryDTO.setStoreBatchIds(moveDTO.getStoreBatchIds());
        queryDTO.setLimitSku((byte)1);
        PageResult<BatchInventoryPO> pageResult = batchInventoryProductStoreBatchMapper.findBatchInventoryList(queryDTO,
            queryDTO.getPageNum(), queryDTO.getPageSize());
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        List<BatchInventoryDTO> batchInventoryDTOS =
            BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOPageList.getDataList());
        if (CollectionUtils.isEmpty(batchInventoryDTOS)) {
            LOGGER.info("自动转残次品批次库存不存在：{}", JSON.toJSONString(moveDTO));
            return;
        }
        LOGGER.info("自动转残次品批次库存查询结果：{}", JSON.toJSONString(batchInventoryDTOS));
        List<Long> skuIds = batchInventoryDTOS.stream().filter(p -> p != null && p.getProductSkuId() != null)
            .map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());

        LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
        locationQueryDTO.setWarehouseId(warehouseId);
        locationQueryDTO.setCategoryList(
            Arrays.asList(CategoryEnum.CARGO_LOCATION.getByteValue(), CategoryEnum.CARGO_AREA.getByteValue()));
        locationQueryDTO.setSubcategoryList(
            Arrays.asList(LocationEnum.残次品位.getType().byteValue(), LocationAreaEnum.残次品区.getType().byteValue()));
        locationQueryDTO.setState(LocationStateEnum.启用.getType());
        List<LocationReturnDTO> defectiveLocationList =
            iProductLocationService.listLocationByCondition(locationQueryDTO);
        if (CollectionUtils.isEmpty(defectiveLocationList)) {
            LOGGER.warn("自动转残次品货位不存在：{}", JSON.toJSONString(moveDTO));
            return;
        }

        List<StoreTransferOrderItemDTO> transferItems = new ArrayList<>();

        for (BatchInventoryDTO dto : batchInventoryDTOS) {
            StoreTransferOrderItemDTO item = new StoreTransferOrderItemDTO();
            BigDecimal[] count = dto.getStoreTotalCount().divideAndRemainder(dto.getPackageQuantity());
            LocationReturnDTO defectiveLocation = defectiveLocationList.stream().findFirst().get();

            item.setOrg_id(cityId);
            item.setState(StoreTransferStateEnum.待移库.getType());
            item.setOwnerName(dto.getOwnerName());
            item.setSkuId(dto.getProductSkuId());
            item.setProductName(dto.getProductSkuName());
            item.setSpecName(dto.getSpecificationName());
            item.setSpecQuantity(dto.getPackageQuantity());
            item.setPackageName(dto.getPackageName());
            item.setUnitName(dto.getUnitName());
            item.setPackageCount(count[0]);
            item.setUnitCount(count[1]);
            item.setOverMovePackageCount(count[0]);
            item.setOverMoveUnitCount(count[1]);
            item.setUnitTotalCount(dto.getStoreTotalCount());
            item.setFromLocation_id(dto.getLocationId());
            item.setFromLocationName(dto.getLocationName());
            item.setToLocation_id(defectiveLocation.getId());
            item.setToLocationName(defectiveLocation.getName());
            item.setToChannel(null != dto.getChannel() ? dto.getChannel().toString() : "");
            item.setCreateUser(userName);
            item.setLastupdateuser(userName);
            item.setProductionDate(dto.getProductionDate());
            item.setBatchTime(dto.getBatchTime());
            item.setProductSpecificationId(dto.getProductSpecificationId());
            item.setSecOwnerId(dto.getSecOwnerId());
            item.setBatchAttributeInfoNo(dto.getBatchAttributeInfoNo());
            item.setProductStoreId(dto.getProductStoreId());
            item.setProductStoreBatchId(dto.getStoreBatchId());
            transferItems.add(item);
        }

        if (CollectionUtils.isEmpty(transferItems)) {
            return;
        }

        StoreTransferOrderDTO transferOrderDTO = new StoreTransferOrderDTO();
        transferOrderDTO.setOrg_id(cityId);
        transferOrderDTO.setState(StoreTransferStateEnum.待移库.getType());
        transferOrderDTO.setTransferType(StoreTransferEnum.处理品转入移库.getType());
        transferOrderDTO.setStartTime(new Date());
        transferOrderDTO.setWarehouse_Id(warehouseId);
        transferOrderDTO.setWarehouseName(warehouse.getName());
        transferOrderDTO.setSorter_id(moveDTO.getUserId());
        transferOrderDTO.setSorterName(userName);
        transferOrderDTO.setRemark("临期滞销强制转入");
        transferOrderDTO.setCreateUser(userName);
        transferOrderDTO.setLastupdateuser(userName);
        transferOrderDTO.setBatchProperty(ProductStoreBatchPropertyEnum.自动转入.getType());
        transferOrderDTO.setStoreTransferOrderItemDTOS(transferItems);
        LOGGER.info("自动转残次品提交移库数据：{}", JSON.toJSONString(transferOrderDTO));
        iStoreTransferOrderService.updateStoreTranferNoOrder(transferOrderDTO);

        // 通知仓库
        transferToDefectiveNotify(moveDTO.getAutoMoveFlag(), batchInventoryDTOS);
    }

    /**
     * 批量检查并删除促销批次
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteWithCheck(List<String> batchAttributeInfoNos) {
        AssertUtils.notEmpty(batchAttributeInfoNos, "批次编号不能为空");

        LOGGER.info("批量检查并删除促销批次入参 :{}", JSON.toJSONString(batchAttributeInfoNos));
        PromotionStoreBatchQueryDTO promotionQueryDTO = new PromotionStoreBatchQueryDTO();
        promotionQueryDTO.setBatchAttributeInfoNos(batchAttributeInfoNos);
        List<ProductPromotionStoreBatchDTO> promotionDTOS =
            promotionStoreBatchMapper.queryByCondition(promotionQueryDTO);
        LOGGER.info("批量检查并删除促销批次 促销批次信息 :{}", JSON.toJSONString(promotionDTOS));
        if (CollectionUtils.isEmpty(promotionDTOS)) {
            return;
        }

        PromotionStoreBatchQueryDTO storeBatchQueryDTO = new PromotionStoreBatchQueryDTO();
        storeBatchQueryDTO.setBatchAttributeInfoNos(batchAttributeInfoNos);
        List<Long> promotionIds =
            Optional.ofNullable(promotionStoreBatchMapper.listPromotionStoreBatchNoGroup(storeBatchQueryDTO).stream()
                .filter(Objects::nonNull).map(PromotionStoreBatchResultDTO::getId).distinct()
                .collect(Collectors.toList())).orElse(Collections.emptyList());
        LOGGER.info("批量检查并删除促销批次 存在库存的促销批次ids :{}", JSON.toJSONString(promotionIds));

        List<Long> deleteIds = promotionDTOS.stream().filter(p -> !promotionIds.contains(p.getId())).map(p -> p.getId())
            .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deleteIds)) {
            return;
        }

        promotionStoreBatchMapper.batchDeleteByIds(deleteIds);
        LOGGER.info("批量检查并删除促销批次成功 ids :{}", JSON.toJSONString(deleteIds));
    }

    /**
     * 强制转入残次品通知仓管
     *
     * @return
     */
    public void transferToDefectiveNotify(boolean autoMove, List<BatchInventoryDTO> batchInventoryDTOS) {
        if (CollectionUtils.isEmpty(batchInventoryDTOS)) {
            return;
        }

        promotionStoreBatchEventBL.transferToDefectiveNotifyEvent(autoMove, batchInventoryDTOS);
    }

    /**
     * 根据交易促销活动创建促销批次库存
     *
     * @param message
     */
    @Transactional(rollbackFor = Exception.class)
    public void synTrdPromotion(TrdPromotionSyncMessage message) {
        AssertUtils.notNull(message, "参数不能为空");
        AssertUtils.notEmpty(message.getTimelimitPromotionItems(), "促销活动不能为空");

        LOGGER.info("根据交易促销活动创建促销批次库存入参：{}", JSON.toJSONString(message));

        // 构建查询条件 DTO 列表
        List<WarehouseBatchInventoryQueryDTO> queryDTOList = new ArrayList<>();
        message.getTimelimitPromotionItems().forEach(itemMessage -> {
            AssertUtils.notNull(itemMessage.getProductInfoSpecId(), "规格id不能为空");
            AssertUtils.notEmpty(itemMessage.getWarehouseIds(), "仓库id不能为空");
            AssertUtils.notEmpty(itemMessage.getConfigTypes(), "配置类型列表不能为空");

            Long productSpecId = itemMessage.getProductInfoSpecId().longValue();
            itemMessage.getWarehouseIds().forEach(warehouseId -> {
                SpecIdAndOwnerIdDTO specAndOwnerId = new SpecIdAndOwnerIdDTO();
                specAndOwnerId.setOwnerId(itemMessage.getOwnerId());
                specAndOwnerId.setProductSpecId(productSpecId);
                specAndOwnerId.setStartProductionDate(itemMessage.getCloseExpirationBeginDate());
                specAndOwnerId.setEndProductionDate(itemMessage.getCloseExpirationEndDate());

                WarehouseBatchInventoryQueryDTO queryDTO = new WarehouseBatchInventoryQueryDTO();
                queryDTO.setSpecAndOwnerIds(Collections.singletonList(specAndOwnerId));
                queryDTO.setWarehouseId(warehouseId);
                queryDTO.setPeriodConfigTypes(itemMessage.getConfigTypes());
                queryDTO.setExistProductionDateFlag(true);

                queryDTOList.add(queryDTO);
            });
        });

        // 按 warehouseId 分组处理
        Map<Integer, List<WarehouseBatchInventoryQueryDTO>> groupedByWarehouse =
            queryDTOList.stream().collect(Collectors.groupingBy(WarehouseBatchInventoryQueryDTO::getWarehouseId));
        groupedByWarehouse.forEach((warehouseId, queryDTOS) -> {
            // 合并 specAndOwnerIds
            List<SpecIdAndOwnerIdDTO> mergedSpecAndOwnerIds =
                queryDTOS.stream().flatMap(dto -> dto.getSpecAndOwnerIds().stream()).collect(Collectors.toList());

            List<Byte> configTypes = queryDTOS.stream().findFirst().get().getPeriodConfigTypes();
            WarehouseBatchInventoryQueryDTO mergedQueryDTO = new WarehouseBatchInventoryQueryDTO();
            mergedQueryDTO.setWarehouseId(warehouseId);
            mergedQueryDTO.setPeriodConfigTypes(configTypes);
            mergedQueryDTO.setExistProductionDateFlag(true);
            mergedQueryDTO.setSpecAndOwnerIds(mergedSpecAndOwnerIds);

            PageList<WarehouseBatchInventoryReportDTO> pageList =
                warehouseInventoryReportQueryService.pageListProductStoreBatch(mergedQueryDTO);
            if (CollectionUtils.isEmpty(pageList.getDataList())) {
                LOGGER.warn("根据交易促销活动创建促销批次库存,符合库存不存在：{}", JSON.toJSONString(mergedQueryDTO));
                return;
            }

            List<String> storeBatchIds = pageList.getDataList().stream().map(WarehouseBatchInventoryReportDTO::getId)
                .filter(StringUtils::hasText).distinct().collect(Collectors.toList());
            ProductPromotionStoreBatchDTO addDTO = new ProductPromotionStoreBatchDTO();
            addDTO.setWarehouseId(warehouseId);
            addDTO.setStoreBatchIds(storeBatchIds);
            addDTO.setAddLocationTodoTask(true);
            // 根据批次库存批量新增促销记录
            batchInsertByStoreBatchIds(addDTO);
            LOGGER.info("根据交易促销活动创建促销批次库存入完成");
        });
    }

    /**
     * 获取存在促销产品批次库存信息
     *
     * @return
     */
    public List<PromotionStoreBatchResultDTO> listPromotionStoreBatchNoGroup(PromotionStoreBatchQueryDTO queryDTO) {
        LOGGER.info("获取存在促销产品批次库存信息入参:{}", JSON.toJSONString(queryDTO));
        List<PromotionStoreBatchResultDTO> resultDTOS =
            promotionStoreBatchMapper.listPromotionStoreBatchNoGroup(queryDTO);
        LOGGER.info("获取存在促销产品批次库存信息结果:{}", JSON.toJSONString(resultDTOS));
        return resultDTOS;
    }

    /**
     * 获取存在混合库存的促销产品批次库存信息
     *
     * @return
     */
    private List<ProductPromotionStoreBatchDTO> getMixedBatch(List<ProductPromotionStoreBatchDTO> storeBatchDTOS) {
        if (CollectionUtils.isEmpty(storeBatchDTOS)) {
            return Collections.emptyList();
        }
        Integer warehouseId = storeBatchDTOS.stream().findFirst().get().getWarehouseId();
        List<Long> skuIds = storeBatchDTOS.stream().map(ProductPromotionStoreBatchDTO::getSkuId).distinct()
            .collect(Collectors.toList());

        PromotionStoreBatchQueryDTO queryDTO = new PromotionStoreBatchQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setSkuIdList(skuIds);
        List<Long> mixedSkuIds = listProductMixedBatchFlag(queryDTO).stream()
            .filter(p -> Objects.equals(p.getIsMixedBatch(), YesOrNoEnum.YES.getValue().byteValue()))
            .map(p -> p.getSkuId()).distinct().collect(Collectors.toList());
        LOGGER.info("促销生成产品关联货位任务 混合库存skuIds:{}", JSON.toJSONString(storeBatchDTOS));
        if (CollectionUtils.isEmpty(mixedSkuIds)) {
            return Collections.emptyList();
        }

        List<ProductPromotionStoreBatchDTO> mixedDTOS =
            storeBatchDTOS.stream().filter(p -> mixedSkuIds.contains(p.getSkuId())).collect(Collectors.toList());
        LOGGER.info("促销生成产品关联货位任务 混合库存过滤结果：{}", JSON.toJSONString(mixedDTOS));
        return mixedDTOS;
    }
}
