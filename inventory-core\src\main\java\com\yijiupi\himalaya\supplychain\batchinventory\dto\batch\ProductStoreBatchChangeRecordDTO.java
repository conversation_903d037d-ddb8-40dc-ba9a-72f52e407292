package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 批次库存变更记录
 *
 * <AUTHOR>
 * @date 2018/12/14 15:38
 */
public class ProductStoreBatchChangeRecordDTO implements Serializable {
    private static final long serialVersionUID = -6659026105008385376L;

    /**
     * 主键
     */
    private String id;

    /**
     * 关联库存表Id
     */
    private String storeId;

    /**
     * 关联批次库存表Id
     */
    private String storeBatchId;

    /**
     * 关联库存变更表记录Id
     */
    private String changeRecordId;

    /**
     * 批次库存变化的总数量
     */
    private BigDecimal changeCount;

    /**
     * 批次库存变化的大数量
     */
    private BigDecimal changePackageCount;

    /**
     * 批次库存变化的小数量
     */
    private BigDecimal changeUnitCount;

    /**
     * 变更时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 货位名称
     */
    private String locationName;

    /**
     * 批次号
     */
    private String batchInfoNo;

    /**
     * 包装规格转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 单据id
     */
    private String orderId;

    /**
     * 单号
     */
    private String orderNo;

    /**
     * 单据类型
     */
    private Integer orderType;

    /**
     * 单据类型名称
     */
    private String orderTypeName;

    /**
     * 酒批事件类型
     */
    private Integer jiupiEventType;

    /**
     * 酒批事件类型名称
     */
    private String jiupiEventTypeName;

    /**
     * erp事件类型
     */
    private Integer erpEventType;

    /**
     * 描述
     */
    private String description;

    /**
     * 操作人
     */
    private String createUser;

    /**
     * 批次时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;
    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 货位/货区
     */
    private Integer locationCategory;

    /**
     * 货位类别
     */
    private Integer locationSubcategory;

    /**
     * 货位类别名称
     */
    private String locationSubcategoryName;

    /**
     * 货位id
     */
    private Long locationId;

    private Long ownerId;

    private Long secOwnerId;

    private Long productSpecificationId;

    /**
     * 分摊剩余临时数量
     */
    private BigDecimal changeTmpCount;

    public BigDecimal getChangeTmpCount() {
        return changeTmpCount;
    }

    public void setChangeTmpCount(BigDecimal changeTmpCount) {
        this.changeTmpCount = changeTmpCount;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public String getLocationSubcategoryName() {
        return locationSubcategoryName;
    }

    public void setLocationSubcategoryName(String locationSubcategoryName) {
        this.locationSubcategoryName = locationSubcategoryName;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Integer getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    public Integer getLocationSubcategory() {
        return locationSubcategory;
    }

    public void setLocationSubcategory(Integer locationSubcategory) {
        this.locationSubcategory = locationSubcategory;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStoreBatchId() {
        return storeBatchId;
    }

    public void setStoreBatchId(String storeBatchId) {
        this.storeBatchId = storeBatchId;
    }

    public String getChangeRecordId() {
        return changeRecordId;
    }

    public void setChangeRecordId(String changeRecordId) {
        this.changeRecordId = changeRecordId;
    }

    public BigDecimal getChangeCount() {
        return changeCount;
    }

    public void setChangeCount(BigDecimal changeCount) {
        this.changeCount = changeCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public BigDecimal getChangePackageCount() {
        if (getChangeCount() != null && getChangeCount().compareTo(BigDecimal.ZERO) != 0 && getPackageQuantity() != null
            && getPackageQuantity().compareTo(BigDecimal.ZERO) != 0) {
            return getChangeCount().divideAndRemainder(getPackageQuantity())[0];
        } else {
            return BigDecimal.ZERO;
        }
    }

    public void setChangePackageCount(BigDecimal changePackageCount) {
        this.changePackageCount = changePackageCount;
    }

    public BigDecimal getChangeUnitCount() {
        if (getChangeCount() != null && getChangeCount().compareTo(BigDecimal.ZERO) != 0 && getPackageQuantity() != null
            && getPackageQuantity().compareTo(BigDecimal.ZERO) != 0) {
            return getChangeCount().divideAndRemainder(getPackageQuantity())[1];
        } else {
            return BigDecimal.ZERO;
        }
    }

    public void setChangeUnitCount(BigDecimal changeUnitCount) {
        this.changeUnitCount = changeUnitCount;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderTypeName() {
        return orderTypeName;
    }

    public void setOrderTypeName(String orderTypeName) {
        this.orderTypeName = orderTypeName;
    }

    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    public String getJiupiEventTypeName() {
        return jiupiEventTypeName;
    }

    public void setJiupiEventTypeName(String jiupiEventTypeName) {
        this.jiupiEventTypeName = jiupiEventTypeName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBatchInfoNo() {
        return batchInfoNo;
    }

    public void setBatchInfoNo(String batchInfoNo) {
        this.batchInfoNo = batchInfoNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getErpEventType() {
        return erpEventType;
    }

    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }
}
