package com.yijiupi.himalaya.supplychain.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 校正库存状态 0 不需要处理 1 未处理 2 已处理
 *
 * <AUTHOR>
 * @date 2018/1/10 11:50
 */
public enum CheckStateEnum {
    /**
     * 不需要处理
     */
    不需要处理((byte)0),
    /**
     * 未处理
     */
    未处理((byte)1),
    /**
     * 已处理
     */
    已处理((byte)2);

    private byte type;

    CheckStateEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    public void setType(byte type) {
        this.type = type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnmuName(Byte value) {
        String name = null;
        if (value != null) {
            name = cache.get(value);
        }
        return name;
    }

    private static Map<Byte, String> cache =
        EnumSet.allOf(CheckStateEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p.name()));

}
