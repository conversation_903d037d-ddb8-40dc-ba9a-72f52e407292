package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import com.yijiupi.himalaya.base.search.PageCondition;

import java.util.List;

/**
 * 批属性模板管理(新增)
 *
 * <AUTHOR> 2018/4/9
 */
public class BatchAttributeTemplateDTO extends PageCondition {
    /**
     * id
     */
    private Long id;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 模板所含属性(所含字典的主键id集合)
     */
    private List<BatchAttributeTemplateRelationDTO> relationList;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     */
    private Boolean enable;
    /**
     * 创建人
     */
    private String createUser;

    /**
     * 获取 模板名称
     */
    public String getTemplateName() {
        return this.templateName;
    }

    /**
     * 设置 模板名称
     */
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    /**
     * 获取 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取 状态
     */
    public Boolean getEnable() {
        return this.enable;
    }

    /**
     * 设置 状态
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 模板所含属性(所含字典的主键id集合)
     */
    public List<BatchAttributeTemplateRelationDTO> getRelationList() {
        return this.relationList;
    }

    /**
     * 设置 模板所含属性(所含字典的主键id集合)
     */
    public void setRelationList(List<BatchAttributeTemplateRelationDTO> relationList) {
        this.relationList = relationList;
    }

    /**
     * 获取 id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 id
     */
    public void setId(Long id) {
        this.id = id;
    }
}
