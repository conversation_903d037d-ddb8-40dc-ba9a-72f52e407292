package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.locationinventory.ProcessLocationInventoryBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event.DefectiveProductPriceEvent;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.inventory.ProductStoreMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.LocationCapacityPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.WarehouseInventoryTransferPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.LocationCapacityDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.ProductPromotionStoreBatchDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.util.UUIDUtil;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPType;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStockAgeStrategyService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 批次库存扣减
 *
 * <AUTHOR> 2018/1/25
 */
@Service
public class ProductStoreBatchBL {
    private final static Logger LOGGER = LoggerFactory.getLogger(ProductStoreBatchBL.class);

    @Autowired
    private ProductStoreMapper productStoreMapper;

    @Autowired
    private BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;
    @Autowired
    private ProductStoreBatchChangeRecordBL productStoreBatchChangeRecordBL;
    @Reference
    private IProductLocationService iProductLocationService;
    @Autowired
    private BatchInventoryManageBL batchInventoryManageBL;
    @Reference
    private LocationAreaService locationAreaService;

    @Reference
    private IStockAgeStrategyService iStockAgeStrategyService;

    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Autowired
    private DefectiveProductPriceEvent defectiveProductPriceEvent;

    @Autowired
    private PromotionStoreBatchBL promotionStoreBatchBL;

    @Reference
    private ILocationService iLocationService;

    // 1、如果为ERP事件
    // 如果能找到上一条批次库存变更历史
    // 按找到的批次库存历史，进行处理（依据之前扣减记录来）
    // 如果不存在批次库存历史
    // 查找SKU对应库存批次
    // 如果找不到，且数量大于0，创建批次。
    // 否则（扣减库存）
    // 如果批次存在，按入库批次进行相应的加减操作。（ERP可以扣成负的）。
    // 如果批次不存在，走订单流程。
    // （批次库存必须大于0才能操作，按入库批次时间从早到晚扣，如果找不到大于0的，按入库时间，扣最后一条，扣成负的）

    // 扣减批次库存
    // 如果不存在大于0的
    // 找最后一条批次库存（可以扣成负的）
    // 找不到批次库存，加一条为0的批次，然后再扣成负的。

    // // 1.如果为ERP事件，创建或者处理对应批次
    // @Transactional
    // public void processERPBatchInventory(ProductInventoryChangeRecordPO productInventoryChangeRecordPO) {
    // List<ProductStoreBatchPO> productStoreBatchPOS = new ArrayList<>();
    // List<ProductStoreBatchChangeRecordPO> productStoreBatchChangeRecordPOS = productStoreBatchChangeRecordBL.
    // selectBatchInventoryByChangeRecordId(productInventoryChangeRecordPO.getProductStoreId(),
    // productInventoryChangeRecordPO.getOrderNo());
    // if (CollectionUtils.isNotEmpty(productStoreBatchChangeRecordPOS)) {
    // //如果能找到对应批次库存变更历史
    // productStoreBatchPOS = processProductStoreBatchByOldBatchRecord(productInventoryChangeRecordPO,
    // productStoreBatchChangeRecordPOS);
    // } else {
    // // 如果不存在
    // // 如果数量大于0 ，创建批次库存
    // // 如果数量小于0 ，转到订单处理逻辑（ERP可以扣成负的）。
    // if (productInventoryChangeRecordPO.getTotalCount() > 0) {
    // // 判断批次是否存在
    // productStoreBatchPOS = processERPBatchCheckExits(productInventoryChangeRecordPO);
    // }
    // // 正常流程，加减批次库存
    // if (productStoreBatchPOS.size() <= 0) {
    // productStoreBatchPOS = processProductStoreBatchByChangeRecord(productInventoryChangeRecordPO, true);
    // }
    // }
    // //更新批次库存
    // if
    // (productStoreBatchPOS.stream().filter(ProductStoreBatchPO::isNeedUpdateBatchInventory).collect(Collectors.toList()).size()
    // > 0) {
    // productStoreBatchMapper.updateBatchInventory(productStoreBatchPOS);
    // }
    // //创建批次库存变更历史记录
    // productStoreBatchChangeRecordBL.createStoreBatchChangeRecord(productInventoryChangeRecordPO,
    // productStoreBatchPOS);
    // }

    // 2、批次库存扣减 如果为订单处理事件，处理相应的批次库存扣减或者增加事件
    //
    // 按找到的批次库存历史，进行处理（依据之前扣减记录来）
    // 如果不存在批次库存变更历史，则按入库日期进行相应的加减操作。
    // 查找SKU对应库存批次
    // 如果能找到，进行加减操作（ERP可以扣成负的）。
    // （批次库存必须大于0才能操作，按入库批次时间从早到晚扣，如果找不到大于0的，按入库时间，扣最后一条，扣成负的）

    // //创建批次库存变更历史
    // @Transactional
    // public void processOrderBatchInventory(ProductInventoryChangeRecordPO productInventoryChangeRecordPO) {
    //
    // List<ProductStoreBatchChangeRecordPO> productStoreBatchChangeRecordPOS = productStoreBatchChangeRecordBL.
    // selectBatchInventoryByChangeRecordId(productInventoryChangeRecordPO.getProductStoreId(),
    // productInventoryChangeRecordPO.getOrderNo());
    // List<ProductStoreBatchPO> productStoreBatchPOS = new ArrayList<>();
    // if (CollectionUtils.isNotEmpty(productStoreBatchChangeRecordPOS)) {
    // //如果已经存在批次库存历史,按找到的批次库存历史，进行处理（依据之前扣减记录来）
    // productStoreBatchPOS = processProductStoreBatchByOldBatchRecord(productInventoryChangeRecordPO,
    // productStoreBatchChangeRecordPOS);
    // } else {
    // //扣减批次库存
    // productStoreBatchPOS = processProductStoreBatchByChangeRecord(productInventoryChangeRecordPO, true);
    // }
    // //更新批次库存
    // if
    // (productStoreBatchPOS.stream().filter(ProductStoreBatchPO::isNeedUpdateBatchInventory).collect(Collectors.toList()).size()
    // > 0) {
    // productStoreBatchMapper.updateBatchInventory(productStoreBatchPOS);
    // }
    // //批量去操作批次库存记录表
    // productStoreBatchChangeRecordBL.createStoreBatchChangeRecord(productInventoryChangeRecordPO,
    // productStoreBatchPOS);
    // }

    // /**
    // * 3.处理库存-手动修改
    // */
    // @Transactional
    // public void processManualBatchInventory(ProductInventoryChangeRecordPO productInventoryChangeRecordPO) {
    // //手动修改，根据storeid查找最后一个库存批次
    // List<ProductStoreBatchPO> productStoreBatchPOS =
    // processProductStoreBatchByChangeRecord(productInventoryChangeRecordPO, false);
    // //更新批次库存
    // if
    // (productStoreBatchPOS.stream().filter(ProductStoreBatchPO::isNeedUpdateBatchInventory).collect(Collectors.toList()).size()
    // > 0) {
    // productStoreBatchMapper.updateBatchInventory(productStoreBatchPOS);
    // }
    // //创建库存变更历史
    // productStoreBatchChangeRecordBL.createStoreBatchChangeRecord(productInventoryChangeRecordPO,
    // productStoreBatchPOS);
    // }

    // /**
    // * 4.处理周转区批次库存
    // *
    // * @param productInventoryChangeRecordPO
    // */
    // @Transactional
    // public void processBHQInventory(ProductInventoryChangeRecordPO productInventoryChangeRecordPO) {
    // if (productInventoryChangeRecordPO.getTotalCount() != 0) {
    // List<Integer> lstExtAreaSubCategory = new ArrayList<Integer>() {{
    // add(LocationAreaEnum.周转区.getType());
    // add(LocationAreaEnum.退货区.getType());
    // add(LocationAreaEnum.暂存区.getType());
    // add(LocationAreaEnum.待检区.getType());
    // }};
    // processLocationInventory(productInventoryChangeRecordPO, null, lstExtAreaSubCategory);
    // }
    // }

    /**
     * 5.入库到指定货区
     *
     * @param productInventoryChangeRecordPO
     * @see ProcessLocationInventoryBL
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Deprecated
    public void processLocationInventory(ProductInventoryChangeRecordPO productInventoryChangeRecordPO,
                                         Integer locationType, List<Integer> exSubcategory, boolean isRandom, Boolean isAdvent) {
        BigDecimal changeCount = productInventoryChangeRecordPO.getTotalCount();
        // 是否开启货位组
        boolean isOpenLocationStockGroup =
                warehouseConfigService.isOpenLocationGroup(productInventoryChangeRecordPO.getWarehouseId());
        // 如果待处理数量等于0，不需要处理
        if (changeCount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        if (productInventoryChangeRecordPO.getProductSkuId() == null) {
            WarehouseInventoryTransferPO skuIdByStoreId =
                    productStoreMapper.findSkuIdByStoreId(productInventoryChangeRecordPO.getProductStoreId());
            if (skuIdByStoreId != null) {
                productInventoryChangeRecordPO.setProductSkuId(skuIdByStoreId.getProductSkuId());
            }
        }

        // 如果是盘点单据：盘盈单据则根据库存ID查询对应产品是否库龄管控产品,如果是库龄产品则查询批次是需要带上批次时间
        Boolean storeAgeControlProduct = null;
        if (Objects.equals(ERPType.库存盘点单.getType(), productInventoryChangeRecordPO.getOrderType())
                && changeCount.compareTo(BigDecimal.ZERO) > 0) {
            ProductInventoryPO inventoryPO =
                    productStoreMapper.findInventoryPO(productInventoryChangeRecordPO.getProductStoreId());
            if (inventoryPO != null) {
                storeAgeControlProduct = iStockAgeStrategyService.isProductStockAgeControlsBySpec(
                        inventoryPO.getCityId(), inventoryPO.getWarehouseId(), inventoryPO.getProductSpecificationId(),
                        inventoryPO.getOwnerId(), inventoryPO.getSecOwnerId());
            }
        }
        // 设置是否库龄管控产品
        productInventoryChangeRecordPO.setStoreAgeControlProduct(storeAgeControlProduct);
        String batchAttributeInfoNo = productInventoryChangeRecordPO.getBatchAttributeInfoNo();
        List<ProductStoreBatchPO> productStoreBatchPOS =
                getProductStoreBatchPOS(productInventoryChangeRecordPO, locationType, exSubcategory);

        if (isGetDefectiveProductStoreBatch(productInventoryChangeRecordPO, productStoreBatchPOS, exSubcategory,
                isRandom)) {
            exSubcategory = null;
            productStoreBatchPOS = getProductStoreBatchPOS(productInventoryChangeRecordPO, locationType, exSubcategory);
            LOGGER.info(String.format("无排除货区开始处理批次库存变更 指定货区：%s,指定货位:%s,查询库存结果：%s", locationType,
                    productInventoryChangeRecordPO.getLocationId(), JSON.toJSONString(productStoreBatchPOS)));
        }

        // 出库是否允许分配指定货位以外库存
        if (isGetOtherLocationProductStoreBatch(productInventoryChangeRecordPO, productStoreBatchPOS, locationType,
                exSubcategory, isRandom)) {
            if (CollectionUtils.isEmpty(exSubcategory)) {
                exSubcategory = new ArrayList<>();
            }
            exSubcategory.add(locationType);
            locationType = null;
            List<ProductStoreBatchPO> otherLocationProductStoreBatch =
                    getProductStoreBatchPOS(productInventoryChangeRecordPO, locationType, exSubcategory);
            LOGGER.info(String.format("指定货区无库存，去除指定货区开始处理批次库存变更 指定货区：%s,指定货位:%s,排除货区:%s,查询库存结果：%s", locationType,
                    productInventoryChangeRecordPO.getLocationId(), exSubcategory,
                    JSON.toJSONString(otherLocationProductStoreBatch)));
            if (CollectionUtils.isNotEmpty(otherLocationProductStoreBatch)) {
                productStoreBatchPOS.addAll(otherLocationProductStoreBatch);
            }
        }

        productStoreBatchPOS.forEach(n -> {
            if (n.getBatchAttributeInfoNo() == null) {
                n.setBatchAttributeInfoNo(batchAttributeInfoNo);
            }
        });

        LOGGER.info(String.format("开始处理批次库存变更 指定货区：%s,指定货位:%s,排除货区：%s,变更消息：%s,查询库存结果：%s", locationType,
                productInventoryChangeRecordPO.getLocationId(), JSON.toJSONString(exSubcategory),
                JSON.toJSONString(productInventoryChangeRecordPO), JSON.toJSONString(productStoreBatchPOS)));

        if (isRandom && isOpenLocationStockGroup && changeCount.compareTo(BigDecimal.ZERO) > 0) {
            // 如果开通货位组 加库存 清除货位信息,保留货区信息
            productStoreBatchPOS = productStoreBatchPOS.stream()
                    .filter(productStoreBatchPO -> Objects.equals(productStoreBatchPO.getLocationCategory(), 1))
                    .collect(Collectors.toList());
            LOGGER.info("开通货位组加库存，清除货位信息,保留货区信息{}", JSON.toJSONString(productStoreBatchPOS));
        }

        // 入库过滤为数量小于等于0的批次库存
        if (CollectionUtils.isNotEmpty(productStoreBatchPOS)
                && productInventoryChangeRecordPO.getTotalCount().compareTo(BigDecimal.ZERO) > 0){
            Predicate<ProductStoreBatchPO> productStoreBatchPOPredicate = (input -> {
                if (input.getTotalCount().compareTo(BigDecimal.ZERO) <= 0) {
                    return false;
                }
                return true;
            });
            productStoreBatchPOS = productStoreBatchPOS.stream().filter(productStoreBatchPOPredicate).collect(Collectors.toList());
            LOGGER.info("入库过滤为数量小于等于0的批次库存结果：{}", JSON.toJSONString(productStoreBatchPOS));
        }

        // 如果找不到,通过storeid查询仓库id,通过仓库id查询默认暂存位..新增一条批次库存记录
        if (productStoreBatchPOS == null || productStoreBatchPOS.isEmpty()) {
            LOGGER.info("单据[{}]编号[{}]没有找到对应批次库存信息，新增一条批次记录！", productInventoryChangeRecordPO.getOrderId(),
                    productInventoryChangeRecordPO.getOrderNo());
            createProductStoreBatchIfNotExits(productInventoryChangeRecordPO, locationType, changeCount,
                    batchAttributeInfoNo);
            changeCount = BigDecimal.ZERO;
        } else {
            List<ProductStoreBatchPO> lstUpdatePO = new ArrayList<>();

            List<ProductStoreBatchPO> lstProcessProductStores = new ArrayList<>();
            // 出库扣库存，随机，忽略生产日期属性
            if (isRandom) {
                // 如果加库存，优先清空负库存
                if (changeCount.compareTo(BigDecimal.ZERO) > 0) {
                    if (productStoreBatchPOS.stream()
                            .anyMatch(p -> p.getTotalCount() != null && p.getTotalCount().compareTo(BigDecimal.ZERO) < 0)) {
                        lstProcessProductStores = productStoreBatchPOS.stream()
                                .filter(p -> p.getTotalCount() != null && p.getTotalCount().compareTo(BigDecimal.ZERO) < 0)
                                .sorted(Comparator.comparing(ProductStoreBatchPO::getTotalCount).reversed())
                                .collect(Collectors.toList());
                    }
                } else {
                    // 如果减库存，优先清空正库存
                    if (productStoreBatchPOS.stream()
                            .anyMatch(p -> p.getTotalCount() != null && p.getTotalCount().compareTo(BigDecimal.ZERO) > 0)) {
                        lstProcessProductStores = productStoreBatchPOS.stream()
                                .filter(p -> p.getTotalCount() != null && p.getTotalCount().compareTo(BigDecimal.ZERO) > 0)
                                .sorted(Comparator.comparing(ProductStoreBatchPO::getTotalCount))
                                .collect(Collectors.toList());
                    }
                }
                List<String> lstLocations = lstProcessProductStores.stream()
                        .map(p -> String.format("pid:%s,sid:%s,productDate:%s,location:%s-%s,batchStore:%s", p.getId(),
                                p.getProductStoreId(), p.getProductionDate(), p.getLocationId(), p.getLocationName(),
                                p.getTotalCount()))
                        .distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(lstLocations)) {
                    LOGGER.info(String.format("随机-优先处理货位集合：%s", JSON.toJSONString(lstLocations)));
                }
                // end 随机扣库存
            } else {
                // 如果扣除数量小于0，优先生产日期匹配的，剩余的：优先扣生产日期为空或者生产日期临期的
                if (productInventoryChangeRecordPO.getProductionDate() != null) {
                    lstProcessProductStores = productStoreBatchPOS.stream().filter(
                                    p -> Objects.equals(p.getProductionDate(), productInventoryChangeRecordPO.getProductionDate()))
                            .collect(Collectors.toList());
                }
                // end 非随机扣库存
            }
            // 找不到优先处理的，直接处理全部
            if (CollectionUtils.isEmpty(lstProcessProductStores)) {
                lstProcessProductStores = productStoreBatchPOS;
            }
            if (CollectionUtils.isNotEmpty(lstProcessProductStores)) {
                lstProcessProductStores =
                        getProductStoreBatchOrderByProductionDateAndBatchDate(lstProcessProductStores);

                //默认优先正品，其次临期
                Comparator<ProductStoreBatchPO> fieldAComparator = Comparator.comparing(ProductStoreBatchPO::getAdventId);

                // 如果单子包含临期产品，则优先临期
                if (isAdvent != null && isAdvent) {
                    fieldAComparator = fieldAComparator.reversed();
                }

                lstProcessProductStores.sort(fieldAComparator);

                processForHasBatchInventoryNew(lstProcessProductStores, changeCount, lstUpdatePO);
            }

            if (lstUpdatePO.size() > 0) {
                // 修改批次库存信息
                batchInventoryProductStoreBatchMapper.updateBatchInventory(lstUpdatePO);

                // 新增批次库存变更记录
                productStoreBatchChangeRecordBL.createStoreBatchChangeRecord(productInventoryChangeRecordPO.getId(),
                        lstUpdatePO);

                lstUpdatePO.forEach(updatePO -> {
                    updatePO.setProductSkuId(productInventoryChangeRecordPO.getProductSkuId());
                });
                defectiveProductPriceEvent.updateDefectiveProductPrice(productInventoryChangeRecordPO.getCityId(),
                        productInventoryChangeRecordPO.getWarehouseId(), lstUpdatePO);
            }
        }

        productInventoryChangeRecordPO.setTotalCount(changeCount);
        // 删除促销批次库存
        deleteByPromotionStoreBatch(productInventoryChangeRecordPO);
    }

    /**
     * 批次库存排序规则-（生产日期+批次时间） 1、生产日期（为空的排最前） 2、批次时间（为空的排最前）
     *
     * @param productStoreBatchPOS
     * @return
     */
    private List<ProductStoreBatchPO>
    getProductStoreBatchOrderByProductionDateAndBatchDate(List<ProductStoreBatchPO> productStoreBatchPOS) {
        List<ProductStoreBatchPO> lstAllProductStores = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productStoreBatchPOS)) {
            List<Date> lstAllProductionDates = new ArrayList<>();
            lstAllProductionDates.add(null);
            List<Date> lstTmpProductionDates = productStoreBatchPOS.stream().filter(p -> p.getProductionDate() != null)
                    .map(p -> p.getProductionDate()).distinct().sorted().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lstTmpProductionDates)) {
                lstAllProductionDates.addAll(lstTmpProductionDates);
            }

            for (Date dtProducttionDate : lstAllProductionDates) {
                List<ProductStoreBatchPO> lstTmp =
                        getProductStoreBatchPOsOrderByBatchTime(productStoreBatchPOS, dtProducttionDate);
                if (CollectionUtils.isNotEmpty(lstTmp)) {
                    lstAllProductStores.addAll(lstTmp);
                }
            }
            if (lstTmpProductionDates.size() > 1) {
                LOGGER.info(String.format("存在多个生产日期！生产日期：%s，批次库存排序结果：%s", JSON.toJSONString(lstAllProductionDates),
                        JSON.toJSONString(lstAllProductStores)));
            }
        }
        return lstAllProductStores;
    }

    /**
     * 同一生产日期的批次库存排序规则 批次时间（为空的排最前）
     *
     * @param productStoreBatchPOS
     * @return
     */
    private List<ProductStoreBatchPO> getProductStoreBatchPOsOrderByBatchTime(
            List<ProductStoreBatchPO> productStoreBatchPOS, Date dtProducttionDate) {
        List<ProductStoreBatchPO> lstTmp = productStoreBatchPOS.stream()
                .filter(p -> Objects.equals(dtProducttionDate, p.getProductionDate())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lstTmp)) {
            // 批次为空的数据
            List<ProductStoreBatchPO> lstNoBatchDate =
                    lstTmp.stream().filter(p -> p.getBatchTime() == null).collect(Collectors.toList());
            // 优先扣批次日期为空或者批次临期的
            List<ProductStoreBatchPO> lstHasBatchDate = lstTmp.stream().filter(p -> p.getBatchTime() != null)
                    .sorted(Comparator.nullsFirst(Comparator.comparing(ProductStoreBatchPO::getBatchTime)))
                    .collect(Collectors.toList());
            lstTmp = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(lstNoBatchDate)) {
                lstTmp.addAll(lstNoBatchDate);
            }
            if (CollectionUtils.isNotEmpty(lstHasBatchDate)) {
                lstTmp.addAll(lstHasBatchDate);
            }
        }
        return lstTmp;
    }

    private void createProductStoreBatchIfNotExits(ProductInventoryChangeRecordPO productInventoryChangeRecordPO,
                                                   Integer locationType, BigDecimal changeCount, String batchAttributeInfoNo) {
        LocationReturnDTO locationReturnDTO = null;
        Long locationId = productInventoryChangeRecordPO.getLocationId();
        // 如果有多个出库货位，随机取第一个
        if (locationId == null && CollectionUtils.isNotEmpty(productInventoryChangeRecordPO.getLocationIds())) {
            locationId = productInventoryChangeRecordPO.getLocationIds().get(0);
        }
        if (locationId != null) {
            locationReturnDTO = locationAreaService.findLocationById(String.valueOf(locationId));
        } else {
            Integer warehouseId = productInventoryChangeRecordPO.getWarehouseId();
            Integer cityId = productInventoryChangeRecordPO.getCityId();
            if (warehouseId == null) {
                warehouseId = batchInventoryProductStoreBatchMapper
                        .findWarehouseIdByStoreId(productInventoryChangeRecordPO.getProductStoreId());
            }
            if (cityId == null) {
                cityId =
                        batchInventoryProductStoreBatchMapper.findCityIdByStoreId(productInventoryChangeRecordPO.getProductStoreId());
            }
            AssertUtils.notNull(warehouseId,
                    "该库存记录仓库Id不存在! storeId : " + productInventoryChangeRecordPO.getProductStoreId());
            AssertUtils.notNull(cityId,
                    "该库存记录CityId不存在! storeId : " + productInventoryChangeRecordPO.getProductStoreId());
            // 根据仓库id+类型查询货区
            byte subcategory =
                    locationType == null ? LocationAreaEnum.存储区.getType().byteValue() : locationType.byteValue();
            locationReturnDTO =
                    batchInventoryManageBL.getLoactionByWarehouseId(warehouseId, cityId, subcategory, changeCount);
        }
        AssertUtils.notNull(locationReturnDTO,
                "货位信息不存在！ID：" + locationId + "，名称：" + productInventoryChangeRecordPO.getLocationName());
        ProductStoreBatchPO productStoreBatchPO =
                ProductInventoryChangeRecordPO2ProductStoreBatchPO(productInventoryChangeRecordPO);
        productStoreBatchPO.setId(UUIDUtil.getUUID().replaceAll("-", ""));
        productStoreBatchPO.setLocationId(locationReturnDTO.getId());
        productStoreBatchPO.setLocationName(locationReturnDTO.getName());
        productStoreBatchPO.setSubcategory(Integer.valueOf(locationReturnDTO.getSubcategory()));
        productStoreBatchPO.setBatchAttributeInfoNo(batchAttributeInfoNo);
        batchInventoryProductStoreBatchMapper.addBatchInventory(productStoreBatchPO);
        // 新增批次库存变更记录
        productStoreBatchChangeRecordBL.createStoreBatchChangeRecord(productInventoryChangeRecordPO.getId(),
                Arrays.asList(productStoreBatchPO));

        productStoreBatchPO.setProductSkuId(productInventoryChangeRecordPO.getProductSkuId());

        defectiveProductPriceEvent.updateDefectiveProductPrice(productInventoryChangeRecordPO.getCityId(),
                productInventoryChangeRecordPO.getWarehouseId(), Collections.singletonList(productStoreBatchPO));
    }

    private BigDecimal processForHasBatchInventory(List<ProductStoreBatchPO> productStoreBatchPOS,
                                                   BigDecimal changeCount, List<ProductStoreBatchPO> lstUpdatePO) {
        for (int i = 0; i < productStoreBatchPOS.size(); i++) {
            if (changeCount.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            ProductStoreBatchPO processPO = productStoreBatchPOS.get(i);
            BigDecimal tmpCount = BigDecimal.ZERO;

            if (changeCount.compareTo(BigDecimal.ZERO) > 0) {
                // if (processPO.getLocationId() != null) {
                // LocationCapacityPO locationCapacityPO = lstLocationCapacitys.stream().filter(p -> p.getLocation_id()
                // != null && p.getLocation_id().equals(processPO.getLocationId())).findAny().get();
                // //正数，直接加库存
                // if (locationCapacityPO != null) {
                // // 货位容量限制
                // tmpCount = Math.min(locationCapacityPO.getCapacity() - locationCapacityPO.getCount(), changeCount /
                // processPO.getPackageQuantity());
                // if (tmpCount < 0) {
                // //如果小于0，说明货位已经存放满了，不能往这个货位继续存放
                // tmpCount = 0;
                // LOGGER.info(String.format("货位库存已满：%s", locationCapacityPO));
                // } else {
                // Integer nowCapacity = tmpCount + locationCapacityPO.getCount();
                // LOGGER.info(String.format("货位库存容量：%s，当前已存：%s", locationCapacityPO, nowCapacity));
                // //如果能往这个货位存放产品，则货位已存放的容量加上本次存放的货位
                // locationCapacityPO.setCount(nowCapacity);
                // }
                // } else {
                // LOGGER.info(String.format("找不到货位：%s", processPO.getProductStoreId()));
                // }
                // }
                tmpCount = changeCount;
            } else {
                // 负数
                // 如果当前库位库存已经是负数，跳过，继续找其他货位的
                if (processPO.getTotalCount().compareTo(BigDecimal.ZERO) > 0) {
                    // 最多只能扣当前库存数量
                    BigDecimal minTmp = processPO.getTotalCount().abs().min(changeCount.abs());
                    tmpCount = minTmp.multiply(new BigDecimal(-1));
                }
            }

            // 如果是最后一条库存记录，无论正负，都处理到最后一条上
            if (i == productStoreBatchPOS.size() - 1) {
                tmpCount = changeCount;
            }
            if (tmpCount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            // LOGGER.info(String.format("当前操作项：%s,本次加减数量：%s,总数量:%s", JSON.toJSONString(processPO), tmpCount,
            // changeCount));
            changeCount = changeCount.subtract(tmpCount);
            processPO.setTotalCount(tmpCount);
            lstUpdatePO.add(processPO);
        }
        return changeCount;
    }

    private void processForHasBatchInventoryNew(List<ProductStoreBatchPO> productStoreBatchPOS,
                                                BigDecimal totalChangeCount, List<ProductStoreBatchPO> lstUpdatePO) {
        // 转移的总数量
        BigDecimal transferTotalCount = totalChangeCount.multiply(new BigDecimal(-1));
        for (int i = 0; i < productStoreBatchPOS.size(); i++) {
            ProductStoreBatchPO processPO = productStoreBatchPOS.get(i);
            if (transferTotalCount.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }

            // 当前批次库存的总数量
            BigDecimal batchCount = processPO.getTotalCount();
            // 当前批次库存的转移数量
            BigDecimal changeCount;

            // 如果要转移的数量，大于批次库存的数量，本次只转移批次库存数量
            // 如果要转移的数量，小于等于批次库存数量，则全部转移
            // 正正
            // 正负
            // 负负
            // 负正
            if (transferTotalCount.compareTo(batchCount) > 0) {
                // 如果原库存小于0，本次新增库存，则优先补到0，剩余的轮训其他负库存货位
                if (transferTotalCount.compareTo(BigDecimal.ZERO) < 0 && batchCount.compareTo(BigDecimal.ZERO) < 0) {
                    changeCount = (transferTotalCount.abs().min(batchCount.abs())).multiply(new BigDecimal(-1));
                    LOGGER.info(String.format("[负库存补零]批次ID:%s,库存ID:%s,货位[%s-%s]库存:%s小件，补%s小件", processPO.getId(),
                            processPO.getProductStoreId(), processPO.getLocationId(), processPO.getLocationName(),
                            batchCount.stripTrailingZeros().toPlainString(),
                            changeCount.abs().stripTrailingZeros().toPlainString()));
                } else {
                    changeCount = batchCount;
                }
            } else {
                changeCount = transferTotalCount;
            }

            // 最后一个，且剩余待移库数量不等于0，如果库存不足，全部扣到最后一个货位上
            if (i == productStoreBatchPOS.size() - 1) {
                if (transferTotalCount.compareTo(batchCount) != 0) {
                    LOGGER.info(String.format("批次ID:%s,库存ID:%s,货位[%s-%s]库存:%s小件,缺%s小件，库存不足强制处理！", processPO.getId(),
                            processPO.getProductStoreId(), processPO.getLocationId(), processPO.getLocationName(),
                            batchCount.stripTrailingZeros().toPlainString(),
                            transferTotalCount.stripTrailingZeros().toPlainString()));
                    changeCount = transferTotalCount;
                }
            }
            if (changeCount.compareTo(BigDecimal.ZERO) != 0) {
                transferTotalCount = transferTotalCount.subtract(changeCount);
                processPO.setTotalCount(changeCount.multiply(new BigDecimal(-1)));
                lstUpdatePO.add(processPO);

                LOGGER.info(String.format("批次ID:%s,库存ID:%s,货位[%s-%s]库存:%s小件,变更数量：%s小件", processPO.getId(),
                        processPO.getProductStoreId(), processPO.getLocationId(), processPO.getLocationName(),
                        batchCount.stripTrailingZeros().toPlainString(), changeCount.stripTrailingZeros().toPlainString()));
            }
        }
    }

    private List<LocationCapacityPO> getLocationCapacityPOs(List<Long> lstLocationIds) {
        List<LocationCapacityPO> lstResult = batchInventoryProductStoreBatchMapper.getLocationCapacityPOs(lstLocationIds);
        return lstResult;
    }

    public List<LocationCapacityDTO> getLocationCapacityDTOs(List<Long> lstLocationIds) {
        List<LocationCapacityPO> lstResult = batchInventoryProductStoreBatchMapper.getLocationCapacityPOs(lstLocationIds);
        List<LocationCapacityDTO> locationCapacityDTOS = lstResult.stream().map(p -> {
            LocationCapacityDTO locationCapacityDTO = new LocationCapacityDTO();
            locationCapacityDTO.setCapacity(p.getCapacity());
            locationCapacityDTO.setLocation_id(p.getLocation_id());
            locationCapacityDTO.setCount(p.getCount());
            locationCapacityDTO.setAreaId(p.getAreaId());
            locationCapacityDTO.setAreaName(p.getAreaName());
            if (p.getAreaId() == null && p.getLocation_id() != null && p.getLocationCategory() != null
                    && p.getLocationCategory().intValue() == 1) {
                locationCapacityDTO.setAreaId(p.getLocation_id());
            }
            return locationCapacityDTO;
        }).collect(Collectors.toList());
        return locationCapacityDTOS;
    }

    private List<ProductStoreBatchPO> getProductStoreBatchPOS(
            ProductInventoryChangeRecordPO productInventoryChangeRecordPO, Integer locationType,
            List<Integer> exSubcategory) {
        List<ProductStoreBatchPO> batchLocationInfoDTOS = null;
        // productInventoryChangeRecordPO 中增加 productStoreBatchId(批次库存ID),
        // 如果批次库存ID存在则优先用它查询批次库存信息,不存在则按照原来的方式查
        if (StringUtils.isNotBlank(productInventoryChangeRecordPO.getProductStoreBatchId())) {
            ProductStoreBatchPO productStoreBatchPO = batchInventoryProductStoreBatchMapper
                    .findProductStoreBatchById(productInventoryChangeRecordPO.getProductStoreBatchId());
            if (productStoreBatchPO != null) {
                batchLocationInfoDTOS = Lists.newArrayList(productStoreBatchPO);
            }
        }
        if (batchLocationInfoDTOS == null || batchLocationInfoDTOS.isEmpty()) {
            // 如果集合为空,要么 productStoreBatchId(批次库存ID) 为空, 要么根据 productStoreBatchId(批次库存ID) 没找到数据则按默认方式查询数据
            if (locationType == null && CollectionUtils.isEmpty(exSubcategory)) {
                batchLocationInfoDTOS =
                        batchInventoryProductStoreBatchMapper.selectTmpLocationInventory(productInventoryChangeRecordPO);
            } else {
                batchLocationInfoDTOS = batchInventoryProductStoreBatchMapper.selectTmpLocationInventoryBySubCategory(
                        productInventoryChangeRecordPO, locationType, exSubcategory);
            }
        }
        return batchLocationInfoDTOS;
    }

    public static ProductStoreBatchPO
    ProductInventoryChangeRecordPO2ProductStoreBatchPO(ProductInventoryChangeRecordPO po) {
        if (po == null) {
            return null;
        }
        ProductStoreBatchPO productStoreBatchPO = new ProductStoreBatchPO();
        productStoreBatchPO.setProductStoreId(po.getProductStoreId());
        productStoreBatchPO.setTotalCount(po.getTotalCount());
        productStoreBatchPO.setProductionDate(po.getProductionDate());
        productStoreBatchPO.setExpireTime(po.getExpireTime());
        if (po.getTotalCount().compareTo(BigDecimal.ZERO) > 0) {
            productStoreBatchPO.setInStockOrderNo(po.getOrderNo());
            productStoreBatchPO.setBatchTime(new Date());
        }
        productStoreBatchPO.setBatchProperty(po.getBatchProperty());
        return productStoreBatchPO;
    }

    // //检查ERP批次是否存在，
    // // 如果不存在创建，
    // // 如果存在，按批次进行加减操作
    // private List<ProductStoreBatchPO> processERPBatchCheckExits(ProductInventoryChangeRecordPO
    // productInventoryChangeRecordPO) {
    // List<ProductStoreBatchPO> productStoreBatchPOS = new ArrayList<>();
    // ProductStoreBatchPO productStoreBatch =
    // productStoreBatchMapper.findProductStoreBatchByBatchId(productInventoryChangeRecordPO.getProductStoreId(),
    // productInventoryChangeRecordPO.getErpOrderKey());
    // if (productStoreBatch != null) {
    // //如果存在，走正常加减流程，按批次处理
    // productStoreBatchPOS = processByProductStoreBatch(productInventoryChangeRecordPO, productStoreBatch);
    // } else {
    // //如果不存在，创建批次库存
    // productStoreBatchPOS = processByNewBatch(productInventoryChangeRecordPO);
    // }
    // return productStoreBatchPOS;
    // }

    // //根据上次扣减库存的记录，处理当前库存
    // private ArrayList<ProductStoreBatchPO> processProductStoreBatchByOldBatchRecord(ProductInventoryChangeRecordPO
    // productInventoryChangeRecordPO, List<ProductStoreBatchChangeRecordPO> lstProductStoreBatchChangeRecord) {
    // ArrayList<ProductStoreBatchPO> productStoreBatchPOS = new ArrayList<>();
    //
    // Integer changeCount = productInventoryChangeRecordPO.getTotalCount();
    // //是否为正数
    // boolean isPositive = changeCount > 0;
    // boolean isRecordPositive = lstProductStoreBatchChangeRecord.get(0).getChangeCount() > 0;
    //
    // //判断历史记录是否为正
    // //如果历史记录为正
    // // 如果本次数量为正，全部加到最后一个批次上
    // // 如果本次数量为负，针对本条记录，本次可扣减数量为历史数量与本次数量绝对值的最小值
    // //如果历史记录为负
    // // 如果本次记录为正，本次可增加的数量为历史数量与本次数量绝对值的最小值
    // // 如果本次为负，全部扣到最后一个批次上
    //
    // //同正同负情况，直接处理最后一条库存
    // if ((isPositive && isRecordPositive) || (!isPositive && !isRecordPositive)) {
    // // 根据时间倒序，取最后一条记录
    // ProductStoreBatchChangeRecordPO lastBatchRecord =
    // lstProductStoreBatchChangeRecord.get(lstProductStoreBatchChangeRecord.size() - 1);
    // ProductStoreBatchPO productStoreBatchPO = new ProductStoreBatchPO();
    // productStoreBatchPO.setId(lastBatchRecord.getBatchId());
    // productStoreBatchPO.setTotalCount(productInventoryChangeRecordPO.getTotalCount());
    // productStoreBatchPOS.add(productStoreBatchPO);
    // } else {
    // //按批次实际数量进行扣减，不一定等于上次扣减的数量，有可能比上次扣减的数量少！！！
    // //先还老的批次，最后还新的批次
    // for (int i = 0; i < lstProductStoreBatchChangeRecord.size(); i++) {
    // if (changeCount == 0) {
    // break;
    // }
    // ProductStoreBatchChangeRecordPO productStoreBatchChangeRecordPO = lstProductStoreBatchChangeRecord.get(i);
    // ProductStoreBatchPO productStoreBatchPO = new ProductStoreBatchPO();
    // productStoreBatchPO.setId(productStoreBatchChangeRecordPO.getBatchId());
    // //如果历史记录为正，本次数量为负，本次可扣减数量为历史数量与本次数量绝对值的最小值
    // //如果历史记录为负，本次记录为正，本次可增加的数量为历史数量与本次数量绝对值的最小值
    // Integer tmpCount = (isPositive ? 1 : -1) * Math.min(Math.abs(productStoreBatchChangeRecordPO.getChangeCount()),
    // Math.abs(changeCount));
    //
    // //如果所有批次都已经大于0了，changecount还是大于0，补到这些批次的最新的一条批次上
    // //或者所有批次都已经小于0了，changecount还是小于0，补到这些批次的最新的一条批次上
    // if (changeCount != 0 && i == lstProductStoreBatchChangeRecord.size() - 1) {
    // tmpCount = changeCount;
    // }
    // changeCount -= tmpCount;
    // productStoreBatchPO.setTotalCount(tmpCount);
    // productStoreBatchPOS.add(productStoreBatchPO);
    // }
    // }
    // return productStoreBatchPOS;
    // }

    // // 订单正常处理流程（加减批次库存,创建批次变更记录)
    // // 3种情况：
    // // 1、查找批次库存大于0的，按入库日期正序扣减（如果最后一个批次库存不够，扣成负数）
    // // 2、如果不存在批次库存大于0的，找到最后一条记录，扣成负的
    // // 3、如果不存在批次库存，创建一条批次库存，进行扣减
    // private List<ProductStoreBatchPO> processProductStoreBatchByChangeRecord(ProductInventoryChangeRecordPO
    // productInventoryChangeRecordPO, boolean isProcessByAllBatch) {
    // List<ProductStoreBatchPO> productStoreBatchPOS = new ArrayList<>();
    // //手动处理库存，不需要处理批次库存，直接处理最后一条批次库存
    // if (isProcessByAllBatch) {
    // if (productInventoryChangeRecordPO.getTotalCount() < 0) {
    // // 找到所有批次库存大于0的，按入库日期倒序扣减
    // // 如果最后一个批次不够扣，把最后一个批次的库存扣成负数
    // productStoreBatchPOS = processByBatchs(productInventoryChangeRecordPO);
    // }
    // }
    // //两种情况
    // //1、changeCount会大于0（上线之后，直接就到部分配送这一步的订单）
    // // 找到最后一个批次，把库存加上去
    // //2、找不到到可以正常处理的批次库存,就去找最后一个批次
    // if (productStoreBatchPOS.size() <= 0) {
    // productStoreBatchPOS = processByLastBatch(productInventoryChangeRecordPO);
    // }
    // //如果最后一个批次也找不到，创建一个新的批次,批次数量为0,再把TotalCount赋值进去.
    // if (productStoreBatchPOS.size() <= 0) {
    // productStoreBatchPOS = processByNewBatch(productInventoryChangeRecordPO);
    // }
    // return productStoreBatchPOS;
    // }

    // // 找到所有批次库存大于0的，按入库日期倒序扣减
    // // 如果最后一个批次不够扣，把最后一个批次的库存扣成负数
    // private List<ProductStoreBatchPO> processByBatchs(ProductInventoryChangeRecordPO productInventoryChangeRecordPO)
    // {
    // List<ProductStoreBatchPO> productStoreBatchPOS = new ArrayList<>();
    // //先根据库存Id，找到所有可以扣减库存的批次（批次库存数量大于0）
    // List<ProductStoreBatchPO> productStoreBatchs =
    // productStoreBatchMapper.findProductStoreBatch(productInventoryChangeRecordPO.getProductStoreId());
    // if (productStoreBatchs.size() > 0) {
    // Integer changeCount = productInventoryChangeRecordPO.getTotalCount();//扣减数量
    // for (int i = 0; i < productStoreBatchs.size(); i++) {
    // if (changeCount == 0) {
    // break;
    // }
    // ProductStoreBatchPO productStoreBatchPO = productStoreBatchs.get(i);
    //
    // //计算本次要扣减数量，原批次数量绝对值与本次数量绝对值的最小值
    // Integer relChangeCount = -1 * Math.min(Math.abs(productStoreBatchPO.getTotalCount()), Math.abs(changeCount));
    //
    // // 如果扣到最后一个批次库存，仍然没扣完，扣最新批次库存，扣成负数
    // if (changeCount < 0 && i == productStoreBatchs.size() - 1) {
    // relChangeCount = changeCount;
    // }
    // changeCount -= relChangeCount;
    // productStoreBatchPO.setTotalCount(relChangeCount);
    // productStoreBatchPOS.add(productStoreBatchPO);
    // }
    // }
    // return productStoreBatchPOS;
    // }

    // // 找不到大于0的批次，则只处理最后一条批次
    // private List<ProductStoreBatchPO> processByLastBatch(ProductInventoryChangeRecordPO
    // productInventoryChangeRecordPO) {
    // // 找不到到可以正常处理的批次库存,就去找最后一个批次
    // ProductStoreBatchPO lastProductStoreBatch =
    // productStoreBatchMapper.findLastProductStoreBatch(productInventoryChangeRecordPO.getProductStoreId());
    // return processByProductStoreBatch(productInventoryChangeRecordPO, lastProductStoreBatch);
    // }

    // // 根据最后一条批次进行库存加减
    // private List<ProductStoreBatchPO> processByProductStoreBatch(ProductInventoryChangeRecordPO
    // productInventoryChangeRecordPO, ProductStoreBatchPO productStoreBatch) {
    // List<ProductStoreBatchPO> productStoreBatchPOS = new ArrayList<>();
    // if (productStoreBatch != null) {
    // //如果找到了 进行库存加减操作
    // productStoreBatch.setTotalCount(productInventoryChangeRecordPO.getTotalCount());
    // productStoreBatchPOS.add(productStoreBatch);
    // }
    // return productStoreBatchPOS;
    // }

    // // 创建一条新批次库存
    // public List<ProductStoreBatchPO> processByNewBatch(ProductInventoryChangeRecordPO productInventoryChangeRecordPO)
    // {
    // ArrayList<ProductStoreBatchPO> productStoreBatchPOS = new ArrayList<>();
    // ProductStoreBatchPO productStoreBatchPO = new ProductStoreBatchPO();
    // if (productInventoryChangeRecordPO.getErpEventType() != null) {//erp
    // productStoreBatchPO.setId(productInventoryChangeRecordPO.getErpOrderKey());
    // } else {
    // productStoreBatchPO.setId(UUIDUtil.getUUID().toString().replaceAll("-", ""));
    // }
    // productStoreBatchPO.setProductStoreId(productInventoryChangeRecordPO.getProductStoreId());
    // productStoreBatchPO.setTotalCount(productInventoryChangeRecordPO.getTotalCount());
    // productStoreBatchPO.setExpireTime(productInventoryChangeRecordPO.getExpireTime());
    // productStoreBatchPO.setProductionDate(productInventoryChangeRecordPO.getProductionDate());
    // productStoreBatchPO.setBatchTime(productInventoryChangeRecordPO.getBatchTime());
    // productStoreBatchPO.setBatchAttributeInfoNo(productInventoryChangeRecordPO.getBatchAttributeInfoNo());
    // productStoreBatchMapper.addBatchInventory(productStoreBatchPO);
    // productStoreBatchPO.setNeedUpdateBatchInventory(false);
    // productStoreBatchPOS.add(productStoreBatchPO);
    // return productStoreBatchPOS;
    // }

    /**
     * 出库是否允许分配残次品库存
     *
     * @param changeRecordPO
     * @return
     */
    private boolean isGetDefectiveProductStoreBatch(ProductInventoryChangeRecordPO changeRecordPO,
                                                    List<ProductStoreBatchPO> productStoreBatchPOS, List<Integer> exSubcategory, boolean isRandom) {
        LOGGER.info(String.format("出库是否允许分配残次品库存 是否随机：%s,排除货区:%s,查询库存结果：%s", isRandom, JSON.toJSONString(exSubcategory),
                JSON.toJSONString(productStoreBatchPOS)));
        if (!isRandom) {
            return false;
        }

        if (CollectionUtils.isEmpty(exSubcategory) || !exSubcategory
                .containsAll(Lists.newArrayList(LocationEnum.残次品位.getType(), LocationAreaEnum.残次品区.getType()))) {
            return false;
        }

        // 过滤入库数据
        if (changeRecordPO.getTotalCount().compareTo(BigDecimal.ZERO) >= 0) {
            return false;
        }

        if (CollectionUtils.isEmpty(productStoreBatchPOS)) {
            return true;
        }

        // 可用为正数的总库存数量小于要扣减的数量，这种情况可以处理残次品数量
        BigDecimal normalProductStoreBatchCount = productStoreBatchPOS.stream()
                .filter(p -> p != null && p.getTotalCount() != null && p.getTotalCount().compareTo(BigDecimal.ZERO) > 0)
                .map(ProductStoreBatchPO::getTotalCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return normalProductStoreBatchCount.compareTo(changeRecordPO.getTotalCount().abs()) < 0;
    }

    /**
     * 出库是否允许分配指定货位以外
     *
     * @param changeRecordPO
     * @return
     */
    private boolean isGetOtherLocationProductStoreBatch(ProductInventoryChangeRecordPO changeRecordPO,
                                                        List<ProductStoreBatchPO> productStoreBatchPOS, Integer locationType, List<Integer> exSubcategory,
                                                        boolean isRandom) {
        LOGGER.info(String.format("出库是否允许去除指定货区分配库存 是否随机：%s,排除货区:%s,查询库存结果：%s", isRandom,
                JSON.toJSONString(exSubcategory), JSON.toJSONString(productStoreBatchPOS)));
        if (!isRandom) {
            return false;
        }

        // 过滤入库数据
        if (changeRecordPO.getTotalCount().compareTo(BigDecimal.ZERO) >= 0) {
            return false;
        }

        // 过滤非残次品调拨出库数据
        if (!Objects.equals(ERPType.残次品调拨库.getType(), changeRecordPO.getOrderType())) {
            return false;
        }

        if (locationType == null || !Objects.equals(locationType, LocationAreaEnum.残次品区.getType())) {
            return false;
        }

        if (CollectionUtils.isEmpty(productStoreBatchPOS)) {
            return true;
        }

        List<ProductStoreBatchPO> locationProductStoreBatchPOS = productStoreBatchPOS.stream()
                .filter(p -> p != null && p.getTotalCount() != null && p.getTotalCount().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationProductStoreBatchPOS)) {
            return true;
        }

        BigDecimal unitTotalCount = locationProductStoreBatchPOS.stream().filter(Objects::nonNull)
                .map(ProductStoreBatchPO::getTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 出库数量大于指定货位库存
        if (changeRecordPO.getTotalCount().abs().compareTo(unitTotalCount) > 0) {
            return true;
        }

        return false;
    }

    public void deleteByPromotionStoreBatch(ProductInventoryChangeRecordPO changeRecordPO) {
        try {
            if (StringUtils.isEmpty(changeRecordPO.getBatchAttributeInfoNo())) {
                return;
            }
            ProductPromotionStoreBatchDTO deleteDTO = new ProductPromotionStoreBatchDTO();
            deleteDTO.setWarehouseId(changeRecordPO.getWarehouseId());
            deleteDTO.setBatchAttributeInfoNo(changeRecordPO.getBatchAttributeInfoNo());
            promotionStoreBatchBL.deleteByBatchAttributeInfoNo(deleteDTO);
        } catch (Exception e) {
            LOGGER.error("根据批次编号检查删除促销批次库存异常：" + JSON.toJSONString(changeRecordPO.getBatchAttributeInfoNo()), e);
        }
    }

    /**
     * 入库货位是残次品
     *
     * @param changeRecordPO
     * @return
     */
    private boolean isDefectiveLocation(ProductInventoryChangeRecordPO changeRecordPO) {

        // 过滤出库数据
        if (changeRecordPO.getTotalCount().compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        List<Long> locations = new ArrayList<>();
        if (changeRecordPO.getLocationId() != null) {
            locations.add(changeRecordPO.getLocationId());
        }
        if (CollectionUtils.isNotEmpty(changeRecordPO.getLocationIds())) {
            locations.addAll(changeRecordPO.getLocationIds());
        }
        if (CollectionUtils.isEmpty(locations)) {
            return false;
        }

        // 是否有残次品位
        boolean isDefective = false;
        // 判断货位类型是否为：残次品位或者残次品区
        List<LoactionDTO> locationInfoList = iLocationService.findLocationByIds(locations);
        if (CollectionUtils.isNotEmpty(locationInfoList)) {
            isDefective = locationInfoList.stream()
                    .anyMatch(p -> Objects.equals(LocationEnum.残次品位.getType().byteValue(), p.getSubcategory())
                            || Objects.equals(LocationAreaEnum.残次品区.getType().byteValue(), p.getSubcategory()));
        }

        return isDefective;
    }
}
