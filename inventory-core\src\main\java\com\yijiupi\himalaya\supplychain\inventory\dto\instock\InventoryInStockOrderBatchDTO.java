package com.yijiupi.himalaya.supplychain.inventory.dto.instock;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单确认入库DTO
 *
 * <AUTHOR> 2018/4/25
 */
public class InventoryInStockOrderBatchDTO implements Serializable {
    /**
     * 城市id(分片用)
     */
    private Integer orgId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 入库批次
     */
    private String taskNumber;
    /**
     * 入库时间
     */
    private Date stockInTime;
    /**
     * 发货时间
     */
    private Date turnoutTime;
    /**
     * 配送车辆id
     */
    private Long carId;
    /**
     * 配送车辆
     */
    private String carName;
    /**
     * 配送员id
     */
    private Integer deliveryUserId;
    /**
     * 配送员姓名
     */
    private String deliveryUserName;
    /**
     * 装卸工
     */
    private Integer stevedoreUserId;
    /**
     * 装卸工
     */
    private String stevedoreUserName;
    /**
     * 退回大数量
     */
    private BigDecimal returnPackageCount;
    /**
     * 退回小数量
     */
    private BigDecimal returnUnitCount;
    /**
     * 关联的订单.
     */
    private List<InventoryOrderDTO> orderList;

    /**
     * 最终发货城市id
     */
    private Integer toOrgId;

    /**
     * 最终发货仓库id
     */
    private Integer toWarehouseId;

    /**
     * 入库备注
     */
    private String remark;

    /**
     * 车次ID
     */
    private Long trainNumberId;

    /**
     * 获取 入库批次
     */
    public String getTaskNumber() {
        return this.taskNumber;
    }

    /**
     * 设置 入库批次
     */
    public void setTaskNumber(String taskNumber) {
        this.taskNumber = taskNumber;
    }

    /**
     * 获取 入库时间
     */
    public Date getStockInTime() {
        return this.stockInTime;
    }

    /**
     * 设置 入库时间
     */
    public void setStockInTime(Date stockInTime) {
        this.stockInTime = stockInTime;
    }

    /**
     * 获取 发货时间
     */
    public Date getTurnoutTime() {
        return this.turnoutTime;
    }

    /**
     * 设置 发货时间
     */
    public void setTurnoutTime(Date turnoutTime) {
        this.turnoutTime = turnoutTime;
    }

    /**
     * 获取 配送车辆id
     */
    public Long getCarId() {
        return this.carId;
    }

    /**
     * 设置 配送车辆id
     */
    public void setCarId(Long carId) {
        this.carId = carId;
    }

    /**
     * 获取 配送车辆
     */
    public String getCarName() {
        return this.carName;
    }

    /**
     * 设置 配送车辆
     */
    public void setCarName(String carName) {
        this.carName = carName;
    }

    /**
     * 获取 配送员id
     */
    public Integer getDeliveryUserId() {
        return this.deliveryUserId;
    }

    /**
     * 设置 配送员id
     */
    public void setDeliveryUserId(Integer deliveryUserId) {
        this.deliveryUserId = deliveryUserId;
    }

    /**
     * 获取 配送员姓名
     */
    public String getDeliveryUserName() {
        return this.deliveryUserName;
    }

    /**
     * 设置 配送员姓名
     */
    public void setDeliveryUserName(String deliveryUserName) {
        this.deliveryUserName = deliveryUserName;
    }

    /**
     * 获取 装卸工
     */
    public String getStevedoreUserName() {
        return this.stevedoreUserName;
    }

    /**
     * 设置 装卸工
     */
    public void setStevedoreUserName(String stevedoreUserName) {
        this.stevedoreUserName = stevedoreUserName;
    }

    /**
     * 获取 退回大数量
     */
    public BigDecimal getReturnPackageCount() {
        return this.returnPackageCount;
    }

    /**
     * 设置 退回大数量
     */
    public void setReturnPackageCount(BigDecimal returnPackageCount) {
        this.returnPackageCount = returnPackageCount;
    }

    /**
     * 获取 退回小数量
     */
    public BigDecimal getReturnUnitCount() {
        return this.returnUnitCount;
    }

    /**
     * 设置 退回小数量
     */
    public void setReturnUnitCount(BigDecimal returnUnitCount) {
        this.returnUnitCount = returnUnitCount;
    }

    /**
     * 获取 关联的订单.
     */
    public List<InventoryOrderDTO> getOrderList() {
        return this.orderList;
    }

    /**
     * 设置 关联的订单.
     */
    public void setOrderList(List<InventoryOrderDTO> orderList) {
        this.orderList = orderList;
    }

    /**
     * 获取 装卸工
     */
    public Integer getStevedoreUserId() {
        return this.stevedoreUserId;
    }

    /**
     * 设置 装卸工
     */
    public void setStevedoreUserId(Integer stevedoreUserId) {
        this.stevedoreUserId = stevedoreUserId;
    }

    @Override
    public String toString() {
        return "InventoryInStockOrderBatchDTO{" + "taskNumber='" + taskNumber + '\'' + ", stockInTime=" + stockInTime
            + ", turnoutTime=" + turnoutTime + ", carId=" + carId + ", carName='" + carName + '\'' + ", deliveryUserId="
            + deliveryUserId + ", deliveryUserName='" + deliveryUserName + '\'' + ", stevedoreUserId=" + stevedoreUserId
            + ", stevedoreUserName='" + stevedoreUserName + '\'' + ", returnPackageCount=" + returnPackageCount
            + ", returnUnitCount=" + returnUnitCount + ", orderList=" + orderList + '}';
    }

    /**
     * 获取 城市id(分片用)
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 城市id(分片用)
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getToOrgId() {
        return toOrgId;
    }

    public void setToOrgId(Integer toOrgId) {
        this.toOrgId = toOrgId;
    }

    public Integer getToWarehouseId() {
        return toWarehouseId;
    }

    public void setToWarehouseId(Integer toWarehouseId) {
        this.toWarehouseId = toWarehouseId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getTrainNumberId() {
        return trainNumberId;
    }

    public void setTrainNumberId(Long trainNumberId) {
        this.trainNumberId = trainNumberId;
    }
}
