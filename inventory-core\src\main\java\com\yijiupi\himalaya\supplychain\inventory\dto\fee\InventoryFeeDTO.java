package com.yijiupi.himalaya.supplychain.inventory.dto.fee;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存费用dto
 * 
 * <AUTHOR> 2018/2/2
 */
public class InventoryFeeDTO implements Serializable {
    /**
     * skuId
     */
    private Long productSkuId;
    /**
     * 产品来源
     */
    private Integer source;
    /**
     * 配送费
     */
    private BigDecimal deliveryFeeSum;
    /**
     * 托管费
     */
    private BigDecimal warehouseCustodyFeeSum;

    /**
     * 获取 skuId
     *
     * @return productSkuId skuId
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 skuId
     *
     * @param productSkuId skuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 产品来源
     *
     * @return source 产品来源
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源
     *
     * @param source 产品来源
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 配送费
     *
     * @return deliveryFeeSum 配送费
     */
    public BigDecimal getDeliveryFeeSum() {
        return this.deliveryFeeSum;
    }

    /**
     * 设置 配送费
     *
     * @param deliveryFeeSum 配送费
     */
    public void setDeliveryFeeSum(BigDecimal deliveryFeeSum) {
        this.deliveryFeeSum = deliveryFeeSum;
    }

    /**
     * 获取 托管费
     *
     * @return warehouseCustodyFeeSum 托管费
     */
    public BigDecimal getWarehouseCustodyFeeSum() {
        return this.warehouseCustodyFeeSum;
    }

    /**
     * 设置 托管费
     *
     * @param warehouseCustodyFeeSum 托管费
     */
    public void setWarehouseCustodyFeeSum(BigDecimal warehouseCustodyFeeSum) {
        this.warehouseCustodyFeeSum = warehouseCustodyFeeSum;
    }
}
