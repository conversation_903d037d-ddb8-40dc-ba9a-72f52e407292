package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * sku对应城市下仓库库存变更记录查询DTO Created by <PERSON><PERSON><PERSON> on 2017-09-30
 */
public class CityInventoryRecordQueryDTO implements Serializable {

    /**
     * 单据类型
     */
    private Integer recordType;
    /**
     * 开始
     */
    private Date timeS;
    /**
     * 结束
     */
    private Date timeE;
    /**
     * 单号
     */
    private String orderNO;
    /**
     * 库存类型 InventoryChangeTypes
     */
    private Integer storeType;
    /**
     * 产品skuId
     */
    private Long productSkuId;
    /**
     * 城市ID
     */
    private Integer cityId;
    /**
     * sku对应城市下库存ID集合
     */
    private List<String> productInventoryIdList;

    /**
     * 获取 单据类型
     *
     * @return recordType 单据类型
     */
    public Integer getRecordType() {
        return this.recordType;
    }

    /**
     * 设置 单据类型
     *
     * @param recordType 单据类型
     */
    public void setRecordType(Integer recordType) {
        this.recordType = recordType;
    }

    /**
     * 获取 开始
     *
     * @return timeS 开始
     */
    public Date getTimeS() {
        return this.timeS;
    }

    /**
     * 设置 开始
     *
     * @param timeS 开始
     */
    public void setTimeS(Date timeS) {
        this.timeS = timeS;
    }

    /**
     * 获取 结束
     *
     * @return timeE 结束
     */
    public Date getTimeE() {
        return this.timeE;
    }

    /**
     * 设置 结束
     *
     * @param timeE 结束
     */
    public void setTimeE(Date timeE) {
        this.timeE = timeE;
    }

    /**
     * 获取 单号
     *
     * @return orderNO 单号
     */
    public String getOrderNO() {
        return this.orderNO;
    }

    /**
     * 设置 单号
     *
     * @param orderNO 单号
     */
    public void setOrderNO(String orderNO) {
        this.orderNO = orderNO;
    }

    /**
     * 获取 库存类型 InventoryChangeTypes
     *
     * @return storeType 库存类型 InventoryChangeTypes
     */
    public Integer getStoreType() {
        return this.storeType;
    }

    /**
     * 设置 库存类型 InventoryChangeTypes
     *
     * @param storeType 库存类型 InventoryChangeTypes
     */
    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    /**
     * 获取 产品skuId
     *
     * @return productSkuId 产品skuId
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品skuId
     *
     * @param productSkuId 产品skuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 城市ID
     *
     * @return cityId 城市ID
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市ID
     *
     * @param cityId 城市ID
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 sku对应城市下库存ID集合
     *
     * @return productInventoryIdList sku对应城市下库存ID集合
     */
    public List<String> getProductInventoryIdList() {
        return this.productInventoryIdList;
    }

    /**
     * 设置 sku对应城市下库存ID集合
     *
     * @param productInventoryIdList sku对应城市下库存ID集合
     */
    public void setProductInventoryIdList(List<String> productInventoryIdList) {
        this.productInventoryIdList = productInventoryIdList;
    }
}
