package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.CcpPriceMessageDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
@Component
public class DefectiveProductPriceEvent {

    @Value("${ex.supplychain.inventory.updateCcpPrice}")
    private String updateCcpPriceEx;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 更新残次品相关额度到redis
     *
     * @param orgId
     * @param warehouseId
     */
    public void updateDefectiveProductPrice(Integer orgId, Integer warehouseId,
        List<ProductStoreBatchPO> productStoreBatchPOS) {
        if (CollectionUtils.isEmpty(productStoreBatchPOS)) {
            return;
        }

        List<ProductStoreBatchPO> ccpStoreBatchList = productStoreBatchPOS.stream()
            .filter(elem -> Objects.equals(elem.getLocationCategory(), CategoryEnum.CARGO_AREA.getValue())
                ? Objects.equals(elem.getSubcategory(), LocationAreaEnum.残次品区.getType())
                : Objects.equals(elem.getSubcategory(), LocationEnum.残次品位.getType()))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ccpStoreBatchList)) {
            return;
        }

        List<ProductStoreBatchDTO> ccpStoreBatchDTOS = new ArrayList<>();
        ccpStoreBatchList.forEach(storePO -> {
            ProductStoreBatchDTO storeDTO = new ProductStoreBatchDTO();
            storeDTO.setProductStoreId(storePO.getProductStoreId());
            storeDTO.setTotalCount(storePO.getTotalCount());
            storeDTO.setProductSkuId(storePO.getProductSkuId());
            storeDTO.setBatchProperty(storePO.getBatchProperty());
            ccpStoreBatchDTOS.add(storeDTO);
        });

        CcpPriceMessageDTO ccpPriceMsg = new CcpPriceMessageDTO();
        ccpPriceMsg.setOrgId(orgId);
        ccpPriceMsg.setWarehouseId(warehouseId);
        ccpPriceMsg.setStoreBatchList(ccpStoreBatchDTOS);
        rabbitTemplate.convertAndSend(updateCcpPriceEx, null, ccpPriceMsg);
    }

}
