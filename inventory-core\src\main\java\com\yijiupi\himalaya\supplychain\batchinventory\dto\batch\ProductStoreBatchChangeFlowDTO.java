package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 批次库存变更流水
 */
public class ProductStoreBatchChangeFlowDTO implements Serializable {
    private static final long serialVersionUID = -6659026105008385376L;

    /**
     * 批次库存变更ID
     */
    private String id;

    /**
     * 关联库存表Id
     */
    private String batchId;

    /**
     * 关联库存变更表记录Id
     */
    private String changeRecordId;

    /**
     * 批次库存变化的总数量
     */
    private BigDecimal totalCountMinUnit;

    /**
     * 变更时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 操作人
     */
    private String createUser;

    /**
     * 单据id
     */
    private String orderId;

    /**
     * 单号
     */
    private String orderNo;

    /**
     * 单据类型
     */
    private Integer orderType;

    /**
     * 单据类型名称
     */
    private String orderTypeName;

    /**
     * 酒批事件类型
     */
    private Integer jiupiEventType;

    /**
     * 酒批事件类型名称
     */
    private String jiupiEventTypeName;

    /**
     * erp事件类型
     */
    private Integer erpEventType;

    /**
     * 描述
     */
    private String description;

    /**
     * 批次号
     */
    private String batchInfoNo;

    /**
     * 批次时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date batchTime;
    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionDate;

    /**
     * 货位/货区
     */
    private Integer locationCategory;

    /**
     * 货位类别
     */
    private Integer locationSubcategory;

    /**
     * 货位类别名称
     */
    private String locationSubcategoryName;

    /**
     * 货位id
     */
    private Long locationId;

    /**
     * 货位名称
     */
    private String locationName;

    /**
     * 货区id
     */
    private Long areaId;

    /**
     * 货区名称
     */
    private String areaName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getChangeRecordId() {
        return changeRecordId;
    }

    public void setChangeRecordId(String changeRecordId) {
        this.changeRecordId = changeRecordId;
    }

    public BigDecimal getTotalCountMinUnit() {
        return totalCountMinUnit;
    }

    public void setTotalCountMinUnit(BigDecimal totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderTypeName() {
        return orderTypeName;
    }

    public void setOrderTypeName(String orderTypeName) {
        this.orderTypeName = orderTypeName;
    }

    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    public String getJiupiEventTypeName() {
        return jiupiEventTypeName;
    }

    public void setJiupiEventTypeName(String jiupiEventTypeName) {
        this.jiupiEventTypeName = jiupiEventTypeName;
    }

    public Integer getErpEventType() {
        return erpEventType;
    }

    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBatchInfoNo() {
        return batchInfoNo;
    }

    public void setBatchInfoNo(String batchInfoNo) {
        this.batchInfoNo = batchInfoNo;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Integer getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    public Integer getLocationSubcategory() {
        return locationSubcategory;
    }

    public void setLocationSubcategory(Integer locationSubcategory) {
        this.locationSubcategory = locationSubcategory;
    }

    public String getLocationSubcategoryName() {
        return locationSubcategoryName;
    }

    public void setLocationSubcategoryName(String locationSubcategoryName) {
        this.locationSubcategoryName = locationSubcategoryName;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
}
