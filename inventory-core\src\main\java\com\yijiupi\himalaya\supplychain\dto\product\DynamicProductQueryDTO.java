package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.util.List;

public class DynamicProductQueryDTO implements Serializable {

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 动销时间-起(格式：yyyy-MM-dd HH:mm:ss)
     */
    private String dynamicStartTime;

    /**
     * 动销时间-止(格式：yyyy-MM-dd HH:mm:ss)
     */
    private String dynamicEndTime;

    /**
     * 一级类目ID
     */
    private Long statisticsClass;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 货位名称
     */
    private String locationName;

    /**
     * 货区/货区类型，货位类型
     */
    private List<Integer> subCategorys;

    /**
     * 是否每日拣货盘点
     */
    private Boolean dynamicDailyPicking = false;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页数
     */
    private Integer pageSize;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getDynamicStartTime() {
        return dynamicStartTime;
    }

    public void setDynamicStartTime(String dynamicStartTime) {
        this.dynamicStartTime = dynamicStartTime;
    }

    public String getDynamicEndTime() {
        return dynamicEndTime;
    }

    public void setDynamicEndTime(String dynamicEndTime) {
        this.dynamicEndTime = dynamicEndTime;
    }

    public Long getStatisticsClass() {
        return statisticsClass;
    }

    public void setStatisticsClass(Long statisticsClass) {
        this.statisticsClass = statisticsClass;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public List<Integer> getSubCategorys() {
        return subCategorys;
    }

    public void setSubCategorys(List<Integer> subCategorys) {
        this.subCategorys = subCategorys;
    }

    public Boolean getDynamicDailyPicking() {
        return dynamicDailyPicking;
    }

    public void setDynamicDailyPicking(Boolean dynamicDailyPicking) {
        this.dynamicDailyPicking = dynamicDailyPicking;
    }
}
