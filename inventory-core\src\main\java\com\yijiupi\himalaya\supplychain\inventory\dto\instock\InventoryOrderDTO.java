package com.yijiupi.himalaya.supplychain.inventory.dto.instock;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单提交
 *
 * @author: tangkun
 * @date: 2017年10月11日 上午10:27:21
 */
public class InventoryOrderDTO implements Serializable {
    private static final long serialVersionUID = -8965416745360041997L;
    /**
     * 订单明细
     */
    private List<InventoryOrderItemDTO> items;
    /**
     * id
     */
    private Long id;
    /**
     * 订单编号
     */
    private String refOrderNo;
    /**
     * 配送订单状态 延迟配送(3)
     */
    private Byte deliveryMarkState;
    /**
     * 标记状态
     */
    private Byte receiptState;
    /**
     * 城市ID，分库用（第三方订单使用一个新的自定义CityId）(发货城市id)
     */
    private Integer orgId;
    /**
     * 出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     */
    private Byte orderType;
    /**
     * 商品种类数
     */
    private Integer skuCount;
    /**
     * 发货仓库id(OMS的pickupWarehouseId)
     */
    private Integer warehouseId;
    /**
     * 收货人姓名
     */
    private String userName;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 收货人手机号
     */
    private String mobileNo;
    /**
     * 收货地址
     */
    private String detailAddress;
    /**
     * 地址id
     */
    private Integer addressId;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String county;
    /**
     * 街道
     */
    private String street;
    /**
     * 托管方ID
     */
    private Long parterId;
    /**
     * 托管方名称
     */
    private String parterName;
    /**
     * 下单时间
     */
    private Date orderCreateTime;
    /**
     * 运单类型（收发单） (判断是出库还是入库,出库out表,入库in表)
     */
    private Byte deliveryOrderType;
    /**
     * 应付金额
     */
    private BigDecimal payableAmount;
    /**
     * 订单金额
     */
    private BigDecimal orderAmount;
    /**
     * 退货金额
     */
    private BigDecimal returnAmount;
    /**
     * OMS单据业务模式
     */
    private Byte businessType;
    /**
     * 区域id
     */
    private Long areaId;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 线路id
     */
    private Long routeId;
    /**
     * 线路名称
     */
    private String routeName;
    /**
     * 线路排序号
     */
    private Integer routeSequence;
    /**
     * 下单城市id
     */
    private Integer fromCityId;
    /**
     * '订单状态【（酒批退货单）1申请退货/2 已取消退货/3区域审核通过/4区域审核拒绝/5运营审核通过/6运营审核拒绝/7待取货/8已取货/
     * 9拒绝取货/10已退货/11待结账/12取货中/延迟退货】【（酒批订单）1已下单/2已取消/3审核通过/5待发货/6已发货/7已完成/8配送失败/10待结账/
     * 11待支付/12支付成功/13支付失败/14延迟配送/16作废】【（经纪人撮合） 1审核通过/2已发货/3待结账/4已经完成/5已取消】 【（兑奖订单）1 待打印、2 待发货、3 待结账、4已完成、5已取消 、6配送失败
     * 、7已发货、8延迟配送】',
     */
    private Byte orderState;
    /**
     * 配送方式 （0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    private Byte deliveryMode;

    /**
     * 是否是易款订单
     */
    private boolean isYiKuanOrder;

    /**
     *
     */
    private Long deliveryOrderId;

    /**
     * 调拨类型
     */
    private Byte allotType;

    /**
     * 货位
     */
    private Long locationId;

    /**
     * 货位名称
     */
    private String locationName;

    /**
     * oms订单id
     */
    private Long omsOrderId;

    /**
     * 下单仓库id
     */
    private Integer fromWarehouseId;

    /**
     * WMS系统单关联据号
     */
    private String relationOrderNo;

    /**
     * 获取 id (oms的主键 businessId)
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 id (oms的主键 businessId)
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 订单编号 (oms的businessNo)
     */
    public String getRefOrderNo() {
        return this.refOrderNo;
    }

    /**
     * 设置 订单编号 (oms的businessNo)
     */
    public void setRefOrderNo(String refOrderNo) {
        this.refOrderNo = refOrderNo;
    }

    /**
     * 获取 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 城市ID，分库用（第三方订单使用一个新的自定义CityId）
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     */
    public Byte getOrderType() {
        return this.orderType;
    }

    /**
     * 设置 出库单类型：普通订单（0），招商订单和分销商订单（1），经销商直配订单（2），大货批发订单（3），大商转配送（10），轻加盟订单（11），经销商订单酒批配送（12）
     */
    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    /**
     * 获取 商品种类数
     */
    public Integer getSkuCount() {
        return this.skuCount;
    }

    /**
     * 设置 商品种类数
     */
    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    /**
     * 获取 发货仓库id(OMS的pickupWarehouseId)
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 发货仓库id(OMS的pickupWarehouseId)
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 收货人姓名
     */
    public String getUserName() {
        return this.userName;
    }

    /**
     * 设置 收货人姓名
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 获取 店铺名称
     */
    public String getShopName() {
        return this.shopName;
    }

    /**
     * 设置 店铺名称
     */
    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    /**
     * 获取 收货人手机号
     */
    public String getMobileNo() {
        return this.mobileNo;
    }

    /**
     * 设置 收货人手机号
     */
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    /**
     * 获取 收货地址
     */
    public String getDetailAddress() {
        return this.detailAddress;
    }

    /**
     * 设置 收货地址
     */
    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    /**
     * 获取 地址id
     */
    public Integer getAddressId() {
        return this.addressId;
    }

    /**
     * 设置 地址id
     */
    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    /**
     * 获取 省
     */
    public String getProvince() {
        return this.province;
    }

    /**
     * 设置 省
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * 获取 市
     */
    public String getCity() {
        return this.city;
    }

    /**
     * 设置 市
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 获取 区
     */
    public String getCounty() {
        return this.county;
    }

    /**
     * 设置 区
     */
    public void setCounty(String county) {
        this.county = county;
    }

    /**
     * 获取 街道
     */
    public String getStreet() {
        return this.street;
    }

    /**
     * 设置 街道
     */
    public void setStreet(String street) {
        this.street = street;
    }

    /**
     * 获取 托管方ID
     */
    public Long getParterId() {
        return this.parterId;
    }

    /**
     * 设置 托管方ID
     */
    public void setParterId(Long parterId) {
        this.parterId = parterId;
    }

    /**
     * 获取 托管方名称
     */
    public String getParterName() {
        return this.parterName;
    }

    /**
     * 设置 托管方名称
     */
    public void setParterName(String parterName) {
        this.parterName = parterName;
    }

    /**
     * 获取 下单时间
     */
    public Date getOrderCreateTime() {
        return this.orderCreateTime;
    }

    /**
     * 设置 下单时间
     */
    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    /**
     * 获取 运单类型（收发单） (判断是出库还是入库,出库out表,入库in表)
     */
    public Byte getDeliveryOrderType() {
        return this.deliveryOrderType;
    }

    /**
     * 设置 运单类型（收发单） (判断是出库还是入库,出库out表,入库in表)
     */
    public void setDeliveryOrderType(Byte deliveryOrderType) {
        this.deliveryOrderType = deliveryOrderType;
    }

    /**
     * 获取 应付金额
     */
    public BigDecimal getPayableAmount() {
        return this.payableAmount;
    }

    /**
     * 设置 应付金额
     */
    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }

    /**
     * 获取 订单金额
     */
    public BigDecimal getOrderAmount() {
        return this.orderAmount;
    }

    /**
     * 设置 订单金额
     */
    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    /**
     * 获取 退货金额
     */
    public BigDecimal getReturnAmount() {
        return this.returnAmount;
    }

    /**
     * 设置 退货金额
     */
    public void setReturnAmount(BigDecimal returnAmount) {
        this.returnAmount = returnAmount;
    }

    /**
     * 获取 OMS单据业务模式
     */
    public Byte getBusinessType() {
        return this.businessType;
    }

    /**
     * 设置 OMS单据业务模式
     */
    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取 区域id
     */
    public Long getAreaId() {
        return this.areaId;
    }

    /**
     * 设置 区域id
     */
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    /**
     * 获取 区域名称
     */
    public String getAreaName() {
        return this.areaName;
    }

    /**
     * 设置 区域名称
     */
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * 获取 线路id
     */
    public Long getRouteId() {
        return this.routeId;
    }

    /**
     * 设置 线路id
     */
    public void setRouteId(Long routeId) {
        this.routeId = routeId;
    }

    /**
     * 获取 线路名称
     */
    public String getRouteName() {
        return this.routeName;
    }

    /**
     * 设置 线路名称
     */
    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    /**
     * 获取 线路排序号
     */
    public Integer getRouteSequence() {
        return this.routeSequence;
    }

    /**
     * 设置 线路排序号
     */
    public void setRouteSequence(Integer routeSequence) {
        this.routeSequence = routeSequence;
    }

    /**
     * 获取 下单城市id
     */
    public Integer getFromCityId() {
        return this.fromCityId;
    }

    /**
     * 设置 下单城市id
     */
    public void setFromCityId(Integer fromCityId) {
        this.fromCityId = fromCityId;
    }

    /**
     * '订单状态【（酒批退货单）1申请退货/2 已取消退货/3区域审核通过/4区域审核拒绝/5运营审核通过/6运营审核拒绝/7待取货/8已取货/
     * 9拒绝取货/10已退货/11待结账/12取货中/延迟退货】【（酒批订单）1已下单/2已取消/3审核通过/5待发货/6已发货/7已完成/8配送失败/10待结账/
     * 11待支付/12支付成功/13支付失败/14延迟配送/16作废】【（经纪人撮合） 1审核通过/2已发货/3待结账/4已经完成/5已取消】 【（兑奖订单）1 待打印、2 待发货、3 待结账、4已完成、5已取消 、6配送失败
     * 、7已发货、8延迟配送】',
     */
    public Byte getOrderState() {
        return this.orderState;
    }

    /**
     * '订单状态【（酒批退货单）1申请退货/2 已取消退货/3区域审核通过/4区域审核拒绝/5运营审核通过/6运营审核拒绝/7待取货/8已取货/
     * 9拒绝取货/10已退货/11待结账/12取货中/延迟退货】【（酒批订单）1已下单/2已取消/3审核通过/5待发货/6已发货/7已完成/8配送失败/10待结账/
     * 11待支付/12支付成功/13支付失败/14延迟配送/16作废】【（经纪人撮合） 1审核通过/2已发货/3待结账/4已经完成/5已取消】 【（兑奖订单）1 待打印、2 待发货、3 待结账、4已完成、5已取消 、6配送失败
     * 、7已发货、8延迟配送】',
     */
    public void setOrderState(Byte OrderState) {
        this.orderState = OrderState;
    }

    /**
     * 获取 配送方式 （0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    public Byte getDeliveryMode() {
        return this.deliveryMode;
    }

    /**
     * 设置 配送方式 （0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    public void setDeliveryMode(Byte deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    /**
     * 获取 订单明细
     */
    public List<InventoryOrderItemDTO> getItems() {
        return this.items;
    }

    /**
     * 设置 订单明细
     */
    public void setItems(List<InventoryOrderItemDTO> items) {
        this.items = items;
    }

    /**
     * 获取 标记状态
     */
    public Byte getReceiptState() {
        return this.receiptState;
    }

    /**
     * 设置 标记状态
     */
    public void setReceiptState(Byte receiptState) {
        this.receiptState = receiptState;
    }

    /**
     * 获取 配送订单状态 延迟配送(3)
     */
    public Byte getDeliveryMarkState() {
        return this.deliveryMarkState;
    }

    /**
     * 设置 配送订单状态 延迟配送(3)
     */
    public void setDeliveryMarkState(Byte deliveryMarkState) {
        this.deliveryMarkState = deliveryMarkState;
    }

    // @Override
    // public String toString() {
    // return "InventoryOrderDTO{" +
    // "items=" + items +
    // ", id=" + id +
    // ", refOrderNo='" + refOrderNo + '\'' +
    // ", deliveryMarkState=" + deliveryMarkState +
    // ", receiptState=" + receiptState +
    // ", orgId=" + orgId +
    // ", orderType=" + orderType +
    // ", skuCount=" + skuCount +
    // ", warehouseId=" + warehouseId +
    // ", userName='" + userName + '\'' +
    // ", shopName='" + shopName + '\'' +
    // ", mobileNo='" + mobileNo + '\'' +
    // ", detailAddress='" + detailAddress + '\'' +
    // ", addressId=" + addressId +
    // ", province='" + province + '\'' +
    // ", city='" + city + '\'' +
    // ", county='" + county + '\'' +
    // ", street='" + street + '\'' +
    // ", parterId=" + parterId +
    // ", parterName='" + parterName + '\'' +
    // ", orderCreateTime=" + orderCreateTime +
    // ", deliveryOrderType=" + deliveryOrderType +
    // ", payableAmount=" + payableAmount +
    // ", orderAmount=" + orderAmount +
    // ", returnAmount=" + returnAmount +
    // ", businessType=" + businessType +
    // ", areaId=" + areaId +
    // ", areaName='" + areaName + '\'' +
    // ", routeId=" + routeId +
    // ", routeName='" + routeName + '\'' +
    // ", routeSequence=" + routeSequence +
    // ", fromCityId=" + fromCityId +
    // ", orderState=" + orderState +
    // ", deliveryMode=" + deliveryMode +
    // '}';
    // }

    /**
     * 获取订单在库存处理过程中的主键
     *
     * @return
     */
    public String getOrderInventoryIdentityKey(String messageType) {
        return messageType + "" + getId();
    }

    public boolean isYiKuanOrder() {
        return isYiKuanOrder;
    }

    public void setYiKuanOrder(boolean yiKuanOrder) {
        isYiKuanOrder = yiKuanOrder;
    }

    public Long getDeliveryOrderId() {
        return deliveryOrderId;
    }

    public void setDeliveryOrderId(Long deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getOrderInventoryIdentityKey(Integer warehouseId, String messageType) {
        return messageType + "" + warehouseId + getRefOrderNo();
    }

    public Long getOmsOrderId() {
        return omsOrderId;
    }

    public void setOmsOrderId(Long omsOrderId) {
        this.omsOrderId = omsOrderId;
    }

    public Integer getFromWarehouseId() {
        return fromWarehouseId;
    }

    public void setFromWarehouseId(Integer fromWarehouseId) {
        this.fromWarehouseId = fromWarehouseId;
    }

    public String getRelationOrderNo() {
        return relationOrderNo;
    }

    public void setRelationOrderNo(String relationOrderNo) {
        this.relationOrderNo = relationOrderNo;
    }
}
