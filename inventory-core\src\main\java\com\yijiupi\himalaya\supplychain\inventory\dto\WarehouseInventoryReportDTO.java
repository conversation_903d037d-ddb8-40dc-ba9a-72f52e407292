package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 仓库库存报表
 *
 * <AUTHOR>
 * @date 2019/4/26 18:10
 */
public class WarehouseInventoryReportDTO implements Serializable {

    private static final long serialVersionUID = -5152019430688598431L;

    /**
     * 城市Id
     */
    private Integer cityId;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 产品SkuID
     */
    private String productSkuId;
    /**
     * 产品名称.
     */
    private String productName;
    /**
     * 产品类目
     */
    private String statisticsClassName;
    /**
     * 库存所属人ID
     */
    private Long ownerId;
    /**
     * 库存所属人名称
     */
    private String ownerName;
    /**
     * 产品渠道
     */
    private Integer channel;
    /**
     * 产品渠道名称
     */
    private String channelName;
    /**
     * 产品规格
     */
    private String specName;
    /**
     * 包装单位（用于求余计算大小单位的数量）
     */
    private Integer specQuantity;
    /**
     * 大单位数量.
     */
    private BigDecimal packageCount;
    /**
     * 小单位数量.
     */
    private BigDecimal unitCount;
    /**
     * 大单位数量名称
     */
    private String packageCountName;
    /**
     * 小单位数量名称
     */
    private String unitCountName;
    /**
     * 库存id
     */
    private String storeId;
    /**
     * 仓库库存总量,按最小单位累计
     */
    private BigDecimal totalCountMinUnit;
    /**
     * 仓库库存
     */
    private String warehouseInventory;
    /**
     * 销售库存总量,按最小单位累计
     */
    private BigDecimal saleTotalCountMinUnit;
    /**
     * 销售库存
     */
    private String warehouseSaleInventory;
    /**
     * 销售库存
     */
    private String omsWarehouseSaleInventory;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    /**
     * oms销售库存数量
     */
    OmsInventoryInfo saleInventory;

    /**
     * oms待发货数量
     */
    OmsInventoryInfo waitDelivery;

    /**
     * 产品信息规格Id
     */
    private Long productInfoSpecificationId;

    /**
     * 产品信息规格Id
     */
    private Long productInfoId;

    /**
     * 二级货主名称
     */
    private String secOwnerName;
    private BigDecimal sellPackageCount;
    /**
     * 小单位数量.
     */
    private BigDecimal sellUnitCount;

    /**
     * 在途库存
     */
    private String allotDeliveryingCount;
    /**
     * 销售库存（数据库）
     */
    private BigDecimal saleInventoryCount;

    /**
     * 品牌名称
     */
    private String productBrand;

    public BigDecimal getSaleInventoryCount() {
        return saleInventoryCount;
    }

    public void setSaleInventoryCount(BigDecimal saleInventoryCount) {
        this.saleInventoryCount = saleInventoryCount;
    }

    public String getAllotDeliveryingCount() {
        return allotDeliveryingCount;
    }

    public void setAllotDeliveryingCount(String allotDeliveryingCount) {
        this.allotDeliveryingCount = allotDeliveryingCount;
    }

    public BigDecimal getSellPackageCount() {
        return sellPackageCount;
    }

    public void setSellPackageCount(BigDecimal sellPackageCount) {
        this.sellPackageCount = sellPackageCount;
    }

    public BigDecimal getSellUnitCount() {
        return sellUnitCount;
    }

    public void setSellUnitCount(BigDecimal sellUnitCount) {
        this.sellUnitCount = sellUnitCount;
    }

    public String getSecOwnerName() {
        return secOwnerName;
    }

    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    public Long getProductInfoSpecificationId() {
        return productInfoSpecificationId;
    }

    public void setProductInfoSpecificationId(Long productInfoSpecificationId) {
        this.productInfoSpecificationId = productInfoSpecificationId;
    }

    public Long getProductInfoId() {
        return productInfoId;
    }

    public void setProductInfoId(Long productInfoId) {
        this.productInfoId = productInfoId;
    }

    public OmsInventoryInfo getSaleInventory() {
        return saleInventory;
    }

    public void setSaleInventory(OmsInventoryInfo saleInventory) {
        this.saleInventory = saleInventory;
    }

    public OmsInventoryInfo getWaitDelivery() {
        return waitDelivery;
    }

    public void setWaitDelivery(OmsInventoryInfo waitDelivery) {
        this.waitDelivery = waitDelivery;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(String productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getStatisticsClassName() {
        return statisticsClassName;
    }

    public void setStatisticsClassName(String statisticsClassName) {
        this.statisticsClassName = statisticsClassName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public Integer getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(Integer specQuantity) {
        this.specQuantity = specQuantity;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public String getPackageCountName() {
        return packageCountName;
    }

    public void setPackageCountName(String packageCountName) {
        this.packageCountName = packageCountName;
    }

    public String getUnitCountName() {
        return unitCountName;
    }

    public void setUnitCountName(String unitCountName) {
        this.unitCountName = unitCountName;
    }

    public BigDecimal getTotalCountMinUnit() {
        return totalCountMinUnit;
    }

    public void setTotalCountMinUnit(BigDecimal totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }

    public String getWarehouseInventory() {
        return warehouseInventory;
    }

    public void setWarehouseInventory(String warehouseInventory) {
        this.warehouseInventory = warehouseInventory;
    }

    public BigDecimal getSaleTotalCountMinUnit() {
        return saleTotalCountMinUnit;
    }

    public void setSaleTotalCountMinUnit(BigDecimal saleTotalCountMinUnit) {
        this.saleTotalCountMinUnit = saleTotalCountMinUnit;
    }

    public String getWarehouseSaleInventory() {
        return warehouseSaleInventory;
    }

    public void setWarehouseSaleInventory(String warehouseSaleInventory) {
        this.warehouseSaleInventory = warehouseSaleInventory;
    }

    public String getOmsWarehouseSaleInventory() {
        return omsWarehouseSaleInventory;
    }

    public void setOmsWarehouseSaleInventory(String omsWarehouseSaleInventory) {
        this.omsWarehouseSaleInventory = omsWarehouseSaleInventory;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }
}
