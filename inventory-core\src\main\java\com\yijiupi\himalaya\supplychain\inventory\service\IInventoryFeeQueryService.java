package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryFeeDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryFeeQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryStorageDTO;

/**
 * 库存费用查询
 * 
 * <AUTHOR> 2018/2/2
 */
public interface IInventoryFeeQueryService {

    /**
     * 配送费查询,托管费查询
     * 
     * @param inventoryFeeQueryDTOS
     * @return
     */
    InventoryFeeDTO findInventoryFee(List<InventoryFeeQueryDTO> inventoryFeeQueryDTOS);

    /**
     * 通过订单号查询存储天数
     */
    Map<String, List<InventoryStorageDTO>> findInventoryStorage(List<String> orderNos);
}
