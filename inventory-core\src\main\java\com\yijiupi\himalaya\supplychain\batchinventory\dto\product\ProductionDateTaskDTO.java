package com.yijiupi.himalaya.supplychain.batchinventory.dto.product;

import java.io.Serializable;

/**
 * 产日期治理任务
 *
 * <AUTHOR>
 * @Date 2025/2/8
 */
public class ProductionDateTaskDTO implements Serializable {

    /**
     * warehouseId
     */
    private Integer warehouseId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 生产日期
     */
    private String productionDate;


    /**
     * 批次库存id
     */
    private String storeBatchId;

    public String getStoreBatchId() {
        return storeBatchId;
    }

    public void setStoreBatchId(String storeBatchId) {
        this.storeBatchId = storeBatchId;
    }


    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(String productionDate) {
        this.productionDate = productionDate;
    }
}
