package com.yijiupi.himalaya.supplychain.inventory.dto.instock;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 易款店仓安全库存变更对象
 *
 * <AUTHOR>
 * @date 2020-03-10 15:54
 */
public class EasyChainSafetyInventoryChangeMessageDTO implements Serializable {

    private static final long serialVersionUID = 2258053144844655999L;

    /**
     * 仓库Id
     */
    private Integer warehouseId;

    /**
     * 产品信息规格ID
     */
    private Long productSpecificationId;

    /**
     * 库存变更数量(小单位)
     */
    private BigDecimal totalCount;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }
}
