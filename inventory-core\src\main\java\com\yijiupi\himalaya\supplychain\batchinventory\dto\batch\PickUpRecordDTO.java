package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.util.List;

public class PickUpRecordDTO implements Serializable {

    /**
     * 拣货信息对应变更记录
     */
    private PickUpChangeRecordDTO pickUpChangeRecordDTO;

    /**
     * 拣货信息
     */
    private List<PickUpDTO> pickUpDTOList;

    public PickUpChangeRecordDTO getPickUpChangeRecordDTO() {
        return pickUpChangeRecordDTO;
    }

    public void setPickUpChangeRecordDTO(PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        this.pickUpChangeRecordDTO = pickUpChangeRecordDTO;
    }

    public List<PickUpDTO> getPickUpDTOList() {
        return pickUpDTOList;
    }

    public void setPickUpDTOList(List<PickUpDTO> pickUpDTOList) {
        this.pickUpDTOList = pickUpDTOList;
    }
}
