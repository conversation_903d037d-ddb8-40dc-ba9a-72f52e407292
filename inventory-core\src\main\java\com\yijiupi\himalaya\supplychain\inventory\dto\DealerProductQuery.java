package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 经销商产品查询数据
 * 
 * @author: lidengfeng
 * @date 2018/9/28 16:49
 */
public class DealerProductQuery implements Serializable {

    /**
     * 经销商id
     */
    private Long dealerId;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 服务商id集合
     */
    private List<Long> facilitatorIdList;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页数
     */
    private Integer pageSize;

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public Long getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(Long dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取 服务商id集合
     * 
     * @return
     */
    public List<Long> getFacilitatorIdList() {
        return facilitatorIdList;
    }

    /**
     * 设置 服务商id集合
     * 
     * @param facilitatorIdList
     */
    public void setFacilitatorIdList(List<Long> facilitatorIdList) {
        this.facilitatorIdList = facilitatorIdList;
    }

    /**
     * 获取 城市id
     * 
     * @return
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市id
     * 
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getFacilitatorId() {
        return facilitatorId;
    }

    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }
}
