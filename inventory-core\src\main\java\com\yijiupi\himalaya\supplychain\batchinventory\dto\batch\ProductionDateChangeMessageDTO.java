package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.util.Date;

public class ProductionDateChangeMessageDTO implements Serializable {

    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 产品信息规格ID
     */
    private Long productSpecificationId;
    /**
     * 库存所属人ID
     */
    private Long ownerId;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 生产日期
     */
    private Date productionDate;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }
}
