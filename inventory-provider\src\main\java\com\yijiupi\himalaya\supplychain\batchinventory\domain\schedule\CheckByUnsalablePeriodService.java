package com.yijiupi.himalaya.supplychain.batchinventory.domain.schedule;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.assignment.dto.todo.UnsalableProductTaskDTO;
import com.yijiupi.himalaya.assignment.service.IUnsalableProductTaskService;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryQueryBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.PromotionStoreBatchBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryMoveDTO;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckParam;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckResultDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.CategoryPeriodConfigTypeEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.InStockAlarmEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IInStockConfigService;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.WarehouseClassEnum;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

/**
 * 创建绝对滞销产品治理任务
 *
 * <AUTHOR>
 * @since 2025/5/23
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class CheckByUnsalablePeriodService {

    @Reference(timeout = 60000)
    private IWarehouseQueryService warehouseQueryService;

    @Reference(timeout = 120000)
    private IInStockConfigService iInStockConfigService;

    @Reference(timeout = 60000)
    private IProductSkuConfigService iProductSkuConfigService;

    @Reference(timeout = 60000)
    private IUnsalableProductTaskService iUnsalableProductTaskService;

    @Autowired
    private BatchInventoryQueryBL batchInventoryQueryBL;

    @Autowired
    private PromotionStoreBatchBL promotionStoreBatchBL;

    private static final Logger logger = LoggerFactory.getLogger(CheckByUnsalablePeriodService.class);

    private static final List<Integer> EXCLUDE_WAREHOUSE_ID_List =
        Arrays.asList(1411, 4741, 7421, 1341, 7461, 4401, 1391, 4461, 4591, 4331, 1421, 7431, 1531, 4481, 7481, 1471,
            7251, 5002, 1351, 7501, 1301, 1381, 1431, 1481, 4361, 7271, 4501, 1461, 7291, 1451, 1511, 1401, 7381, 9981,
            1331, 4551, 7471, 4161, 4071, 4311, 1371, 1541, 7411, 7491, 4371, 7311, 7401, 1281, 7321, 7301, 7281, 4541,
            7441, 1171, 7451, 1335, 4115, 1361, 7351, 4111, 1189, 4451, 4381, 1551, 1441, 1181, 1001, 1121, 1201, 1261,
            1611, 1651, 1681, 1721, 1741, 1851, 2091, 4031, 4091, 4601, 4811, 4871, 4971, 5031, 7001, 7041, 7061, 7161,
            9000602, 9000591, 9000601, 9000551, 9000483, 9000612, 9000598, 9000634, 9000755, 9000803);

    /**
     * 每天执行一次
     */
    @XxlJob("checkProductByUnsalablePeriod")
    public void checkProductByUnsalablePeriod() {
        logger.info("[创建绝对滞销产品治理任务]开始");
        List<Warehouse> warehouseList = new ArrayList<>();
        String warehouseIdStr = XxlJobContext.getXxlJobContext().getJobParam();
        if (StringUtils.isNotEmpty(warehouseIdStr) && !Objects.equals(warehouseIdStr, "{}")) {
            List<Integer> warehouseIdList = Arrays.stream(warehouseIdStr.split("、"))
                .filter(p -> !StringUtils.isEmpty(p) && StringUtils.isNumeric(p)).map(p -> Integer.valueOf(p))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(warehouseIdList)) {
                warehouseList = warehouseQueryService.listWarehouseByIds(warehouseIdList);
            }
        } else {
            // 已启用仓库
            // 城市仓库((byte) 0)
            warehouseList = warehouseQueryService.listEnableWarehouseByTypes(Arrays.asList(0)).stream()
                .filter(p -> p != null && p.getCityId() != null && p.getCityId() >= 100 && p.getCityId() <= 900
                    && !Objects.equals(WarehouseClassEnum.虚仓.getType(), p.getWarehouseClass())
                    && !EXCLUDE_WAREHOUSE_ID_List.contains(p.getId()))
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(warehouseList)) {
            logger.info("[创建绝对滞销产品治理任务]没有查询到任何仓库");
            return;
        }

        for (Warehouse warehouseElem : warehouseList) {
            try {
                checkByUnsalablePeriod(warehouseElem);
            } catch (Exception ex) {
                logger.error("[创建绝对滞销产品治理任务]仓库名：{}，仓库ID：{}，获取仓库绝对滞销期产品数据异常", warehouseElem.getName(),
                    warehouseElem.getId(), ex);
            }
        }
        logger.info("[创建绝对滞销产品治理任务]结束");
    }

    /**
     * 获取单个仓库的绝对滞销期产品数据
     */
    private void checkByUnsalablePeriod(Warehouse warehouseElem) {
        Integer warehouseId = warehouseElem.getId();
        // 1.查询仓库所有sku（有库存、有或无生产日期）
        List<Long> skuIdList = batchInventoryQueryBL.listSkuIdByAllBatchInventory(warehouseId);
        if (CollectionUtils.isEmpty(skuIdList)) {
            logger.info("[创建绝对滞销产品治理任务]仓库名：{}，仓库ID：{}，没有有库存的产品", warehouseElem.getName(), warehouseId);
            return;
        }
        logger.info("[创建绝对滞销产品治理任务]仓库名：{}，仓库ID：{}，有库存的产品个数：{}", warehouseElem.getName(), warehouseId, skuIdList.size());

        // 2.根据类目绝对滞销期配置，查询仓库的绝对滞销期产品数量
        List<OutStockConfigCheckResultDTO> checkResults = new ArrayList<>();
        List<List<Long>> skuIdsPart = Lists.partition(skuIdList, 2000);
        for (List<Long> skuIdListElem : skuIdsPart) {
            List<OutStockConfigCheckDTO> checkProductDTOList = new ArrayList<>();
            skuIdListElem.forEach(skuIdElem -> {
                OutStockConfigCheckDTO checkElem = new OutStockConfigCheckDTO();
                checkElem.setSkuId(skuIdElem);
                checkElem.setChannel(ProductChannelType.JIUPI);
                checkElem.setSource(ProductSourceType.易酒批);
                checkProductDTOList.add(checkElem);
            });

            OutStockConfigCheckParam configCheckDTO = new OutStockConfigCheckParam();
            configCheckDTO.setWarehouseId(warehouseId);
            configCheckDTO.setCheckProductDTOList(checkProductDTOList);
            configCheckDTO.setExcludeSubcategoryList(
                Arrays.asList(LocationEnum.残次品位.getType().byteValue(), LocationAreaEnum.残次品区.getType().byteValue()));
            configCheckDTO.setCheckAllStoreFlag(true);
            configCheckDTO.setCalStorageAttribute(true);
            configCheckDTO.setCategoryPeriodConfigType(CategoryPeriodConfigTypeEnum.UNSALABLE_PERIOD.getValue());
            List<OutStockConfigCheckResultDTO> results =
                iInStockConfigService.checkByCategoryPeriodConfigType(configCheckDTO).stream()
                    .filter(elem -> elem != null && Objects.equals(elem.getAlarm(), InStockAlarmEnum.入库禁止.getType())
                        && elem.getStorageAttribute() != null)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(results)) {
                continue;
            }
            checkResults.addAll(results);
        }

        if (CollectionUtils.isEmpty(checkResults)) {
            logger.info("[创建绝对滞销产品治理任务]仓库名：{}，仓库ID：{}，仓库无绝对滞销期产品", warehouseElem.getName(), warehouseId);
            return;
        }

        // 更新产品配置是否绝对滞销状态
        updateSkuConfigBatch(warehouseId, checkResults);
        // 转入残次品代办任务
        createTodoTask(warehouseId, checkResults);
    }

    private void updateSkuConfigBatch(Integer warehouseId, List<OutStockConfigCheckResultDTO> checkResults) {
        // 通知创建绝对滞销产品治理品任务
        List<ProductSkuConfigDTO> updadteConfigDTOS = checkResults.stream().map(p -> {
            ProductSkuConfigDTO configDTO = new ProductSkuConfigDTO();
            configDTO.setWarehouseId(warehouseId);
            configDTO.setProductSkuId(p.getSkuId());
            configDTO.setIsUnsalable(YesOrNoEnum.YES.getValue().byteValue());
            return configDTO;
        }).collect(Collectors.toList());
        logger.info("[创建绝对滞销产品治理任务]更新产品配置参数：{}", JSON.toJSONString(updadteConfigDTOS));
        iProductSkuConfigService.updateSkuConfigIsUnsalable(updadteConfigDTOS);
    }

    private void createTodoTask(Integer warehouseId, List<OutStockConfigCheckResultDTO> checkResults) {
        // 通知创建绝对滞销产品治理品任务
        List<UnsalableProductTaskDTO> taskDTOS = checkResults.stream().map(p -> {
            UnsalableProductTaskDTO taskDTO = new UnsalableProductTaskDTO();
            taskDTO.setWarehouseId(warehouseId);
            taskDTO.setSkuId(p.getSkuId());
            taskDTO.setProductName(p.getProductName());
            taskDTO.setTaskProperty(p.getStorageAttribute().intValue());
            taskDTO.setUnsalableDays(
                StringUtils.isNotEmpty(p.getPeriodConfig()) && StringUtils.isNumeric(p.getPeriodConfig())
                    ? Integer.valueOf(p.getPeriodConfig()) : null);
            return taskDTO;
        }).collect(Collectors.toList());
        logger.info("[创建绝对滞销产品治理任务]创建参数：{}", JSON.toJSONString(taskDTOS));
        iUnsalableProductTaskService.createTask(taskDTOS);
    }

    /**
     * 自动转残次品
     * 
     * @param warehouseId
     * @param checkResults
     */
    private void transferToDefective(Integer warehouseId, List<OutStockConfigCheckResultDTO> checkResults) {
        if (CollectionUtils.isEmpty(checkResults)) {
            return;
        }

        List<String> storeBatchIdList =
            checkResults.stream().filter(p -> p != null && !StringUtils.isEmpty(p.getStoreBatchId()))
                .map(p -> p.getStoreBatchId()).distinct().collect(Collectors.toList());
        List<List<String>> storeBatchIdsPart = Lists.partition(storeBatchIdList, 500);
        for (List<String> storeBatchIds : storeBatchIdsPart) {
            BatchInventoryMoveDTO moveDTO = new BatchInventoryMoveDTO();
            moveDTO.setWarehouseId(warehouseId);
            moveDTO.setStoreBatchIds(storeBatchIds);
            moveDTO.setAutoMoveFlag(true);
            promotionStoreBatchBL.storeBatchTransferToDefective(moveDTO);
        }
    }
}
