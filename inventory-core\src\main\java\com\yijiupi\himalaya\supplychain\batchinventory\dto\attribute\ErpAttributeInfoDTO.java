package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import java.io.Serializable;
import java.util.List;

/**
 * 批属性信息表
 *
 * <AUTHOR> 2018/4/10
 */
public class ErpAttributeInfoDTO implements Serializable {

    /**
     * ERP传递的批属性信息
     */
    private List<BatchAttributeInfoDTO> items;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * '货位名称'
     */
    private String locationName;

    private Integer locationCategory;

    private Integer subcategory;

    public Integer getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    public Integer getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(Integer subcategory) {
        this.subcategory = subcategory;
    }

    public List<BatchAttributeInfoDTO> getItems() {
        return items;
    }

    public void setItems(List<BatchAttributeInfoDTO> items) {
        this.items = items;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
