package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @author: lidengfeng
 * @date 2019/1/15 16:02
 */
public class ProductChargeQuery implements Serializable {

    /**
     * 规格参数id集合
     */
    private List<Long> productSpecificationIdList;

    /**
     * 经销商id
     */
    private Long dealerId;

    /**
     * 仓库di
     */
    private Integer warehouseId;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 服务商id集合
     */
    private List<Long> facilitatorIdList;

    /**
     * 获取 规格参数id集合
     * 
     * @return
     */
    public List<Long> getProductSpecificationIdList() {
        return productSpecificationIdList;
    }

    /**
     * 设置 规格参数id集合
     * 
     * @param productSpecificationIdList
     */
    public void setProductSpecificationIdList(List<Long> productSpecificationIdList) {
        this.productSpecificationIdList = productSpecificationIdList;
    }

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public Long getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(Long dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    /**
     * 获取 服务商id集合
     * 
     * @return
     */
    public List<Long> getFacilitatorIdList() {
        return facilitatorIdList;
    }

    /**
     * 设置 服务商id集合
     * 
     * @param facilitatorIdList
     */
    public void setFacilitatorIdList(List<Long> facilitatorIdList) {
        this.facilitatorIdList = facilitatorIdList;
    }
}
