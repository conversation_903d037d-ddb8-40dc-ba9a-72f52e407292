package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 待出库订单的产品
 *
 * <AUTHOR>
 * @date 2019/4/15 19:20
 */
public class ProductWaitDeliveryDTO implements Serializable {

    private static final long serialVersionUID = 3985342220616857155L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 包装规格大单位
     */
    private String packageName;

    /**
     * 包装规格小单位
     */
    private String unitName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 商品特征:1:大件,2:小件
     */
    private Byte productFeature;

    /**
     * 是否拣货 0:否 1:是
     */
    private Byte pick;

    /**
     * 是否播种 0:否 1:是
     */
    private Byte sow;

    /**
     * 保存条件 0:常温 1:冷藏 2:冷冻
     */
    private Byte storageType;

    /**
     * 库存数量
     */
    private BigDecimal storeTotalUnitCount;

    /**
     * 库存大数量
     */
    private BigDecimal storePackageCount;

    /**
     * 库存小数量
     */
    private BigDecimal storeUnitCount;

    /**
     * 待出库数量
     */
    private BigDecimal deliveryTotalUnitCount;

    /**
     * 待出库大数量
     */
    private BigDecimal deliveryPackageCount;

    /**
     * 待出库小数量
     */
    private BigDecimal deliveryUnitCount;

    /**
     * 可用数量
     */
    private BigDecimal enableTotalUnitCount;

    /**
     * 可用大数量
     */
    private BigDecimal enablePackageCount;

    /**
     * 可用小数量
     */
    private BigDecimal enableUnitCount;

    /**
     * 未分配数量
     */
    private BigDecimal notAllotTotalUnitCount;

    /**
     * 未分配大数量
     */
    private BigDecimal notAllotPackageCount;

    /**
     * 未分配小数量
     */
    private BigDecimal notAllotUnitCount;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Byte getProductFeature() {
        return productFeature;
    }

    public void setProductFeature(Byte productFeature) {
        this.productFeature = productFeature;
    }

    public Byte getPick() {
        return pick;
    }

    public void setPick(Byte pick) {
        this.pick = pick;
    }

    public Byte getSow() {
        return sow;
    }

    public void setSow(Byte sow) {
        this.sow = sow;
    }

    public BigDecimal getStoreTotalUnitCount() {
        return storeTotalUnitCount;
    }

    public void setStoreTotalUnitCount(BigDecimal storeTotalUnitCount) {
        this.storeTotalUnitCount = storeTotalUnitCount;
    }

    public BigDecimal getStorePackageCount() {
        return storePackageCount;
    }

    public void setStorePackageCount(BigDecimal storePackageCount) {
        this.storePackageCount = storePackageCount;
    }

    public BigDecimal getStoreUnitCount() {
        return storeUnitCount;
    }

    public void setStoreUnitCount(BigDecimal storeUnitCount) {
        this.storeUnitCount = storeUnitCount;
    }

    public BigDecimal getDeliveryTotalUnitCount() {
        return deliveryTotalUnitCount;
    }

    public void setDeliveryTotalUnitCount(BigDecimal deliveryTotalUnitCount) {
        this.deliveryTotalUnitCount = deliveryTotalUnitCount;
    }

    public BigDecimal getDeliveryPackageCount() {
        return deliveryPackageCount;
    }

    public void setDeliveryPackageCount(BigDecimal deliveryPackageCount) {
        this.deliveryPackageCount = deliveryPackageCount;
    }

    public BigDecimal getDeliveryUnitCount() {
        return deliveryUnitCount;
    }

    public void setDeliveryUnitCount(BigDecimal deliveryUnitCount) {
        this.deliveryUnitCount = deliveryUnitCount;
    }

    public Byte getStorageType() {
        return storageType;
    }

    public void setStorageType(Byte storageType) {
        this.storageType = storageType;
    }

    public BigDecimal getEnableTotalUnitCount() {
        return enableTotalUnitCount;
    }

    public void setEnableTotalUnitCount(BigDecimal enableTotalUnitCount) {
        this.enableTotalUnitCount = enableTotalUnitCount;
    }

    public BigDecimal getEnablePackageCount() {
        return enablePackageCount;
    }

    public void setEnablePackageCount(BigDecimal enablePackageCount) {
        this.enablePackageCount = enablePackageCount;
    }

    public BigDecimal getEnableUnitCount() {
        return enableUnitCount;
    }

    public void setEnableUnitCount(BigDecimal enableUnitCount) {
        this.enableUnitCount = enableUnitCount;
    }

    public BigDecimal getNotAllotTotalUnitCount() {
        return notAllotTotalUnitCount;
    }

    public void setNotAllotTotalUnitCount(BigDecimal notAllotTotalUnitCount) {
        this.notAllotTotalUnitCount = notAllotTotalUnitCount;
    }

    public BigDecimal getNotAllotPackageCount() {
        return notAllotPackageCount;
    }

    public void setNotAllotPackageCount(BigDecimal notAllotPackageCount) {
        this.notAllotPackageCount = notAllotPackageCount;
    }

    public BigDecimal getNotAllotUnitCount() {
        return notAllotUnitCount;
    }

    public void setNotAllotUnitCount(BigDecimal notAllotUnitCount) {
        this.notAllotUnitCount = notAllotUnitCount;
    }
}
