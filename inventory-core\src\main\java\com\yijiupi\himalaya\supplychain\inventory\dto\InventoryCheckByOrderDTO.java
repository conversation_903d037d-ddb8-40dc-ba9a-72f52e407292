package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 根据订单矫正仓库库存
 *
 * <AUTHOR>
 * @date 11/28/20 2:52 PM
 */
public class InventoryCheckByOrderDTO implements Serializable {

    private static final long serialVersionUID = 608833642825137483L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 单据类型
     */
    private Integer orderType;

    /**
     * 订单项集合
     */
    private List<InventoryCheckByOrderItemDTO> itemList;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public List<InventoryCheckByOrderItemDTO> getItemList() {
        return itemList;
    }

    public void setItemList(List<InventoryCheckByOrderItemDTO> itemList) {
        this.itemList = itemList;
    }
}
