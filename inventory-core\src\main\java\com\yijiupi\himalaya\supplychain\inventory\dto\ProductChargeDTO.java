package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品收费配置DTO
 * 
 * @author: lidengfeng
 * @date 2018/9/25 15:36
 */
public class ProductChargeDTO implements Serializable {

    /**
     * 产品规格id
     */
    private Long productSpecificationId;

    /**
     * 产品skuid
     */
    private Long productSkuId;

    /**
     * 大单位数量
     */
    private BigDecimal packageNum;

    /**
     * 小单位数量
     */
    private BigDecimal unitNum;

    /**
     * 转换系数
     */
    private Integer packageQuantity;

    /**
     * 产品收费的数量
     */
    private BigDecimal num;

    /**
     * 获取 规格id
     * 
     * @return
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 规格id
     * 
     * @param productSpecificationId
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 大单位数量
     * 
     * @return
     */
    public BigDecimal getPackageNum() {
        return packageNum;
    }

    /**
     * 设置 大单位数量
     * 
     * @param packageNum
     */
    public void setPackageNum(BigDecimal packageNum) {
        this.packageNum = packageNum;
    }

    /**
     * 获取小单位数量
     * 
     * @return
     */
    public BigDecimal getUnitNum() {
        return unitNum;
    }

    /**
     * 设置小单位数量
     * 
     * @param unitNum
     */
    public void setUnitNum(BigDecimal unitNum) {
        this.unitNum = unitNum;
    }

    /**
     * 获取 规格转换系数
     * 
     * @return
     */
    public Integer getPackageQuantity() {
        return packageQuantity;
    }

    /**
     * 设置 规格转换系数
     * 
     * @param packageQuantity
     */
    public void setPackageQuantity(Integer packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 收费的数量
     * 
     * @return
     */
    public BigDecimal getNum() {
        return num;
    }

    /**
     * 设置 收费的数量
     * 
     * @param num
     */
    public void setNum(BigDecimal num) {
        this.num = num;
    }

    /**
     * 获取 产品skuid
     * 
     * @return
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    /**
     * 设置产品skuid
     * 
     * @param productSkuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }
}
