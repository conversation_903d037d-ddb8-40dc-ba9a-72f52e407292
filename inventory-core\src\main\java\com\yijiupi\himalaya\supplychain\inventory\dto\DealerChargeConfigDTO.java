/*
 * @ClassName DealerChargeConfigPODTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2018-11-28 19:14:09
 */
package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.Date;

public class DealerChargeConfigDTO implements Serializable {
    /**
     * @Fields id 编号
     */
    private String id;
    /**
     * @Fields dealerId 经销商Id
     */
    private String dealerId;
    /**
     * @Fields dealerName 经销商名称
     */
    private String dealerName;
    /**
     * @Fields isGetWarehouseCharge 是否收取仓配费用 1为收取 0为不收取
     */
    private Byte isGetWarehouseCharge;
    /**
     * @Fields businessType 业务类型 0=正常业务 1=贷款业务
     */
    private Byte businessType;
    /**
     * @Fields status 状态 0=停用 1=启用
     */
    private Byte status;
    /**
     * @Fields facilitatorId 服务商id
     */
    private Long facilitatorId;
    /**
     * @Fields facilitatorName 服务商名称
     */
    private String facilitatorName;
    /**
     * @Fields createUser 创建人
     */
    private Long createUser;
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private Long lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后更新时间
     */
    private Date lastUpdateTime;
    /**
     * @Fields mobileNo 经销商手机号
     */
    private String mobileNo;
    /**
     * @Fields firstServiceTime 首次服务时间
     */
    private Date firstServiceTime;

    /**
     * 获取 编号
     */
    public String getId() {
        return id;
    }

    /**
     * 设置 编号
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 经销商Id
     */
    public String getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商Id
     */
    public void setDealerId(String dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取 经销商名称
     */
    public String getDealerName() {
        return dealerName;
    }

    /**
     * 设置 经销商名称
     */
    public void setDealerName(String dealerName) {
        this.dealerName = dealerName == null ? null : dealerName.trim();
    }

    /**
     * 获取 是否收取仓配费用 1为收取 0为不收取
     */
    public Byte getIsGetWarehouseCharge() {
        return isGetWarehouseCharge;
    }

    /**
     * 设置 是否收取仓配费用 1为收取 0为不收取
     */
    public void setIsGetWarehouseCharge(Byte isGetWarehouseCharge) {
        this.isGetWarehouseCharge = isGetWarehouseCharge;
    }

    /**
     * 获取 业务类型 0=正常业务 1=贷款业务
     */
    public Byte getBusinessType() {
        return businessType;
    }

    /**
     * 设置 业务类型 0=正常业务 1=贷款业务
     */
    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取 状态 0=停用 1=启用
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置 状态 0=停用 1=启用
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取 服务商id
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    /**
     * 获取 服务商名称
     */
    public String getFacilitatorName() {
        return facilitatorName;
    }

    /**
     * 设置 服务商名称
     */
    public void setFacilitatorName(String facilitatorName) {
        this.facilitatorName = facilitatorName == null ? null : facilitatorName.trim();
    }

    /**
     * 获取 创建人
     */
    public Long getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public Long getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(Long lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 获取 最后更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * 获取 经销商手机号
     */
    public String getMobileNo() {
        return mobileNo;
    }

    /**
     * 设置 经销商手机号
     */
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    /**
     * 获取 首次服务时间
     */
    public Date getFirstServiceTime() {
        return firstServiceTime;
    }

    /**
     * 设置 首次服务时间
     */
    public void setFirstServiceTime(Date firstServiceTime) {
        this.firstServiceTime = firstServiceTime;
    }
}