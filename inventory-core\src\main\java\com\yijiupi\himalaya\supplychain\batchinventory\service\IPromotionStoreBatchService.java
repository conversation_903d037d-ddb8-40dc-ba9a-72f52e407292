package com.yijiupi.himalaya.supplychain.batchinventory.service;

import java.util.List;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryMoveDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.*;

/**
 * 促销批次库存
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
public interface IPromotionStoreBatchService {

    /**
     * 获取存在促销批次库存产品数据
     *
     * @param queryDTO
     * @return
     */
    List<PromotionStoreBatchResultDTO> listPromotionStoreBatchProduct(PromotionStoreBatchQueryDTO queryDTO);

    /**
     * 获取产品是否混合批次库存标识
     *
     * @param queryDTO
     * @return
     */
    List<PromotionStoreBatchResultDTO> listProductMixedBatchFlag(PromotionStoreBatchQueryDTO queryDTO);

    /**
     * 批量新增促销批次库存
     *
     * @param addDTOS
     * @return
     */
    void insertBatch(List<ProductPromotionStoreBatchDTO> addDTOS);

    /**
     * 获取产品是否混合批次库存标识
     *
     * @param ids
     * @return
     */
    void batchDeleteByIds(List<Long> ids);

    /**
     * 条件查询促销批次库存
     *
     * @param queryDTO
     * @return
     */
    List<ProductPromotionStoreBatchDTO> queryByCondition(PromotionStoreBatchQueryDTO queryDTO);

    /**
     * 根据批次编号检查删除促销批次库存
     *
     * @param deleteDTO
     * @return
     */
    void deleteByBatchAttributeInfoNo(ProductPromotionStoreBatchDTO deleteDTO);

    /**
     * 入库促销货位检查
     *
     * @param checkDTOS
     * @return
     */
    void checkInStockLocation(List<PromotionLocationCheckDTO> checkDTOS);

    /**
     * 根据批次库存批量新增促销批次库存记录,并判断是否新增关联货位代办
     *
     * @param addDTO
     * @return
     */
    void batchInsertByStoreBatchIds(ProductPromotionStoreBatchDTO addDTO);

    /**
     * 自动转残次品
     *
     * @param moveDTO
     * @return
     */
    void storeBatchTransferToDefective(BatchInventoryMoveDTO moveDTO);

    /**
     * 交易促销活动同步
     *
     * @param message
     * @return
     */
    void synTrdPromotion(TrdPromotionSyncMessage message);

    /**
     * 获取存在促销产品批次库存信息
     *
     * @param queryDTO
     * @return
     */
    List<PromotionStoreBatchResultDTO> listPromotionStoreBatchNoGroup(PromotionStoreBatchQueryDTO queryDTO);
}
