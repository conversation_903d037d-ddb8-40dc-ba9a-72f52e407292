package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import java.io.Serializable;
import java.util.List;

public class RuleRelationQueryDTO implements Serializable {

    private static final long serialVersionUID = -3976950579085937461L;

    /**
     * 配置类型 适用服务商(0),适用仓库(1),适用货主(2),适用类名(3),适用品牌(4)
     */
    private List<Byte> ruleTypeList;
    /**
     * 属性值Id
     */
    private List<String> attributeValueIdList;

    /**
     * 属性值名称
     */
    private List<String> attributeValueNameList;

    public List<Byte> getRuleTypeList() {
        return ruleTypeList;
    }

    public void setRuleTypeList(List<Byte> ruleTypeList) {
        this.ruleTypeList = ruleTypeList;
    }

    public List<String> getAttributeValueIdList() {
        return attributeValueIdList;
    }

    public void setAttributeValueIdList(List<String> attributeValueIdList) {
        this.attributeValueIdList = attributeValueIdList;
    }

    public List<String> getAttributeValueNameList() {
        return attributeValueNameList;
    }

    public void setAttributeValueNameList(List<String> attributeValueNameList) {
        this.attributeValueNameList = attributeValueNameList;
    }
}
