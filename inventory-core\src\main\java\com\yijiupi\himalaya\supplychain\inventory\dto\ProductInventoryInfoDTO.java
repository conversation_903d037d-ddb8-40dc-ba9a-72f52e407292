package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存信息dto
 * 
 * <AUTHOR> 2017/12/22
 */
public class ProductInventoryInfoDTO implements Serializable {

    /**
     * 主键ID
     */
    private String id;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 库存总量,按最小单位累计
     */
    private BigDecimal totalCountMinUnit;
    /**
     * 库存所属类型
     */
    private Integer ownerType;
    /**
     * 库存所属人ID
     */
    private Long ownerId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
    /**
     * 产品信息规格ID
     */
    private Long productSpecificationId;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * skuid
     */
    private Long productSkuId;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    private String ownerName;

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    /**
     * 获取 主键ID
     *
     * @return id 主键ID
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 主键ID
     *
     * @param id 主键ID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 仓库ID
     *
     * @return warehouseId 仓库ID
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库ID
     *
     * @param warehouseId 仓库ID
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 库存总量按最小单位累计
     *
     * @return totalCountMinUnit 库存总量按最小单位累计
     */
    public BigDecimal getTotalCountMinUnit() {
        return this.totalCountMinUnit;
    }

    /**
     * 设置 库存总量按最小单位累计
     *
     * @param totalCountMinUnit 库存总量按最小单位累计
     */
    public void setTotalCountMinUnit(BigDecimal totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }

    /**
     * 获取 库存所属类型
     *
     * @return ownerType 库存所属类型
     */
    public Integer getOwnerType() {
        return this.ownerType;
    }

    /**
     * 设置 库存所属类型
     *
     * @param ownerType 库存所属类型
     */
    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    /**
     * 获取 库存所属人ID
     *
     * @return ownerId 库存所属人ID
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置 库存所属人ID
     *
     * @param ownerId 库存所属人ID
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 创建时间
     *
     * @return createTime 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新时间
     *
     * @return lastUpdateTime 最后更新时间
     */
    public Date getLastUpdateTime() {
        return this.lastUpdateTime;
    }

    /**
     * 设置 最后更新时间
     *
     * @param lastUpdateTime 最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * 获取 产品信息规格ID
     *
     * @return productSpecificationId 产品信息规格ID
     */
    public Long getProductSpecificationId() {
        return this.productSpecificationId;
    }

    /**
     * 设置 产品信息规格ID
     *
     * @param productSpecificationId 产品信息规格ID
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 城市id
     *
     * @return cityId 城市id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市id
     *
     * @param cityId 城市id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 skuid
     *
     * @return productSkuId skuid
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 skuid
     *
     * @param productSkuId skuid
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 库存渠道0:酒批，1:大宗产品
     *
     * @return channel 库存渠道0:酒批，1:大宗产品
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 库存渠道0:酒批，1:大宗产品
     *
     * @param channel 库存渠道0:酒批，1:大宗产品
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 包装规格名称
     *
     * @return specificationName 包装规格名称
     */
    public String getSpecificationName() {
        return this.specificationName;
    }

    /**
     * 设置 包装规格名称
     *
     * @param specificationName 包装规格名称
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    /**
     * 获取 包装规格大单位
     *
     * @return packageName 包装规格大单位
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 包装规格大单位
     *
     * @param packageName 包装规格大单位
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 包装规格小单位
     *
     * @return unitName 包装规格小单位
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 包装规格小单位
     *
     * @param unitName 包装规格小单位
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 包装规格大小单位转换系数
     *
     * @return packageQuantity 包装规格大小单位转换系数
     */
    public BigDecimal getPackageQuantity() {
        return this.packageQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     *
     * @param packageQuantity 包装规格大小单位转换系数
     */
    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @return source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @param source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 二级货主Id
     *
     * @return secOwnerId 二级货主Id
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主Id
     *
     * @param secOwnerId 二级货主Id
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }
}
