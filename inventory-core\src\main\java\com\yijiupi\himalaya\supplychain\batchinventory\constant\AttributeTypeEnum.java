package com.yijiupi.himalaya.supplychain.batchinventory.constant;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

public enum AttributeTypeEnum {
    /**
     * 文本框
     */
    文本框((byte)1),
    /**
     * 下拉框
     */
    下拉框((byte)2),
    /**
     * 复选框
     */
    复选框((byte)3),
    /**
     * 日期
     */
    日期((byte)4),
    /**
     * 日期时间
     */
    日期时间((byte)5),
    /**
     * 数字
     */
    数字((byte)6),
    /**
     * 系统表
     */
    系统表((byte)7);

    private Byte value;

    AttributeTypeEnum(Byte value) {
        this.value = value;
    }

    /**
     * 根据枚举值获取枚举对象名称
     *
     * @param value
     * @return
     */
    public static String getEnmuName(Byte value) {
        String name = null;
        if (value != null) {
            name = cache.get(value);
        }
        return name;
    }

    private static Map<Byte, String> cache =
        EnumSet.allOf(AttributeTypeEnum.class).stream().collect(Collectors.toMap(p -> p.value, p -> p.name()));

    public Byte getValue() {
        return value;
    }

    public void setValue(Byte value) {
        this.value = value;
    }
}
