package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 未开启货位库存盘点变更批次库存DTO
 */
public class StoreCheckUpdateBatchInventoryItemDTO implements Serializable {

    /**
     * 产品skuId
     */
    private Long productSkuId;

    /**
     * 货位ID
     */
    private Long locationId;

    /**
     * 货位名称
     */
    private String locationName;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 批次时间
     */
    private Date batchTime;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 生产日期对应实盘小单位数量
     */
    private BigDecimal unitTotalCount;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }
}
