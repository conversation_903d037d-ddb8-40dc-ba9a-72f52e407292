package com.yijiupi.himalaya.supplychain.dto.erp;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-18 10:36
 **/
public class ErpInOutStockItem implements Serializable {

    /**
     * 明细 id
     */
    private Long businessItemId;

    /**
     * 小单位总数量
     */
    private BigDecimal num;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 产品 skuId
     */
    private Long productId;

    /**
     * 产品规格 id
     */
    private Long productSpecId;

    /**
     * 一级货主 id
     */
    private Long ownerId;

    /**
     * 二级货主明细
     */
    private List<ErpInOutStockSecOwnerDetail> secOwnerDetail;

    /**
     * 规格系数
     */
    private BigDecimal specQuantity;

    /**
     * 大单位数量
     */
    private BigDecimal specificationNum;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 小单位名称
     */
    private String unit;

    public Long getBusinessItemId() {
        return businessItemId;
    }

    public void setBusinessItemId(Long businessItemId) {
        this.businessItemId = businessItemId;
    }

    public BigDecimal getNum() {
        return num;
    }

    public void setNum(BigDecimal num) {
        this.num = num;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public List<ErpInOutStockSecOwnerDetail> getSecOwnerDetail() {
        return secOwnerDetail;
    }

    public void setSecOwnerDetail(List<ErpInOutStockSecOwnerDetail> secOwnerDetail) {
        this.secOwnerDetail = secOwnerDetail;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public BigDecimal getSpecificationNum() {
        return specificationNum;
    }

    public void setSpecificationNum(BigDecimal specificationNum) {
        this.specificationNum = specificationNum;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
