package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/12
 */
public class ProductStoreBatchChangeInfoQueryDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 组织机构id
     */
    private Integer orgId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 产品信息规格id
     */
    private Long productSpecificationId;
    /**
     * 产品skuId
     */
    private Long skuId;

    private Byte jiupiEventType;

    private String orderId;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 组织机构id
     *
     * @return orgId 组织机构id
     */
    public Integer getOrgId() {
        return this.orgId;
    }

    /**
     * 设置 组织机构id
     *
     * @param orgId 组织机构id
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取 订单号
     *
     * @return orderNo 订单号
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 订单号
     *
     * @param orderNo 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 产品信息规格id
     *
     * @return productSpecificationId 产品信息规格id
     */
    public Long getProductSpecificationId() {
        return this.productSpecificationId;
    }

    /**
     * 设置 产品信息规格id
     *
     * @param productSpecificationId 产品信息规格id
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 产品skuId
     *
     * @return skuId 产品skuId
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 产品skuId
     *
     * @param skuId 产品skuId
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    /**
     * 获取
     *
     * @return jiupiEventType
     */
    public Byte getJiupiEventType() {
        return this.jiupiEventType;
    }

    /**
     * 设置
     *
     * @param jiupiEventType
     */
    public void setJiupiEventType(Byte jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    /**
     * 获取
     *
     * @return orderId
     */
    public String getOrderId() {
        return this.orderId;
    }

    /**
     * 设置
     *
     * @param orderId
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
