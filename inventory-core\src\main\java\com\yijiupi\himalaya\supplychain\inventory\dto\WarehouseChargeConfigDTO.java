package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 仓库标准费率配置
 * 
 * @author: lidengfeng
 * @date 2018/9/15 10:23
 */
public class WarehouseChargeConfigDTO implements Serializable {

    /**
     * 编号
     */
    private String id;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 地址
     */
    private String detailAddress;

    /**
     * 下车费
     */
    private BigDecimal unloadingcharge = BigDecimal.valueOf(0);

    /**
     * 分拣费
     */
    private BigDecimal sortingcharge = BigDecimal.valueOf(0);

    /**
     * 托管费
     */
    private BigDecimal custodiancharge = BigDecimal.valueOf(0);

    /**
     * 装车费
     */
    private BigDecimal loadingcharge = BigDecimal.valueOf(0);

    /**
     * 运输费
     */
    private BigDecimal transportcharge = BigDecimal.valueOf(0);

    /**
     * 卸货费
     */
    private BigDecimal landingcharge = BigDecimal.valueOf(0);

    /**
     * 状态 0=停用 1=启用
     */
    private Byte status;

    /**
     * 创建人
     */
    private Long createuser;

    /**
     * 创建时间
     */
    private Date createtime;

    /**
     * 最后更新人
     */
    private Long lastupdateuser;

    /**
     * 最后更新时间
     */
    private Date lastupdatetime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    public BigDecimal getUnloadingcharge() {
        return unloadingcharge;
    }

    public void setUnloadingcharge(BigDecimal unloadingcharge) {
        this.unloadingcharge = unloadingcharge;
    }

    public BigDecimal getSortingcharge() {
        return sortingcharge;
    }

    public void setSortingcharge(BigDecimal sortingcharge) {
        this.sortingcharge = sortingcharge;
    }

    public BigDecimal getCustodiancharge() {
        return custodiancharge;
    }

    public void setCustodiancharge(BigDecimal custodiancharge) {
        this.custodiancharge = custodiancharge;
    }

    public BigDecimal getLoadingcharge() {
        return loadingcharge;
    }

    public void setLoadingcharge(BigDecimal loadingcharge) {
        this.loadingcharge = loadingcharge;
    }

    public BigDecimal getTransportcharge() {
        return transportcharge;
    }

    public void setTransportcharge(BigDecimal transportcharge) {
        this.transportcharge = transportcharge;
    }

    public BigDecimal getLandingcharge() {
        return landingcharge;
    }

    public void setLandingcharge(BigDecimal landingcharge) {
        this.landingcharge = landingcharge;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Long getCreateuser() {
        return createuser;
    }

    public void setCreateuser(Long createuser) {
        this.createuser = createuser;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Long getLastupdateuser() {
        return lastupdateuser;
    }

    public void setLastupdateuser(Long lastupdateuser) {
        this.lastupdateuser = lastupdateuser;
    }

    public Date getLastupdatetime() {
        return lastupdatetime;
    }

    public void setLastupdatetime(Date lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

}
