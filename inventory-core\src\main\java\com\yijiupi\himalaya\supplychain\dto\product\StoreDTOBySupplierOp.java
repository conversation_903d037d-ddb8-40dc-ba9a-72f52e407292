/*
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */

package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 供应商门户库存对象 Created by 杨康 on 2016/11/11.
 */
public class StoreDTOBySupplierOp implements Serializable {

    /**
     * 仓库库存
     */
    private BigDecimal warehouseStoreCount = BigDecimal.ZERO;
    /**
     * 预售库存
     */
    private BigDecimal preSaleStoreCount = BigDecimal.ZERO;
    /**
     * 仓库名称
     */
    private Integer warehouseId;
    /**
     * 城市Id
     */
    private Integer cityId;
    /**
     * skuId
     */
    private Long productSkuId;
    /**
     * 产品信息规格
     */
    private Long productSpecificationId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 渠道
     */
    private Integer channel;

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public BigDecimal getWarehouseStoreCount() {
        return warehouseStoreCount;
    }

    public void setWarehouseStoreCount(BigDecimal warehouseStoreCount) {
        this.warehouseStoreCount = warehouseStoreCount;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public BigDecimal getPreSaleStoreCount() {
        return preSaleStoreCount;
    }

    public void setPreSaleStoreCount(BigDecimal preSaleStoreCount) {
        this.preSaleStoreCount = preSaleStoreCount;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }
}
