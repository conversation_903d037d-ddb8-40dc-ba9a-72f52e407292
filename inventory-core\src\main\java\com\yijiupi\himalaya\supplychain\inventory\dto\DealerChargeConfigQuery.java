package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 经销商费用配置查询参数
 * 
 * @author: lidengfeng
 * @date 2018/9/26 19:12
 */
public class DealerChargeConfigQuery implements Serializable {

    /**
     * 经销商id
     */
    private Long dealerId;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 服务商id集合
     */
    private List<Long> facilitatorIdList;

    /**
     * 是否收取仓配费用
     */
    private Byte isGetWarehouseCharge;

    /**
     * 业务类型 0=正常业务 1=贷款业务
     */
    private Byte businessType;

    /**
     * 经销商手机号
     */
    private String mobileNo;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页数
     */
    private Integer pageSize;

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    /**
     * 获取 服务商id集合
     * 
     * @return
     */
    public List<Long> getFacilitatorIdList() {
        return facilitatorIdList;
    }

    /**
     * 设置 服务商id集合
     * 
     * @param facilitatorIdList
     */
    public void setFacilitatorIdList(List<Long> facilitatorIdList) {
        this.facilitatorIdList = facilitatorIdList;
    }

    /**
     * 获取 是否收取仓配费用
     * 
     * @return
     */
    public Byte getIsGetWarehouseCharge() {
        return isGetWarehouseCharge;
    }

    /**
     * 设置 是否收取仓配费用
     * 
     * @param getWarehouseCharge
     */
    public void setIsGetWarehouseCharge(Byte getWarehouseCharge) {
        isGetWarehouseCharge = getWarehouseCharge;
    }

    /**
     * 获取 业务类型
     * 
     * @return
     */
    public Byte getBusinessType() {
        return businessType;
    }

    /**
     * 设置 业务类型
     * 
     * @param businessType
     */
    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取 手机号
     * 
     * @return
     */
    public String getMobileNo() {
        return mobileNo;
    }

    /**
     * 设置 手机号
     * 
     * @param mobileNo
     */
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public Long getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(Long dealerId) {
        this.dealerId = dealerId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
