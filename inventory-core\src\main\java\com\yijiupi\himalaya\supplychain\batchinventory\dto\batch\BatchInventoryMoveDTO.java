package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 货位库存移库
 *
 * <AUTHOR>
 * @date 2024/9/5
 */
public class BatchInventoryMoveDTO implements Serializable {

    private static final long serialVersionUID = 7466417215602368517L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 货主id
     *
     * @return
     */
    private Long ownerId;

    /**
     * 二级货主id
     *
     * @return
     */
    private Long secOwnerId;

    /**
     * 来源货区集合
     */
    private List<Integer> fromLocationTypes;

    /**
     * skuid
     */
    private Long skuId;

    /**
     * 目的货位
     */
    private Long toLocationId;

    /**
     * 移动小单位总数量
     */
    private BigDecimal moveCount;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 批次库存ids
     */
    private List<String> storeBatchIds;

    /**
     * 是否自动移库
     */
    private boolean autoMoveFlag;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public List<Integer> getFromLocationTypes() {
        return fromLocationTypes;
    }

    public void setFromLocationTypes(List<Integer> fromLocationTypes) {
        this.fromLocationTypes = fromLocationTypes;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getToLocationId() {
        return toLocationId;
    }

    public void setToLocationId(Long toLocationId) {
        this.toLocationId = toLocationId;
    }

    public BigDecimal getMoveCount() {
        return moveCount;
    }

    public void setMoveCount(BigDecimal moveCount) {
        this.moveCount = moveCount;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public List<String> getStoreBatchIds() {
        return storeBatchIds;
    }

    public void setStoreBatchIds(List<String> storeBatchIds) {
        this.storeBatchIds = storeBatchIds;
    }

    public boolean getAutoMoveFlag() {
        return autoMoveFlag;
    }

    public void setAutoMoveFlag(boolean autoMoveFlag) {
        this.autoMoveFlag = autoMoveFlag;
    }
}
