package com.yijiupi.himalaya.supplychain.inventory.dto.instock;

import java.io.Serializable;
import java.util.List;

/**
 * 召回->发货批次
 */
public class RecallDeliverDTO implements Serializable {
    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 订单编号
     */
    private List<String> refOrderNoList;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getRefOrderNoList() {
        return refOrderNoList;
    }

    public void setRefOrderNoList(List<String> refOrderNoList) {
        this.refOrderNoList = refOrderNoList;
    }
}
