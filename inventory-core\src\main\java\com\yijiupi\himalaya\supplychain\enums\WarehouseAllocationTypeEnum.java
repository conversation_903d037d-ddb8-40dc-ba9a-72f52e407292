package com.yijiupi.himalaya.supplychain.enums;

public enum WarehouseAllocationTypeEnum {
	/**
	 * 未定义
	 */
	UNDEFINED(-1, ""),

	/**
	 * 酒饮
	 */
	DRINKING(1, "酒饮"),

	/**
	 * 休食
	 */
	REST(2, "休食"),
	;
	private final Integer code;

	private final String desc;

	WarehouseAllocationTypeEnum(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public Integer getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}

	public static WarehouseAllocationTypeEnum getEnum(Integer value) {
		for (WarehouseAllocationTypeEnum e : WarehouseAllocationTypeEnum.values()) {
			if (e.getCode().equals(value)) {
				return e;
			}
		}
		return UNDEFINED;
	}
}
