package com.yijiupi.himalaya.supplychain.dto.easysell;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存出入库历史记录明细传输DTO
 * 
 * @author: lidengfeng
 * @date: 2018/8/14 11:26
 */
public class ProductStoreChangeDetailDTO implements Serializable {

    /**
     * sku名称
     */
    private String name;

    /**
     * 包装规格名称
     */
    private String specificationname;

    /**
     * 包装规格大单位
     */
    private String packagename;

    /**
     * 包装规格小单位
     */
    private String unitName;

    /**
     * 订单类型共8种:1 酒批订单2 酒批退货单 3 采购入库单 4 采购退货单 5 破损出库单 6 其他出库单（客情、招待、福利） 7 库存盘点单（盘盈单、盘亏单） 8 仓库物料调拨单 1，2这2种都是酒批的，其他都是ERP的
     */
    private Integer ordertype;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packagequantity;

    /**
     * 库存变更数量总计（按照小单位计算）如果是出库就是-XXX
     */
    private BigDecimal totalcount;

    /**
     * 库存变更前的原库存，（按照小单位计算）
     */
    private BigDecimal sourcetotalcount;

    /**
     * 库存总量
     */
    private BigDecimal totalAmountCount;

    /**
     * 变更时间
     */
    private String createtime;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSpecificationname() {
        return specificationname;
    }

    public void setSpecificationname(String specificationname) {
        this.specificationname = specificationname;
    }

    public String getPackagename() {
        return packagename;
    }

    public void setPackagename(String packagename) {
        this.packagename = packagename;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getOrdertype() {
        return ordertype;
    }

    public void setOrdertype(Integer ordertype) {
        this.ordertype = ordertype;
    }

    public BigDecimal getPackagequantity() {
        return packagequantity;
    }

    public void setPackagequantity(BigDecimal packagequantity) {
        this.packagequantity = packagequantity;
    }

    public BigDecimal getTotalcount() {
        return totalcount;
    }

    public void setTotalcount(BigDecimal totalcount) {
        this.totalcount = totalcount;
    }

    public BigDecimal getSourcetotalcount() {
        return sourcetotalcount;
    }

    public void setSourcetotalcount(BigDecimal sourcetotalcount) {
        this.sourcetotalcount = sourcetotalcount;
    }

    public BigDecimal getTotalAmountCount() {
        return totalAmountCount;
    }

    public void setTotalAmountCount(BigDecimal totalAmountCount) {
        this.totalAmountCount = totalAmountCount;
    }

    public String getCreatetime() {
        return createtime;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }
}
