/**
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by 杨康 on 2016/10/16.
 */
public class ProductStoreCountDTO implements Serializable {
    /**
     * 实际库存数量，按包装单位计算（大单位）
     */
    private BigDecimal storeCountMaxUnit = BigDecimal.ZERO;
    /**
     * 实际库存数量，按最小单位计算
     */
    private BigDecimal storeCountMinUnit = BigDecimal.ZERO;
    /**
     * 库存的总数量，按最小单位计算 比如1*6的茅台，现在仓库中有3箱，以及另外2瓶，那么总数量为20
     */
    private BigDecimal totalCountMinUnit = BigDecimal.ZERO;

    public ProductStoreCountDTO() {
        super();
    }

    public ProductStoreCountDTO(BigDecimal totalCounnt, BigDecimal quantity) {
        if (quantity != null && totalCounnt != null) {
            BigDecimal[] remainder = totalCounnt.divideAndRemainder(quantity);
            this.setStoreCountMaxUnit(remainder[0]);
            this.setStoreCountMinUnit(remainder[1]);
        }
        this.setTotalCountMinUnit(totalCounnt);
    }

    public BigDecimal getStoreCountMaxUnit() {
        return storeCountMaxUnit;
    }

    public void setStoreCountMaxUnit(BigDecimal storeCountMaxUnit) {
        this.storeCountMaxUnit = storeCountMaxUnit;
    }

    public BigDecimal getStoreCountMinUnit() {
        return storeCountMinUnit;
    }

    public void setStoreCountMinUnit(BigDecimal storeCountMinUnit) {
        this.storeCountMinUnit = storeCountMinUnit;
    }

    public BigDecimal getTotalCountMinUnit() {
        return totalCountMinUnit;
    }

    public void setTotalCountMinUnit(BigDecimal totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }
}
