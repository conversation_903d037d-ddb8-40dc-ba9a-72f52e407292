package com.yijiupi.himalaya.supplychain.batchinventory.service;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductDateTaskMessage;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDateTaskDTO;

/**
 * 批次库存过期产品任务
 *
 * <AUTHOR>
 * @date 2025/2/8
 */
public interface IBatchInventoryProductDateTaskService {

    /**
     * 完成生产日期治理任务检查
     */
    Map<String, Boolean> checkCompleteProductDateTask(List<ProductionDateTaskDTO> taskDTOS);

    /**
     * 完成生产日期治理任务通
     */
    void completeProductDateTaskNotify(List<ProductDateTaskMessage> taskMessageList);
}
