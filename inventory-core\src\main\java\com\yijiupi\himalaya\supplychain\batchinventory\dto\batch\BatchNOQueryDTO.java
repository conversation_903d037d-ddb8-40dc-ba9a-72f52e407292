package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.util.List;

public class BatchNOQueryDTO implements Serializable {

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 产品批次信息
     */
    private List<BatchNOQueryItemDTO> addItemDTOList;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<BatchNOQueryItemDTO> getAddItemDTOList() {
        return addItemDTOList;
    }

    public void setAddItemDTOList(List<BatchNOQueryItemDTO> addItemDTOList) {
        this.addItemDTOList = addItemDTOList;
    }
}
