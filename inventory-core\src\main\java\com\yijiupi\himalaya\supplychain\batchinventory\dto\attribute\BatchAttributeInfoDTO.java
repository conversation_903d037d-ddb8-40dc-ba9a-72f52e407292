package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import java.io.Serializable;

/**
 * 批属性信息表
 *
 * <AUTHOR> 2018/4/10
 */
public class BatchAttributeInfoDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 批次编号
     */
    private String batchAttributeInfoNo;
    /**
     * 属性Id(dic)
     */
    private Long attributeId;
    /**
     * 属性名称
     */
    private String attributeName;
    /**
     * 属性值id
     */
    private String attributeValueId;
    /**
     * 属性值名称
     */
    private String attributeValueName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createUser;

    /**
     * 获取 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 批次编号
     */
    public String getBatchAttributeInfoNo() {
        return this.batchAttributeInfoNo;
    }

    /**
     * 设置 批次编号
     */
    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    /**
     * 获取 属性Id(dic)
     */
    public Long getAttributeId() {
        return this.attributeId;
    }

    /**
     * 设置 属性Id(dic)
     */
    public void setAttributeId(Long attributeId) {
        this.attributeId = attributeId;
    }

    /**
     * 获取 属性名称
     */
    public String getAttributeName() {
        return this.attributeName;
    }

    /**
     * 设置 属性名称
     */
    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    /**
     * 获取 属性值id
     */
    public String getAttributeValueId() {
        return this.attributeValueId;
    }

    /**
     * 设置 属性值id
     */
    public void setAttributeValueId(String attributeValueId) {
        this.attributeValueId = attributeValueId;
    }

    /**
     * 获取 属性值名称
     */
    public String getAttributeValueName() {
        return this.attributeValueName;
    }

    /**
     * 设置 属性值名称
     */
    public void setAttributeValueName(String attributeValueName) {
        this.attributeValueName = attributeValueName;
    }

    /**
     * 获取 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
}
