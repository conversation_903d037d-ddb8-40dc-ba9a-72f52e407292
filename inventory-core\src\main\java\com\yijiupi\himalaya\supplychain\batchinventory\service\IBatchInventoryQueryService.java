package com.yijiupi.himalaya.supplychain.batchinventory.service;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryResultDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 批次库存相关查询接口
 *
 * <AUTHOR> 2018/3/28
 */
public interface IBatchInventoryQueryService {

    /**
     * 查看批次库存信息
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    PageList<BatchInventoryDTO> findBatchInventoryList(BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 查看批次库存信息
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    PageList<BatchInventoryDTO> findBatchInventoryListBySpecification(BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 查看批次库存信息（根据货位或产品名称获取批次库存）
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    PageList<BatchInventoryDTO> findBatchInventoryListNew(BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 查看批次库存信息（根据货位或产品名称获取批次库存，支持skuIds）
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    PageList<BatchInventoryDTO> findBatchInventoryListBatchNew(BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 根据skuid获取库存货位
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    List<BatchInventoryDTO> findInventoryLocationBySkuId(BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 查看周转区
     */
    PageList<BatchInventoryDTO> findCBHInventoryList(BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 根据货区查可用货位(可以不传locationId)
     *
     * @param batchLocationInfoQueryDTOS
     * @return
     */
    Map<Long, List<BatchLocationInfoDTO>>
        findBatchLocationDTOBySku(List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS);

    /**
     * 查询单个产品的货位库存
     *
     * @param batchLocationInfoQueryDTO
     * @return
     */
    List<BatchLocationInfoDTO> findBatchLocationDTOBySingleSku(BatchLocationInfoQueryDTO batchLocationInfoQueryDTO);

    /**
     * 根据拣货产品的sku及货区Id查询货位相关信息(可以不传locationId)
     *
     * @param batchLocationInfoQueryDTOS
     * @return
     */
    Map<Long, List<BatchLocationInfoDTO>> findBatchHuoWeiDTOBySku(
        List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS, Byte repositoryPriorityRules, Byte repositoryLimit);

    /**
     * 根据货区查可用货位（所有货位，库存信息可能不存在）(areaId必传)
     *
     * @param batchLocationInfoQueryDTOS
     * @return
     */
    Map<Long, List<BatchLocationInfoDTO>> findBatchHuoWeiDTOBySkuAndArea(
        List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS, Byte repositoryPriorityRules, Byte repositoryLimit);

    // /**
    // * 根据货区查可用货位(只传locationId)
    // *
    // * @param locationId
    // * @return
    // */
    // List<BatchLocationInfoDTO> findBatchLocationDTOByLocationId(Long locationId);

    /**
     * 查询计算好的下架策略
     *
     * @param orderItemDTOList
     * @param billType
     * @return
     */
    List<OrderItemDTO> findOutStockStrategyRule(List<OrderItemDTO> orderItemDTOList, String billType);

    /**
     * 查询计算好的延迟配送下架策略
     *
     * @param orderItemDTOList
     * @param billType
     * @return
     */
    List<OrderItemDTO> findDelayOutStockStrategyRule(List<OrderItemDTO> orderItemDTOList, String billType);

    /**
     * 查询符合条件的存储位
     *
     * @param dto
     * @return
     */
    List<OrderItemDTO> findPackageOutStockLocation(BatchInventoryPackageLocationDTO dto);

    /**
     * 查询货位库存
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2018/5/31 16:16
     */
    List<LocationCapacityDTO> getLocationCapacityDTOs(List<Long> lstLocationIds);

    /**
     * 获取批次库存变更记录
     *
     * @return
     */
    PageList<ProductStoreBatchChangeRecordDTO>
        listProductStoreBatchChangeRecord(ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO);

    /**
     * 获取批次库存变更流水
     *
     * @return
     */
    PageList<ProductStoreBatchChangeFlowDTO>
        listProductStoreBatchChangeFlow(ProductStoreBatchChangFlowQueryDTO productStoreBatchChangeRecordQueryDTO);

    /**
     * 检查产品能否存放到某个货位上
     *
     * @param checkBatchInventoryDTO
     * @param msg
     * @return
     */
    Boolean checkIsCanPutIntoLocation(CheckBatchInventoryDTO checkBatchInventoryDTO, String msg);

    /**
     * 通过分配策略查询货位(销售、调拨、其他、第三方出)
     */
    Map<Long, List<BatchLocationInfoDTO>> findLocationByStrategyRule(List<OrderItemDTO> orderItemDTOList,
        String billType);

    /**
     * 根据skuid获取库存货位（不关联location）
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    List<BatchInventoryDTO> findBatchStoreBySkuId(BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 检查产品货位库存是否足够
     */
    void checkProductInventory(List<CheckBatchInventoryDTO> checkBatchInventoryDTOS);

    /**
     * 根据sku查询自身与关联产品的货位库存。 返回结果以传入sku为主，其对应关联产品的货位库存以：货位 + 生产日期 方式合并至 sku 对应货位中
     *
     * @param dto
     * @return
     */
    List<BatchInventoryDTO> findProductAndRefProductLocationStoreBySkuId(BatchInventoryQueryDTO dto);

    /**
     * 库龄产品批次库存查询
     */
    PageList<BatchInventoryDTO> pageListStockAgeProductInventory(StockAgeInventoryQueryDTO stockAgeInventoryQueryDTO);

    /**
     * 根据规格货主仓库查询生产日期
     */
    List<ProductionDateDTO> findProductionDate(List<ProductionDateQueryDTO> productionDateQueryDTOS);

    /**
     * 查询库龄产品相关批次信息
     *
     * @param dto
     * @return
     */
    PageList<BatchInventoryDTO> findStoreAgeRefBatchInventoryList(BatchInventoryQueryDTO dto);

    /**
     * 查询所有负库存仓库
     *
     * @return
     */
    List<BatchInventoryNegativeDTO> listWarehouseByInventoryNegative();

    /**
     * 根据仓库及产品规格信息查询批次生产日期
     */
    List<BatchProductionDateDTO> findProductionDateFromStoreBatch(BatchProductionDateQueryDTO productionDateQueryDTO);

    /**
     * 根据货位、批次库存信息查询产品SKU基础信息
     */
    PageList<BatchInventoryDTO> findProductBaseInfoFromBatchInventory(BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 根据skuId仓库查询生产日期和成本价
     */
    List<ProductionDatePriceDTO> findProductionDatePriceBySkuIds(Integer warehouseId, List<Long> skuIds);

    List<ProductStoreBatchChangeRecordDTO> selectProductStoreBatchChangeRecords(
        ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO);

    /**
     * 查找货位库存
     */
    PageList<ProductStoreBatchDTO> listProductStoreBatch(BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 查找有货位库存的skuId
     */
    List<Long> listSkuIdByBatchInventory(Integer warehouseId);

    /**
     * 根据sku查货位库存
     *
     * 1、查询条件：skuId、source、channel、warehouseId、locationId（可为null） 2、查询结果：skuId、productionDate
     * 3、过滤条件：查询条件、库存数量大于0、生产日期不为null
     */
    Map<Long, List<BatchLocationInfoDTO>>
        listProductStoreBatchBySku(List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS);

    /**
     * 查找有货位库存的skuId（不分页）
     */
    List<Long> findSkuIdByBatchInventory(BatchLocationInfoQueryDTO queryDTO);

    /**
     * 查询有生产日期、有库存的货位库存
     */
    PageList<BatchLocationInfoDTO> findProductDateInventory(BatchLocationInfoQueryDTO queryDTO);

    /**
     * 查询批次库存信息
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    PageList<BatchInventoryDTO> findBatchInventoryInfo(BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 返回出库批次库存生产日期
     */
    Map<String, List<ProductStoreBatchChangeRecordDTO>>
        getProductStoreBatchChangeRecord(ProductStoreBatchChangeRecordQueryDTO qeryDTO);

    /**
     * 根据仓库版本查货位库存
     * 
     * @param batchInventoryQueryDTO
     * @return
     */
    List<BatchInventoryDTO> findBatchInventoryInfoList(BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 查看批次库存信息
     *
     * @param productStoreIds
     * @return
     */
    Map<String, Date> findProductionDateByProductStoreIds(List<String> productStoreIds);

    /**
     * 查询一条为0的批次库存
     *
     * @return
     */
    BatchInventoryDTO queryZeroBatchInventory(ZeroBatchInventoryQueryDTO queryDTO);

    /**
     * 查询销售库存
     *
     * @param queryDTOList
     * @return
     */
    List<OrderCenterSaleInventoryQueryResultDTO>
        findSaleInventoryList(List<OrderCenterSaleInventoryQueryDTO> queryDTOList);

    /**
     * 根据规格 和 订单号查询批次库存记录
     * 
     * @param queryDTO
     * @return
     */
    List<ProductStoreBatchChangeInfoResultDTO>
        findChangeRecordInfoByOrderInfo(ProductStoreBatchChangeInfoQueryDTO queryDTO);

    /**
     * 根据计算的销售库存获取生产日期
     */
    List<ProductionDateDTO> findProductionDateByCalculation(List<ProductionDateQueryDTO> productionDateQueryDTOS);

    /**
     * 批次库存查询，返回促销id
     */
    List<BatchInventoryDTO> listStoreBatchWithPromotion(BatchInventoryQueryDTO batchInventoryQueryDTO);
}
