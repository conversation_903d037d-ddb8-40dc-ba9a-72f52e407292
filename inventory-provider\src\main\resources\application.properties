#Redis cluster 
spring.redis.cluster.nodes=Redis01.yjp.com:6301,Redis01.yjp.com:6302,Redis02.yjp.com:6301,Redis02.yjp.com:6302,Redis03.yjp.com:6301,Redis03.yjp.com:6302
spring.cache.type=redis
#dubbo
dubbo.scanpackage=com.yijiupi.himalaya
dubbo.zookeeper.address=Zookeeper01.yjp.com:2181,Zookeeper02.yjp.com:2181,Zookeeper03.yjp.com:2181
dubbo.provider=productinventory
config.sources=sc_ProductDB
spring.datasource.maxActive=100
# \u8D85\u8FC7\u56DE\u6536\u65F6\u95F4 1800\u79D2\uFF0830\u5206\u949F\uFF09
spring.datasource.remove-abandoned=true
spring.datasource.remove-abandoned-timeout=1800
# MyBatis
mybatis.typealiasespackage=com.yijiupi.himalaya.supplychain.inventory.domain.po,com.yijiupi.himalaya.supplychain.batchinventory.domain.po
mybatis.mapperLocations=com/yijiupi/himalaya/supplychain/inventory/domain/dao/*.xml,com/yijiupi/himalaya/supplychain/batchinventory/domain/dao/*.xml
mybatis.configLocation=classpath:/mybatis-config.xml
#MQ
spring.rabbitmq.addresses=RabbitMQ01.yjp.com:6000,RabbitMQ02.yjp.com:6000,RabbitMQ03.yjp.com:6000
spring.rabbitmq.username=yjp
spring.rabbitmq.password=yjp
spring.rabbitmq.virtual-host=/
spring.rabbitmq.listener.retry.enabled=true
spring.rabbitmq.listener.retry.max-attempts=3
spring.rabbitmq.listener.acknowledge-mode=auto
spring.rabbitmq.recover.enable=true
spring.rabbitmq.listener.concurrency=1
spring.rabbitmq.listener.max-concurrency=10
spring.rabbitmq.template.retry.enabled=true
spring.rabbitmq.template.default-exchange=ex.supplychain.inventory
#ERP-MQ
mq.supplychain.inventory.syncInventory=mq.supplychain.inventory.syncInventory
#supplychain-MQ
ex.supplychain.inventory.SellInventoryChange=ex.supplychain.inventory.SellInventoryChange
ex.supplychain.inventory.WMSSellInventoryChange=ex.supplychain.inventory.WMSSellInventoryChange
ex.supplychain.inventory.productstorechange=ex.supplychain.inventory.productstorechange
ex.supplychain.inStockOrderRatioConvert.convertOrderAdd=ex.supplychain.inStockOrderRatioConvert.convertOrderAdd
#\u4F9B\u5E94\u94FE\u5931\u8D25\u6D88\u606F
ex.supplychain.mq.sendFaild=ex.supplychain.mq.sendFaild
ex.supplychain.replenishment.add=ex.supplychain.replenishment.add
mq.supplychain.inventory.productstorechange=mq.supplychain.inventory.productstorechange
#mq.supplychain.inventory.deliverycountchange=mq.supplychain.inventory.deliverycountchange
mq.supplychain.jiupiOrder.tradingordercomplete=mq.supplychain.jiupiOrder.tradingordercomplete
#\u76D8\u76C8\u76D8\u4E8F\u5355\u76F4\u63A5\u901A\u8FC7\u76D1\u542C\u961F\u5217
mq.supplychain.storecheckResult.directPass=mq.supplychain.storecheckResult.directPass
#\u9000\u8D27\u8BA2\u5355\u5B8C\u6210
mq.supplychain.jiupiOrder.complete=mq.supplychain.jiupiOrder.complete
mq.supplychain.jiupiOrder.deliveryFailed=mq.supplychain.jiupiOrder.deliveryFailed
mq.supplychain.distributtionOrder.complete=mq.supplychain.distributtionOrder.complete
mq.supplychain.awardOrderDelivery.complete=mq.supplychain.awardOrderDelivery.complete
mq.supplychain.jiupiOrder.wmsBusComplete=mq.supplychain.jiupiOrder.wmsBusComplete
#mq.supplychain.awardOrder.deliveryFailed=mq.supplychain.awardOrder.deliveryFailed
mq.supplychain.easyGoWMSOrderDelivery.complete=mq.supplychain.easyGoWMSOrderDelivery.complete
mq.supplychain.outStockOrder.directProcessing=mq.supplychain.outStockOrder.directProcessing
mq.supplychain.ykorders.complete=mq.supplychain.ykorders.complete
mq.supplychain.nporders.complete=mq.supplychain.nporders.complete
mq.supplychain.npreturnordes.complete=mq.supplychain.npreturnordes.complete
mq.supplychain.orders.WmsJiupiOrderComplete=mq.supplychain.orders.WmsJiupiOrderComplete
ex.supplychain.convertOrder.add=ex.supplychain.convertOrder.add
#\uFFFD\u05BF\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u03E2-\uFFFD\u2CBF
ex.supplychain.inventory.StoreInventoryChange=ex.supplychain.inventory.StoreInventoryChange
#\u6613\u6B3E\u5B89\u5168\u5E93\u5B58\u53D8\u66F4
mq.supplychain.easyChain.safetyInventoryChange=mq.supplychain.easyChain.safetyInventoryChange
#\u8BA2\u5355\u660E\u7EC6\u53D8\u66F4
ex.supplychain.omsStockOrder.inventoryChange=ex.supplychain.omsStockOrder.inventoryChange
mq.supplychain.inventory.updateCcpPrice=mq.supplychain.inventory.updateCcpPrice
ex.supplychain.inventory.updateCcpPrice=ex.supplychain.inventory.updateCcpPrice
#\u540C\u6B65\u751F\u4EA7\u65E5\u671F\u6D88\u606F
ex.supplychain.inventory.SynProductionDate=ex.supplychain.inventory.SynProductionDate
ex.supplychain.batchinventory.orderproductiondate=ex.supplychain.batchinventory.orderproductiondate
mq.supplychain.batchinventory.orderproductiondate=mq.supplychain.batchinventory.orderproductiondate
ex.supplychain.batchinventory.productiondatetaskcomplete=ex.supplychain.batchinventory.productiondatetaskcomplete
#erpclient.erp-store-url=http://erp-api.yjp.com/ERPStore/FindERPStoreList?cityId={cityId}
inventory.tradingAPIUrl=http://in-trading-api.yjp.com/
inventory.erpAPIUrl=http://in-erp5-innerapi.yjp.com/inventory/
api.erpAPIUrl=http://in-erp5-innerapi.yjp.com/trading/
deliveryFeeDefault=3
warehouseCustodyFeeDefault=1.2
#Seata\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0534\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
service.max.commit.retry.timeout=5
service.max.rollback.retry.timeout=5
#\u8C03\u62E8\u51FA\u5E93\u5355\u5B8C\u6210
mq.supplychain.allotOrders.complete=mq.supplychain.allotOrders.complete
#\u5E93\u5B58\u5DEE\u5F02\u540C\u6B65ERP
ex.supplychain.InventorySyncRecord.sync=ex.supplychain.InventorySyncRecord.sync
spring.datasource.url=**************************************************************************************************
#\u540C\u6B65\u4EA4\u6613\u4FC3\u9500\u6D3B\u52A8
mq.supplychain.batchinventory.synTrdPromotion=mq.supplychain.batchinventory.synTrdPromotion
#spring.datasource.url=*************************************************************************************
#spring.datasource.url=*************************************************************************************
spring.datasource.username=root
spring.datasource.password=mycat
ordercenter.sdk.host=http://in-ordercenter.yjp.com
ordercenter.sdk.timeout=30000
ordercenter.sdk.app-key=6d01b033-7e2c-435a-b0a6-6cfd21e17a80
ordercenter.sdk.app-secret=2e18dc6ff2e5c9ceb2a310137521872b
ex.supplychain.outstockordersnyc.inboundAndOutbound=ex.supplychain.outstockordersnyc.inboundAndOutbound
xxl.job.executor.appname=scm-app-inventory
xxl.job.executor.name=scm-app-inventory
