package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * 监听交易平台招商订单(总部发货)确认完成DTO
 * 
 * <AUTHOR> 2018/3/6
 */
public class TradingJiupiOrderDTO implements Serializable {
    /**
     * 订单id
     */
    private Long id;
    /**
     * 仓库id
     */
    private Integer warehouse_Id;
    /**
     * 订单类型
     */
    private Integer jiupiOrderType;

    /**
     * 获取 仓库id
     *
     * @return warehouse_Id 仓库id
     */
    public Integer getWarehouse_Id() {
        return this.warehouse_Id;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouse_Id 仓库id
     */
    public void setWarehouse_Id(Integer warehouse_Id) {
        this.warehouse_Id = warehouse_Id;
    }

    /**
     * 获取 订单类型
     *
     * @return jiupiOrderType 订单类型
     */
    public Integer getJiupiOrderType() {
        return this.jiupiOrderType;
    }

    /**
     * 设置 订单类型
     *
     * @param jiupiOrderType 订单类型
     */
    public void setJiupiOrderType(Integer jiupiOrderType) {
        this.jiupiOrderType = jiupiOrderType;
    }

    /**
     * 获取 订单id
     *
     * @return id 订单id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 订单id
     *
     * @param id 订单id
     */
    public void setId(Long id) {
        this.id = id;
    }
}
