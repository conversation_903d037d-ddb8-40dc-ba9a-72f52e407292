package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @author: lidengfeng
 * @date 2019/1/11 10:28
 */
public class ProductCountQuery implements Serializable {

    /**
     * 产品规格参数id
     */
    private Long productSpecificationId;

    /**
     * 经销商id
     */
    private Long dealerId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 产品skuId
     */
    private List<Long> productSkuIdList;

    /**
     * 获取 规格参数id
     * 
     * @return
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 规格参数id
     * 
     * @param productSpecificationId
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public Long getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(Long dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    /**
     * 获取产品skuId集合
     * 
     * @return
     */
    public List<Long> getProductSkuIdList() {
        return productSkuIdList;
    }

    /**
     * 设置 产品skuId集合
     * 
     * @param productSkuIdList
     */
    public void setProductSkuIdList(List<Long> productSkuIdList) {
        this.productSkuIdList = productSkuIdList;
    }
}
