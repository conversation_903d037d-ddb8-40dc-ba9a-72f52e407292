package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/13
 */
public class ZeroBatchInventoryQueryDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 货位id
     */
    private Long locationId;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 skuId
     *
     * @return skuId skuId
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 skuId
     *
     * @param skuId skuId
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    /**
     * 获取 货位id
     *
     * @return locationId 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     *
     * @param locationId 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }
}
