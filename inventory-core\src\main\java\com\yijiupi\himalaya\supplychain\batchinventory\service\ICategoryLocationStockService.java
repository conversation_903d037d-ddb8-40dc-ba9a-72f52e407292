package com.yijiupi.himalaya.supplychain.batchinventory.service;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.order.OrderItemDTO;

import java.util.List;
import java.util.Map;

public interface ICategoryLocationStockService {

    /**
     * 查询计算好策略
     *
     * @param orderItemDTOList
     * @param billType
     * @return
     */
    List<OrderItemDTO> findStrategyRuleForCategory(List<OrderItemDTO> orderItemDTOList, String billType, Integer orgId,
        Byte deliveryMode);

    /**
     * 通过分配策略查询货位(销售、调拨、其他、第三方出库)，支持按类目开启货位库存
     */
    Map<Long, List<BatchLocationInfoDTO>> findLocationForCategory(List<OrderItemDTO> orderItemDTOList, String billType,
        Integer warehouseId);
}
