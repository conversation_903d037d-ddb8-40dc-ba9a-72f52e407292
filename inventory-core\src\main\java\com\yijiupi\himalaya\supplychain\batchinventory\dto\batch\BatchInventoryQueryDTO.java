package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yijiupi.himalaya.base.search.PageCondition;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 批次库存查询dto
 *
 * <AUTHOR> 2018/3/29
 */
public class BatchInventoryQueryDTO extends PageCondition {
    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * skuId集合
     */
    private List<Long> productSkuIdList;

    /**
     * 商品名称
     */
    private String productSkuName;
    /**
     * 仓库
     */
    private Integer warehouseId;
    /**
     * 货位
     */
    private Long locationId;
    /**
     * 供应商(合作商)
     */
    private Long supplierId;
    /**
     * 经销商(入住商)
     */
    private Long agencyId;
    /**
     * 库存分类
     */
    private Integer ownerType;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 批属性编号
     */
    private String batchAttributeInfoNo;

    /**
     * 货位名称
     */
    private String locationName;

    /**
     * 货位完成名称
     */
    private String locationFullName;

    /**
     * 货区/货区类型，货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3），货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）
     */
    private Integer subCategory;

    /**
     * 货区/货区类型，货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3），货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）
     */
    private List<Integer> subCategoryList;

    /**
     * 货区或货位类型：0:货位，1:货区
     */
    private Integer locationCategory;

    /**
     * 产品Id集合
     */
    private List<Long> skuIds;

    /**
     * 关联的仓库库存id
     */
    private String productStoreId;

    /**
     * 是否显示全部货位库存 默认：显示货位库存不为0， -1：显示货位库存<0
     */
    private Byte showAll;

    /**
     * 规格Id+OwnerId
     */
    private List<ProductSpecIdAndOwnerIdDTO> specIdAndOwnerIdList;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     ** 是否限制产品范围（1：表示只查本仓库的产品）
     */
    private Byte limitSku;

    /**
     * 库存 1：有库存 2：无库存
     */
    private Integer storeType;

    /**
     * 起始生产日期
     */
    private Date startProductionDate;

    /**
     * 截止生产日期
     */
    private Date endProductionDate;

    /**
     * 是否按批次日期排序（降序排序）
     */
    private Boolean orderByBatchTime;

    /**
     * 起始入库日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 接截止入库日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 最小库龄
     */
    private Long startStockAge;

    /**
     * 最大库龄
     */
    private Long endStockAge;

    /**
     * 有效期类型 0：正常 1：临期 2：过期
     */
    private Integer shelfLifeType;

    /**
     * 查询类型 0：无分组 1：按生产日期分组 2：按入库日期分组
     */
    private Integer queryType;

    /**
     * 货位
     */
    private List<Long> locationIds;

    /**
     * 起始库龄日期
     */
    private Date startStockAgeTime;

    /**
     * 接截止库龄日期
     */
    private Date endStockAgeTime;
    /**
     * 过滤货位货区字段
     */
    private List<Integer> filterLocations;

    /**
     * 库存记录显示开关
     */
    private Boolean nonnegative;
    /**
     * 查询是否删除的sku：0 否，1 是
     */
    private List<Byte> deleted = Collections.singletonList((byte)0);

    /**
     * 产品条码
     */
    private String productCode;

    /**
     * 一级类目
     */
    private String statisticsClassName;

    /**
     * 二级类目
     */
    private String secondStatisticsClassName;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 关键字
     */
    private String keyWord;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;

    /**
     * 批次库存id集合
     */
    private List<String> storeBatchIds;

    /**
     * 货位业务类型，1：赠品 2:促销
     *
     */
    private Byte locationBusinessType;

    /**
     * 库存属性（0：默认，1：自动转入）
     */
    private Byte batchProperty;

    /**
     * 排除货位业务类型
     *
     */
    private List<Byte> excludeLocationBusinessTypes;

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public Byte getLimitSku() {
        return limitSku;
    }

    public void setLimitSku(Byte limitSku) {
        this.limitSku = limitSku;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public List<ProductSpecIdAndOwnerIdDTO> getSpecIdAndOwnerIdList() {
        return specIdAndOwnerIdList;
    }

    public void setSpecIdAndOwnerIdList(List<ProductSpecIdAndOwnerIdDTO> specIdAndOwnerIdList) {
        this.specIdAndOwnerIdList = specIdAndOwnerIdList;
    }

    public Byte getShowAll() {
        return showAll;
    }

    public void setShowAll(Byte showAll) {
        this.showAll = showAll;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public List<Long> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Integer getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(Integer subCategory) {
        this.subCategory = subCategory;
    }

    public Integer getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    /**
     * 获取 商品名称
     */
    public String getProductSkuName() {
        return this.productSkuName;
    }

    /**
     * 设置 商品名称
     */
    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    /**
     * 获取 仓库
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 货位
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 供应商(合作商)
     */
    public Long getSupplierId() {
        return this.supplierId;
    }

    /**
     * 设置 供应商(合作商)
     */
    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * 获取 经销商(入住商)
     */
    public Long getAgencyId() {
        return this.agencyId;
    }

    /**
     * 设置 经销商(入住商)
     */
    public void setAgencyId(Long agencyId) {
        this.agencyId = agencyId;
    }

    /**
     * 获取 库存分类
     */
    public Integer getOwnerType() {
        return this.ownerType;
    }

    /**
     * 设置 库存分类
     */
    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    /**
     * 获取 库存渠道,0:酒批，1:大宗产品
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 库存渠道,0:酒批，1:大宗产品
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 二级货主Id
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主Id
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取 批属性编号
     */
    public String getBatchAttributeInfoNo() {
        return this.batchAttributeInfoNo;
    }

    /**
     * 设置 批属性编号
     */
    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    /**
     * 获取 skuId
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 skuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public List<Long> getProductSkuIdList() {
        return productSkuIdList;
    }

    public void setProductSkuIdList(List<Long> productSkuIdList) {
        this.productSkuIdList = productSkuIdList;
    }

    public List<Integer> getSubCategoryList() {
        return subCategoryList;
    }

    public void setSubCategoryList(List<Integer> subCategoryList) {
        this.subCategoryList = subCategoryList;
    }

    public String getLocationFullName() {
        return locationFullName;
    }

    public void setLocationFullName(String locationFullName) {
        this.locationFullName = locationFullName;
    }

    public Date getStartProductionDate() {
        return startProductionDate;
    }

    public void setStartProductionDate(Date startProductionDate) {
        this.startProductionDate = startProductionDate;
    }

    public Date getEndProductionDate() {
        return endProductionDate;
    }

    public void setEndProductionDate(Date endProductionDate) {
        this.endProductionDate = endProductionDate;
    }

    public Boolean getOrderByBatchTime() {
        return orderByBatchTime;
    }

    public void setOrderByBatchTime(Boolean orderByBatchTime) {
        this.orderByBatchTime = orderByBatchTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getStartStockAge() {
        return startStockAge;
    }

    public void setStartStockAge(Long startStockAge) {
        this.startStockAge = startStockAge;
    }

    public Long getEndStockAge() {
        return endStockAge;
    }

    public void setEndStockAge(Long endStockAge) {
        this.endStockAge = endStockAge;
    }

    public Integer getShelfLifeType() {
        return shelfLifeType;
    }

    public void setShelfLifeType(Integer shelfLifeType) {
        this.shelfLifeType = shelfLifeType;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public List<Long> getLocationIds() {
        return locationIds;
    }

    public void setLocationIds(List<Long> locationIds) {
        this.locationIds = locationIds;
    }

    public Date getStartStockAgeTime() {
        return startStockAgeTime;
    }

    public void setStartStockAgeTime(Date startStockAgeTime) {
        this.startStockAgeTime = startStockAgeTime;
    }

    public Date getEndStockAgeTime() {
        return endStockAgeTime;
    }

    public void setEndStockAgeTime(Date endStockAgeTime) {
        this.endStockAgeTime = endStockAgeTime;
    }

    public List<Integer> getFilterLocations() {
        return filterLocations;
    }

    public void setFilterLocations(List<Integer> filterLocations) {
        this.filterLocations = filterLocations;
    }

    public Boolean getNonnegative() {
        return nonnegative;
    }

    public void setNonnegative(Boolean nonnegative) {
        this.nonnegative = nonnegative;
    }

    /**
     * 获取 查询是否删除的sku：0 否，1 是
     *
     * @return deleted 查询是否删除的sku：0 否，1 是
     */
    public List<Byte> getDeleted() {
        return this.deleted;
    }

    /**
     * 设置 查询是否删除的sku：0 否，1 是
     *
     * @param deleted 查询是否删除的sku：0 否，1 是
     */
    public void setDeleted(List<Byte> deleted) {
        this.deleted = deleted;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getStatisticsClassName() {
        return statisticsClassName;
    }

    public void setStatisticsClassName(String statisticsClassName) {
        this.statisticsClassName = statisticsClassName;
    }

    public String getSecondStatisticsClassName() {
        return secondStatisticsClassName;
    }

    public void setSecondStatisticsClassName(String secondStatisticsClassName) {
        this.secondStatisticsClassName = secondStatisticsClassName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }

    public List<String> getStoreBatchIds() {
        return storeBatchIds;
    }

    public void setStoreBatchIds(List<String> storeBatchIds) {
        this.storeBatchIds = storeBatchIds;
    }

    public Byte getLocationBusinessType() {
        return locationBusinessType;
    }

    public void setLocationBusinessType(Byte locationBusinessType) {
        this.locationBusinessType = locationBusinessType;
    }

    public Byte getBatchProperty() {
        return batchProperty;
    }

    public void setBatchProperty(Byte batchProperty) {
        this.batchProperty = batchProperty;
    }

    public List<Byte> getExcludeLocationBusinessTypes() {
        return excludeLocationBusinessTypes;
    }

    public void setExcludeLocationBusinessTypes(List<Byte> excludeLocationBusinessTypes) {
        this.excludeLocationBusinessTypes = excludeLocationBusinessTypes;
    }
}
