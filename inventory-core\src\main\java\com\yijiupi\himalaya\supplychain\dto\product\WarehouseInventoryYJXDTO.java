package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 易经销查看仓库库存
 *
 * <AUTHOR>
 * @date 2018/12/27 20:40
 */
public class WarehouseInventoryYJXDTO implements Serializable {
    private static final long serialVersionUID = 5392812016221121687L;

    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 品牌
     */
    private String productBrand;

    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 大单位库存数量
     */
    private BigDecimal packageCount;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 小单位库存数量
     */
    private BigDecimal unitCount;

    /**
     * 小单位总库存数量
     */
    private BigDecimal unitTotalCount;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 货主类型（酒批：0 合作商：1 入驻商：2）
     */
    private Byte ownerType;

    /**
     * 货主名称
     */
    private String secOwnerName;

    public String getSecOwnerName() {
        return secOwnerName;
    }

    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Byte getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Byte ownerType) {
        this.ownerType = ownerType;
    }
}
