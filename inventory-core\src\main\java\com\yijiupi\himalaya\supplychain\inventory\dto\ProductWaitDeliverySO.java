package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * 待出库订单的产品查询
 *
 * <AUTHOR>
 * @date 2019/4/15 19:24
 */
public class ProductWaitDeliverySO extends PageCondition implements Serializable {
    private static final long serialVersionUID = -6655575734063056534L;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 商品特征:1:大件,2:小件
     */
    private Byte productFeature;

    /**
     * 保存条件 0:常温 1:冷藏 2:冷冻
     */
    private Byte storageType;

    /**
     * 产品规格id和ownerId
     */
    private List<ProductSpecAndOwnerIdDTO> specAndOwnerIds;

    /**
     * 产品skuId
     */
    private List<Long> skuIds;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Byte getProductFeature() {
        return productFeature;
    }

    public void setProductFeature(Byte productFeature) {
        this.productFeature = productFeature;
    }

    public List<ProductSpecAndOwnerIdDTO> getSpecAndOwnerIds() {
        return specAndOwnerIds;
    }

    public void setSpecAndOwnerIds(List<ProductSpecAndOwnerIdDTO> specAndOwnerIds) {
        this.specAndOwnerIds = specAndOwnerIds;
    }

    public List<Long> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    public Byte getStorageType() {
        return storageType;
    }

    public void setStorageType(Byte storageType) {
        this.storageType = storageType;
    }
}
