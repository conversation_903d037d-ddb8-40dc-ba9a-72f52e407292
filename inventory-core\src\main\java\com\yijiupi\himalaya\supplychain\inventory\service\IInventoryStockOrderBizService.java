package com.yijiupi.himalaya.supplychain.inventory.service;

import com.yijiupi.himalaya.supplychain.inventory.dto.StockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryChangeDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.ProcessInStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.ProcessOutStockOrderInventoryDTO;

import java.util.List;

/**
 * 仓库单据库存处理
 */
public interface IInventoryStockOrderBizService {

    /**
     * 处理出库单库存
     *
     * @param processDTO
     */
    List<WarehouseInventoryChangeDTO> processOutStockOrderInventory(ProcessOutStockOrderInventoryDTO processDTO);

    /**
     * 处理出库单库存(异常消息重试)
     *
     * @param message
     */
    void retriesOutStockOrderInventoryByMessage(String message);

    /**
     * 处理入库单库存
     */
    void processInStockOrderInventory(ProcessInStockOrderInventoryDTO processDTO);

    /**
     * 处理入库单库存(异常消息重试)
     */
    void retriesInStockOrderInventoryByMessage(String message);

    /**
     * 处理 WMS 不存在单据库存：ERP某些单据不存在WMS但是删除的时候需要处理库存
     */
    void processWmsNotExitsOrderInventory(List<WarehouseInventoryChangeDTO> changeDTOList);

    /**
     * SCM2-15295 虚仓实配库存管理 不生成出库单据，只处理库存
     */
    @Deprecated
    void processOutStockOrderInventoryDYY(StockOrderInventoryDTO stockOrderInventoryDTO);

    /**
     * 易经销出入库处理
     */
    void processOutStockOrderInventoryWithEasySell(StockOrderInventoryDTO stockOrderInventoryDTO);
}
