package com.yijiupi.himalaya.supplychain.inventory.dto.instock;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.yijiupi.himalaya.supplychain.inventory.dto.SimpleOrderInfoDTO;

public class InStockCompleteDTO implements Serializable {

    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 订单编号
     */
    private List<String> refOrderNoList;

    private List<Long> omsOrderIds;

    /**
     * 订单信息
     */

    /*
    * 订单货位id
    * */
    private String locationId;

    /*
     * 订单货位name
     *
     * */
    private String locationName;

    public List<Long> getOmsOrderIds() {
        return omsOrderIds;
    }

    public void setOmsOrderIds(List<Long> omsOrderIds) {
        this.omsOrderIds = omsOrderIds;
    }

    public String getLocationId() {
        return locationId;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    private List<SimpleOrderInfoDTO> simpleOrderInfoDTOS;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String> getRefOrderNoList() {
        if (!CollectionUtils.isEmpty(simpleOrderInfoDTOS)) {
            List<String> orderNos =
                simpleOrderInfoDTOS.stream().filter(orderInfo -> !StringUtils.isEmpty(orderInfo.getOrderNo()))
                    .map(SimpleOrderInfoDTO::getOrderNo).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(orderNos)) {
                return orderNos;
            }
        }
        return refOrderNoList;
    }

    public void setRefOrderNoList(List<String> refOrderNoList) {
        this.refOrderNoList = refOrderNoList;
    }

    public List<SimpleOrderInfoDTO> getSimpleOrderInfoDTOS() {
        return simpleOrderInfoDTOS;
    }

    public void setSimpleOrderInfoDTOS(List<SimpleOrderInfoDTO> simpleOrderInfoDTOS) {
        this.simpleOrderInfoDTOS = simpleOrderInfoDTOS;
    }
}
