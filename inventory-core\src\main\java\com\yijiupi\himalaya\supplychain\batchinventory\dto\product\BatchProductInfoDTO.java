package com.yijiupi.himalaya.supplychain.batchinventory.dto.product;

import java.io.Serializable;

/**
 * 批次商品信息
 *
 * <AUTHOR> 2018/4/10
 */
public class BatchProductInfoDTO implements Serializable {
    /**
     * 商品品牌
     */
    private String brand;
    /**
     * 商品类目
     */
    private String category;
    /**
     * 商品所在仓库id
     */
    private String warehouseId;
    /**
     * 供应商id
     */
    private String ownerId;

    /**
     * 获取 商品品牌
     */
    public String getBrand() {
        return this.brand;
    }

    /**
     * 设置 商品品牌
     */
    public void setBrand(String brand) {
        this.brand = brand;
    }

    /**
     * 获取 商品类目
     */
    public String getCategory() {
        return this.category;
    }

    /**
     * 设置 商品类目
     */
    public void setCategory(String category) {
        this.category = category;
    }

    /**
     * 获取 商品所在仓库id
     */
    public String getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 商品所在仓库id
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 供应商id
     */
    public String getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置 供应商id
     */
    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }
}
