/**
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.search;

import java.io.Serializable;

/**
 * Created by 杨康 on 2016/10/16.
 */
public class ProductCityStoreDetailSO implements Serializable {
    /**
     * 城市Id
     */
    private Integer cityId;
    /**
     * 所属类型
     */
    private Integer ownerType;
    /**
     * 信息规格Id
     */
    private Long productInfoSpecId;
    /**
     * 所属人Id
     */
    private Long ownerId;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Long getProductInfoSpecId() {
        return productInfoSpecId;
    }

    public void setProductInfoSpecId(Long productInfoSpecId) {
        this.productInfoSpecId = productInfoSpecId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public ProductCityStoreDetailSO() {
        super();
    }

    public ProductCityStoreDetailSO(Integer cityId, Long ownerId, Integer ownerType, Long productInfoSpecId) {
        this.cityId = cityId;
        this.ownerId = ownerId;
        this.ownerType = ownerType;
        this.productInfoSpecId = productInfoSpecId;
    }
}
