/*
 * @ClassName ProductChargeConfigPODTO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2018-11-29 19:53:36
 */
package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProductChargeConfigDTO implements Serializable {
    /**
     * @Fields id 编号
     */
    private String id;
    /**
     * @Fields productSpecificationId 产品信息规格id
     */
    private Long productSpecificationId;

    /**
     * 产品id
     */
    private Long productSkuId;
    /**
     * @Fields unloadingCharge 下车费
     */
    private BigDecimal unloadingCharge;
    /**
     * @Fields sortingCharge 分拣费
     */
    private BigDecimal sortingCharge;
    /**
     * @Fields custodianCharge 托管费
     */
    private BigDecimal custodianCharge;
    /**
     * @Fields loadingCharge 装车费
     */
    private BigDecimal loadingCharge;
    /**
     * @Fields transportCharge 运输费
     */
    private BigDecimal transportCharge;
    /**
     * @Fields landingCharge 卸货费
     */
    private BigDecimal landingCharge;
    /**
     * @Fields createUser 创建人
     */
    private Long createUser;
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private Long lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后更新时间
     */
    private Date lastUpdateTime;
    /**
     * @Fields productName 产品名称
     */
    private String productName;
    /**
     * @Fields productBrand 产品品牌
     */
    private String productBrand;
    /**
     * @Fields businessCity 业务城市名称
     */
    private String businessCity;
    /**
     * @Fields warehouseName 仓库名称
     */
    private String warehouseName;
    /**
     * @Fields specificationName 包装规格名称
     */
    private String specificationName;
    /**
     * @Fields firstInStockTime 首次入库时间
     */
    private Date firstInStockTime;
    /**
     * @Fields dealerId 经销商id
     */
    private Long dealerId;
    /**
     * @Fields status 状态 0=停用 1=启用
     */
    private Byte status;

    /**
     * @Fields warehouseId 仓库ID
     */
    private Integer warehouseId;
    /**
     * @Fields cityId 业务城市ID
     */
    private Integer cityId;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * @Fields dealerName 经销商名称
     */
    private String dealerName;

    /**
     * 经销商手机号
     */
    private String mobileNo;

    /**
     * 获取 产品id
     * 
     * @return
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    /**
     * 设置 产品id
     * 
     * @param productSkuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 编号
     */
    public String getId() {
        return id;
    }

    /**
     * 设置 编号
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 产品信息规格id
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 产品信息规格id
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 下车费
     */
    public BigDecimal getUnloadingCharge() {
        return unloadingCharge;
    }

    /**
     * 设置 下车费
     */
    public void setUnloadingCharge(BigDecimal unloadingCharge) {
        this.unloadingCharge = unloadingCharge;
    }

    /**
     * 获取 分拣费
     */
    public BigDecimal getSortingCharge() {
        return sortingCharge;
    }

    /**
     * 设置 分拣费
     */
    public void setSortingCharge(BigDecimal sortingCharge) {
        this.sortingCharge = sortingCharge;
    }

    /**
     * 获取 托管费
     */
    public BigDecimal getCustodianCharge() {
        return custodianCharge;
    }

    /**
     * 设置 托管费
     */
    public void setCustodianCharge(BigDecimal custodianCharge) {
        this.custodianCharge = custodianCharge;
    }

    /**
     * 获取 装车费
     */
    public BigDecimal getLoadingCharge() {
        return loadingCharge;
    }

    /**
     * 设置 装车费
     */
    public void setLoadingCharge(BigDecimal loadingCharge) {
        this.loadingCharge = loadingCharge;
    }

    /**
     * 获取 运输费
     */
    public BigDecimal getTransportCharge() {
        return transportCharge;
    }

    /**
     * 设置 运输费
     */
    public void setTransportCharge(BigDecimal transportCharge) {
        this.transportCharge = transportCharge;
    }

    /**
     * 获取 卸货费
     */
    public BigDecimal getLandingCharge() {
        return landingCharge;
    }

    /**
     * 设置 卸货费
     */
    public void setLandingCharge(BigDecimal landingCharge) {
        this.landingCharge = landingCharge;
    }

    /**
     * 获取 创建人
     */
    public Long getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public Long getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(Long lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 获取 最后更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * 获取 产品名称
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置 产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    /**
     * 获取 产品品牌
     */
    public String getProductBrand() {
        return productBrand;
    }

    /**
     * 设置 产品品牌
     */
    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand == null ? null : productBrand.trim();
    }

    /**
     * 获取 业务城市名称
     */
    public String getBusinessCity() {
        return businessCity;
    }

    /**
     * 设置 业务城市名称
     */
    public void setBusinessCity(String businessCity) {
        this.businessCity = businessCity == null ? null : businessCity.trim();
    }

    /**
     * 获取 仓库名称
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置 仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    /**
     * 获取 包装规格名称
     */
    public String getSpecificationName() {
        return specificationName;
    }

    /**
     * 设置 包装规格名称
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName == null ? null : specificationName.trim();
    }

    /**
     * 获取 首次入库时间
     */
    public Date getFirstInStockTime() {
        return firstInStockTime;
    }

    /**
     * 设置 首次入库时间
     */
    public void setFirstInStockTime(Date firstInStockTime) {
        this.firstInStockTime = firstInStockTime;
    }

    /**
     * 获取 经销商id
     */
    public Long getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     */
    public void setDealerId(Long dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取 状态 0=停用 1=启用
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置 状态 0=停用 1=启用
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取 仓库ID
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库ID
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 业务城市ID
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 业务城市ID
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    /**
     * 获取 经销商名称
     */
    public String getDealerName() {
        return dealerName;
    }

    /**
     * 设置 经销商名称
     */
    public void setDealerName(String dealerName) {
        this.dealerName = dealerName == null ? null : dealerName.trim();
    }

    /**
     * 获取手机号
     * 
     * @return
     */
    public String getMobileNo() {
        return mobileNo;
    }

    /**
     * 设置手机号
     * 
     * @param mobileNo
     */
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

}