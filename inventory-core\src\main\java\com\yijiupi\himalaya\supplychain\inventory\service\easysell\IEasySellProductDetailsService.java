package com.yijiupi.himalaya.supplychain.inventory.service.easysell;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsQueryDTO;

/**
 * @author: lidengfeng
 * @date 2018/8/30 19:21
 */
public interface IEasySellProductDetailsService {

    /**
     * 根据规格Id集合,经销商id,仓库查询商品信息参数
     * 
     * @param productDetailsQueryDTO
     * @return
     */
    PageList<ProductDetailsDTO> findProductDetailsList(ProductDetailsQueryDTO productDetailsQueryDTO);
}
