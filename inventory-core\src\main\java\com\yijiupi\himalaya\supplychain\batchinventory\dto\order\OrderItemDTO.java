package com.yijiupi.himalaya.supplychain.batchinventory.dto.order;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单项dtoØ Ø
 * 
 * <AUTHOR> 2018/4/11
 */
public class OrderItemDTO implements Serializable {
    /**
     * 订单项id
     */
    private Long id;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 产品ID
     */
    private Long productSkuId;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal specQuantity;
    /**
     * 入参小单位总数量
     */
    private BigDecimal unitTotalCount;
    /**
     * 货位id(返回用,入参不需要)
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * 拣货小数量总数(大*转换系数+小)
     */
    private BigDecimal pickUpCount;
    /**
     * 拣货大数量
     */
    private BigDecimal packageCount;
    /**
     * 拣货小数量
     */
    private BigDecimal unitCount;
    /**
     * 货区id
     */
    private Long areaId;
    /**
     * 货区名称
     */
    private String areaName;

    private Byte subCategory;

    /**
     * 批次入库时间
     */
    private Date batchTime;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 所属人(货主)id
     */
    private Long ownerId;

    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 订单项详细id
     */
    private Long itemDetailId;

    /**
     * 是否是促销订单项
     */
    private Byte IsAdvent;

    public Byte getIsAdvent() {
        return IsAdvent;
    }

    public void setIsAdvent(Byte isAdvent) {
        IsAdvent = isAdvent;
    }

    public String getIdentityKey() {
        return String.format("%s-%s", getId(), getItemDetailId());
    }

    public Long getItemDetailId() {
        return itemDetailId;
    }

    public void setItemDetailId(Long itemDetailId) {
        this.itemDetailId = itemDetailId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Byte getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(Byte subCategory) {
        this.subCategory = subCategory;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * 获取 订单项id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 订单项id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 仓库ID
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库ID
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品ID
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品ID
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 库存渠道,0:酒批，1:大宗产品
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 库存渠道,0:酒批，1:大宗产品
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 包装规格大小单位转换系数
     */
    public BigDecimal getSpecQuantity() {
        return this.specQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     */
    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取 小单位总数量
     */
    public BigDecimal getUnitTotalCount() {
        return this.unitTotalCount;
    }

    /**
     * 设置 小单位总数量
     */
    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    /**
     * 获取 货位id(返回用,入参不需要)
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id(返回用,入参不需要)
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 拣货数量
     */
    public BigDecimal getPickUpCount() {
        return this.pickUpCount;
    }

    /**
     * 设置 拣货数量
     */
    public void setPickUpCount(BigDecimal pickUpCount) {
        this.pickUpCount = pickUpCount;
    }

    /**
     * 获取 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 拣货大数量
     */
    public BigDecimal getPackageCount() {
        return this.packageCount;
    }

    /**
     * 设置 拣货大数量
     */
    public void setPackageCount(BigDecimal packageCount) {
        this.packageCount = packageCount;
    }

    /**
     * 获取 拣货小数量
     */
    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    /**
     * 设置 拣货小数量
     */
    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }
}
