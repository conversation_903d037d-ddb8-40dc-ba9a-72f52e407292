package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSecOwnerIdQueryDTO;

/**
 * 查询二级货主服务
 *
 */
public interface IProductSecOwnerService {

    /**
     * 更新二级货主 查询条件List<WarehouseId+"-"+ProductSpecificationId+"-"+OwnerId
     */
    Map<String, List<Long>> getSecOwnerIdMap(ProductSecOwnerIdQueryDTO productSecOwnerIdQueryDTO);

}
