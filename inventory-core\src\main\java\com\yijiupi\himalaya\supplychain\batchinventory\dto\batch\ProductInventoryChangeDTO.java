package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.yijiupi.himalaya.supplychain.batchinventory.constant.ProcessLocationInventoryTypeConstants;

public class ProductInventoryChangeDTO implements Serializable {

    /**
     * 主键ID
     */
    private String id;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 库存ID
     */
    private String productStoreId;
    /**
     * 城市ID
     */
    private Integer cityId;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 酒批时间类型
     */
    private Integer jiupiEventType;
    /**
     * ERP事件类型
     */
    private Integer erpEventType;
    /**
     * 库存变更数量(大单位,设置为0)
     */
    private BigDecimal countMaxUnit;
    /**
     * 库存变更数量(小单位)
     */
    private BigDecimal countMinUnit;
    /**
     * 库存变更数量总计
     */
    private BigDecimal totalCount;
    /**
     * 变更前原库存
     */
    private BigDecimal sourceTotalCount;
    /**
     * 描述
     */
    private String description;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 批次时间
     */
    private Date batchTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 变更类型(1:销售库存 2:仓库库存)
     */
    private Integer storeType;

    private String createTimeStr;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 过期时间
     */
    private Date expireTime;
    /**
     * '操作来源,0:供应链，1:ERP,2:易经销
     */
    private Integer systemSource;
    /**
     * 批属性编号
     */
    private String batchAttributeInfoNo;
    /**
     * 批属性JSON
     */
    private String attributeList;
    /**
     * 出入库类型. 入库单(1),出库单(2)
     */
    private Integer outInType;
    /**
     * 配送方式（0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    private Integer deliveryMode;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * '货位名称'
     */
    private String locationName;

    /**
     * 多个货位Id
     */
    private List<Long> locationIds;

    /**
     * 批次库存表ID
     */
    private String productStoreBatchId;

    private Integer ownerType;

    /**
     * 去重字段
     */
    private String distinctKey;

    /**
     * 是否库龄管控产品 false : 不是 true : 是
     */
    private Boolean storeAgeControlProduct;

    /**
     * 原订单ID
     */
    private String oldOrderId;

    /**
     * 原订单编号
     */
    private String oldOrderNo;

    /**
     * 指定货位类型
     */
    private Integer locationType;

    /**
     * 排除货位类型
     */
    private List<Integer> exSubcategory;

    /**
     * 是否随机扣库存，默认非随机
     */
    private Boolean needRandom = false;

    /**
     * 产品skuId
     */
    private Long productSkuId;

    /**
     * 是否忽略空生产日期 false : 不是 true : 是
     */
    private Boolean ignoreNullProductionDate;
    /**
     * 处理事件
     * 
     * @see ProcessLocationInventoryTypeConstants
     */
    private Integer processType;

    /**
     * 库存属性（0：默认，1：自动转入
     */
    private Byte batchProperty;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    public Integer getErpEventType() {
        return erpEventType;
    }

    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public BigDecimal getCountMaxUnit() {
        return countMaxUnit;
    }

    public void setCountMaxUnit(BigDecimal countMaxUnit) {
        this.countMaxUnit = countMaxUnit;
    }

    public BigDecimal getCountMinUnit() {
        return countMinUnit;
    }

    public void setCountMinUnit(BigDecimal countMinUnit) {
        this.countMinUnit = countMinUnit;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getSourceTotalCount() {
        return sourceTotalCount;
    }

    public void setSourceTotalCount(BigDecimal sourceTotalCount) {
        this.sourceTotalCount = sourceTotalCount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Integer getSystemSource() {
        return systemSource;
    }

    public void setSystemSource(Integer systemSource) {
        this.systemSource = systemSource;
    }

    public String getBatchAttributeInfoNo() {
        return batchAttributeInfoNo;
    }

    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    public String getAttributeList() {
        return attributeList;
    }

    public void setAttributeList(String attributeList) {
        this.attributeList = attributeList;
    }

    public Integer getOutInType() {
        return outInType;
    }

    public void setOutInType(Integer outInType) {
        this.outInType = outInType;
    }

    public Integer getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(Integer deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public List<Long> getLocationIds() {
        return locationIds;
    }

    public void setLocationIds(List<Long> locationIds) {
        this.locationIds = locationIds;
    }

    public String getProductStoreBatchId() {
        return productStoreBatchId;
    }

    public void setProductStoreBatchId(String productStoreBatchId) {
        this.productStoreBatchId = productStoreBatchId;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public String getDistinctKey() {
        return distinctKey;
    }

    public void setDistinctKey(String distinctKey) {
        this.distinctKey = distinctKey;
    }

    public Boolean getStoreAgeControlProduct() {
        return storeAgeControlProduct;
    }

    public void setStoreAgeControlProduct(Boolean storeAgeControlProduct) {
        this.storeAgeControlProduct = storeAgeControlProduct;
    }

    public String getOldOrderId() {
        return oldOrderId;
    }

    public void setOldOrderId(String oldOrderId) {
        this.oldOrderId = oldOrderId;
    }

    public String getOldOrderNo() {
        return oldOrderNo;
    }

    public void setOldOrderNo(String oldOrderNo) {
        this.oldOrderNo = oldOrderNo;
    }

    public Integer getLocationType() {
        return locationType;
    }

    public void setLocationType(Integer locationType) {
        this.locationType = locationType;
    }

    public List<Integer> getExSubcategory() {
        return exSubcategory;
    }

    public void setExSubcategory(List<Integer> exSubcategory) {
        this.exSubcategory = exSubcategory;
    }

    public Boolean getNeedRandom() {
        return needRandom;
    }

    public void setNeedRandom(Boolean needRandom) {
        this.needRandom = needRandom;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Boolean getIgnoreNullProductionDate() {
        return ignoreNullProductionDate;
    }

    public void setIgnoreNullProductionDate(Boolean ignoreNullProductionDate) {
        this.ignoreNullProductionDate = ignoreNullProductionDate;
    }

    @Override
    public String toString() {
        return "ProductInventoryChangeDTO{" + "id='" + id + '\'' + ", warehouseId=" + warehouseId + ", productStoreId='"
            + productStoreId + '\'' + ", cityId=" + cityId + ", orderType=" + orderType + ", orderId='" + orderId + '\''
            + ", orderNo='" + orderNo + '\'' + ", jiupiEventType=" + jiupiEventType + ", erpEventType=" + erpEventType
            + ", countMaxUnit=" + countMaxUnit + ", countMinUnit=" + countMinUnit + ", totalCount=" + totalCount
            + ", sourceTotalCount=" + sourceTotalCount + ", description='" + description + '\'' + ", createTime="
            + createTime + ", batchTime=" + batchTime + ", createUser='" + createUser + '\'' + ", storeType="
            + storeType + ", createTimeStr='" + createTimeStr + '\'' + ", productionDate=" + productionDate
            + ", expireTime=" + expireTime + ", systemSource=" + systemSource + ", batchAttributeInfoNo='"
            + batchAttributeInfoNo + '\'' + ", attributeList='" + attributeList + '\'' + ", outInType=" + outInType
            + ", deliveryMode=" + deliveryMode + ", locationId=" + locationId + ", locationName='" + locationName + '\''
            + ", locationIds=" + locationIds + ", productStoreBatchId='" + productStoreBatchId + '\'' + ", ownerType="
            + ownerType + ", distinctKey='" + distinctKey + '\'' + ", storeAgeControlProduct=" + storeAgeControlProduct
            + ", oldOrderId='" + oldOrderId + '\'' + ", oldOrderNo='" + oldOrderNo + '\'' + ", locationType="
            + locationType + ", exSubcategory=" + exSubcategory + ", needRandom=" + needRandom + ", productSkuId="
            + productSkuId + ", ignoreNullProductionDate=" + ignoreNullProductionDate + '}';
    }

    /**
     * 获取 处理事件 @see ProcessLocationInventoryTypeConstants
     *
     * @return processType 处理事件 @see ProcessLocationInventoryTypeConstants
     */
    public Integer getProcessType() {
        return this.processType;
    }

    /**
     * 设置 处理事件 @see ProcessLocationInventoryTypeConstants
     *
     * @param processType 处理事件 @see ProcessLocationInventoryTypeConstants
     */
    public void setProcessType(Integer processType) {
        this.processType = processType;
    }

    public Byte getBatchProperty() {
        return batchProperty;
    }

    public void setBatchProperty(Byte batchProperty) {
        this.batchProperty = batchProperty;
    }
}
