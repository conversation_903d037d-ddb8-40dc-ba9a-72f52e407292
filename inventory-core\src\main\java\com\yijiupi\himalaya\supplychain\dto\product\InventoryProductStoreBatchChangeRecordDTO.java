package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/4/30
 */
public class InventoryProductStoreBatchChangeRecordDTO implements Serializable {

    private String storeId;

    private Integer cityId;

    private Integer warehouseId;

    private Long productSkuId;

    private String productName;

    private String specName;

    private Integer specQuantity;

    private String packageName;

    private String unitName;

    private Long ownerId;

    private Long secOwnerId;

    private Long specId;

    private Byte ownerType;

    private String storeRecordId;

    private Byte orderType;

    private Byte jiupiEventType;

    private Byte erpEventType;

    private String orderId;

    private String orderNo;

    private String productStoreBatchId;

    private BigDecimal batchChangeUnitTotalCount;

    private String BatchAttributeInfoNo;

    private Long locationId;

    private String locationName;

    private Date productiondate;

    private BigDecimal countMaxUnit;

    private BigDecimal countMinUnit;

    private BigDecimal totalCount;

    private BigDecimal sourceTotalCount;

    private String ownerName;

    private String secOwnerName;

    private Byte channel;

    private Date batchTime;
    /**
     * 
     */
    private String createUser;

    /**
     * 获取
     *
     * @return storeId
     */
    public String getStoreId() {
        return this.storeId;
    }

    /**
     * 设置
     *
     * @param storeId
     */
    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    /**
     * 获取
     *
     * @return cityId
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置
     *
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取
     *
     * @return warehouseId
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置
     *
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取
     *
     * @return productSkuId
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置
     *
     * @param productSkuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取
     *
     * @return productName
     */
    public String getProductName() {
        return this.productName;
    }

    /**
     * 设置
     *
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取
     *
     * @return specName
     */
    public String getSpecName() {
        return this.specName;
    }

    /**
     * 设置
     *
     * @param specName
     */
    public void setSpecName(String specName) {
        this.specName = specName;
    }

    /**
     * 获取
     *
     * @return specQuantity
     */
    public Integer getSpecQuantity() {
        return this.specQuantity;
    }

    /**
     * 设置
     *
     * @param specQuantity
     */
    public void setSpecQuantity(Integer specQuantity) {
        this.specQuantity = specQuantity;
    }

    /**
     * 获取
     *
     * @return packageName
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置
     *
     * @param packageName
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取
     *
     * @return unitName
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置
     *
     * @param unitName
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取
     *
     * @return ownerId
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置
     *
     * @param ownerId
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取
     *
     * @return secOwnerId
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置
     *
     * @param secOwnerId
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取
     *
     * @return specId
     */
    public Long getSpecId() {
        return this.specId;
    }

    /**
     * 设置
     *
     * @param specId
     */
    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    /**
     * 获取
     *
     * @return ownerType
     */
    public Byte getOwnerType() {
        return this.ownerType;
    }

    /**
     * 设置
     *
     * @param ownerType
     */
    public void setOwnerType(Byte ownerType) {
        this.ownerType = ownerType;
    }

    /**
     * 获取
     *
     * @return storeRecordId
     */
    public String getStoreRecordId() {
        return this.storeRecordId;
    }

    /**
     * 设置
     *
     * @param storeRecordId
     */
    public void setStoreRecordId(String storeRecordId) {
        this.storeRecordId = storeRecordId;
    }

    /**
     * 获取
     *
     * @return orderType
     */
    public Byte getOrderType() {
        return this.orderType;
    }

    /**
     * 设置
     *
     * @param orderType
     */
    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    /**
     * 获取
     *
     * @return jiupiEventType
     */
    public Byte getJiupiEventType() {
        return this.jiupiEventType;
    }

    /**
     * 设置
     *
     * @param jiupiEventType
     */
    public void setJiupiEventType(Byte jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    /**
     * 获取
     *
     * @return erpEventType
     */
    public Byte getErpEventType() {
        return this.erpEventType;
    }

    /**
     * 设置
     *
     * @param erpEventType
     */
    public void setErpEventType(Byte erpEventType) {
        this.erpEventType = erpEventType;
    }

    /**
     * 获取
     *
     * @return orderId
     */
    public String getOrderId() {
        return this.orderId;
    }

    /**
     * 设置
     *
     * @param orderId
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取
     *
     * @return orderNo
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置
     *
     * @param orderNo
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取
     *
     * @return productStoreBatchId
     */
    public String getProductStoreBatchId() {
        return this.productStoreBatchId;
    }

    /**
     * 设置
     *
     * @param productStoreBatchId
     */
    public void setProductStoreBatchId(String productStoreBatchId) {
        this.productStoreBatchId = productStoreBatchId;
    }

    /**
     * 获取
     *
     * @return batchChangeUnitTotalCount
     */
    public BigDecimal getBatchChangeUnitTotalCount() {
        return this.batchChangeUnitTotalCount;
    }

    /**
     * 设置
     *
     * @param batchChangeUnitTotalCount
     */
    public void setBatchChangeUnitTotalCount(BigDecimal batchChangeUnitTotalCount) {
        this.batchChangeUnitTotalCount = batchChangeUnitTotalCount;
    }

    /**
     * 获取
     *
     * @return BatchAttributeInfoNo
     */
    public String getBatchAttributeInfoNo() {
        return this.BatchAttributeInfoNo;
    }

    /**
     * 设置
     *
     * @param BatchAttributeInfoNo
     */
    public void setBatchAttributeInfoNo(String BatchAttributeInfoNo) {
        this.BatchAttributeInfoNo = BatchAttributeInfoNo;
    }

    /**
     * 获取
     *
     * @return locationId
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置
     *
     * @param locationId
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取
     *
     * @return locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置
     *
     * @param locationName
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取
     *
     * @return productiondate
     */
    public Date getProductiondate() {
        return this.productiondate;
    }

    /**
     * 设置
     *
     * @param productiondate
     */
    public void setProductiondate(Date productiondate) {
        this.productiondate = productiondate;
    }

    /**
     * 获取
     *
     * @return countMaxUnit
     */
    public BigDecimal getCountMaxUnit() {
        return this.countMaxUnit;
    }

    /**
     * 设置
     *
     * @param countMaxUnit
     */
    public void setCountMaxUnit(BigDecimal countMaxUnit) {
        this.countMaxUnit = countMaxUnit;
    }

    /**
     * 获取
     *
     * @return countMinUnit
     */
    public BigDecimal getCountMinUnit() {
        return this.countMinUnit;
    }

    /**
     * 设置
     *
     * @param countMinUnit
     */
    public void setCountMinUnit(BigDecimal countMinUnit) {
        this.countMinUnit = countMinUnit;
    }

    /**
     * 获取
     *
     * @return totalCount
     */
    public BigDecimal getTotalCount() {
        return this.totalCount;
    }

    /**
     * 设置
     *
     * @param totalCount
     */
    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取
     *
     * @return sourceTotalCount
     */
    public BigDecimal getSourceTotalCount() {
        return this.sourceTotalCount;
    }

    /**
     * 设置
     *
     * @param sourceTotalCount
     */
    public void setSourceTotalCount(BigDecimal sourceTotalCount) {
        this.sourceTotalCount = sourceTotalCount;
    }

    /**
     * 获取
     *
     * @return ownerName
     */
    public String getOwnerName() {
        return this.ownerName;
    }

    /**
     * 设置
     *
     * @param ownerName
     */
    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    /**
     * 获取
     *
     * @return secOwnerName
     */
    public String getSecOwnerName() {
        return this.secOwnerName;
    }

    /**
     * 设置
     *
     * @param secOwnerName
     */
    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    /**
     * 获取
     *
     * @return channel
     */
    public Byte getChannel() {
        return this.channel;
    }

    /**
     * 设置
     *
     * @param channel
     */
    public void setChannel(Byte channel) {
        this.channel = channel;
    }

    /**
     * 获取
     *
     * @return batchTime
     */
    public Date getBatchTime() {
        return this.batchTime;
    }

    /**
     * 设置
     *
     * @param batchTime
     */
    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    /**
     * 获取
     *
     * @return createUser
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置
     *
     * @param createUser
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
}
