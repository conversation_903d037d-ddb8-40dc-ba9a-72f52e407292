package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * 校验仓库是否可停用入参
 * 
 * @author: lid<PERSON>feng
 * @date 2018/12/3 17:09
 */
public class ProductWarehouseDTO implements Serializable {

    /**
     * 经销商id
     */
    private String dealerId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 获取 经销商id
     * 
     * @return
     */
    public String getDealerId() {
        return dealerId;
    }

    /**
     * 设置 经销商id
     * 
     * @param dealerId
     */
    public void setDealerId(String dealerId) {
        this.dealerId = dealerId;
    }

    /**
     * 获取仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
