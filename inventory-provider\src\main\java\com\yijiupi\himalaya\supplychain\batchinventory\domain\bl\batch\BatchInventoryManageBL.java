package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.batchinventory.config.RedisKeyConstant;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute.BatchAttributeInfoBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.locationinventory.ProcessLocationInventoryBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.locationinventory.ProcessLocationInventoryBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event.DefectiveProductPriceEvent;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event.ProductionDateChangeEventBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event.ProductionDateTaskEventBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchInventoryConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductSkuMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.inventory.ProductStoreMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.WarehouseInventoryTransferPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.productSku.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.DefectiveInventoryPriceDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductDateTaskMessage;
import com.yijiupi.himalaya.supplychain.batchinventory.util.RedisUtil;
import com.yijiupi.himalaya.supplychain.batchinventory.util.UUIDUtil;
import com.yijiupi.himalaya.supplychain.dto.OrderProcessRuleConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.enums.*;
import com.yijiupi.himalaya.supplychain.inventory.constant.InventoryChangeTypes;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryTransfersDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IProductInventoryRecordManagerService;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryManageService;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuConfigService;
import com.yijiupi.himalaya.supplychain.service.IOrderProcessRuleConfigService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.util.SecOwnerIdComparator;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyConfigQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.*;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskQueryService;

/**
 * 批次库存操作
 *
 * <AUTHOR> 2018/3/31
 */
@Service
public class BatchInventoryManageBL {
    private final static Logger LOGGER = LoggerFactory.getLogger(BatchInventoryManageBL.class);
    @Autowired
    private BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;
    @Autowired
    private BatchInventoryQueryBL batchInventoryQueryBL;
    @Reference
    private LocationAreaService locationAreaService;
    @Reference
    private ILocationService iLocationService;
    @Autowired
    private BatchAttributeInfoBL batchAttributeInfoBL;
    @Autowired
    private ProductStoreMapper productStoreMapper;
    @Autowired
    private ProcessLocationInventoryBL processLocationInventoryBL;
    @Autowired
    private ProductStoreBatchChangeRecordBL productStoreBatchChangeRecordBL;
    @Autowired
    private BatchInventoryProductSkuMapper batchInventoryProductSkuMapper;
    @Autowired
    private ProductStoreBatchBL productStoreBatchBL;
    @Autowired
    private ProductionDateChangeEventBL productionDateChangeEventBL;
    @Autowired
    private ProductionDateTaskEventBL productionDateTaskEventBL;
    @Autowired
    private PromotionStoreBatchBL promotionStoreBatchBL;
    @Autowired
    private RedisUtil<String> redisUtil;

    @Reference
    private IWarehouseInventoryManageService iWarehouseInventoryManageService;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IStockAgeStrategyService stockAgeStrategyService;
    @Reference
    private IProductInventoryRecordManagerService iProductInventoryRecordManagerService;
    @Reference
    private IOrderProcessRuleConfigService iOrderProcessRuleConfigService;
    @Reference
    private IVariableValueService variableValueService;
    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private IWarehouseQueryService warehouseQueryService;
    @Reference
    private IBatchTaskQueryService iBatchTaskQueryService;
    @Reference
    private IProductSkuService iProductSkuService;
    @Reference
    private IProductSkuConfigService iProductSkuConfigService;

    @Autowired
    private DefectiveProductPriceEvent defectiveProductPriceEvent;

    private static final String PRODUCTION_DATE_CHANEG_EVENT_TYPE_NAME = "生产日期变更为";

    /**
     * 批次库存转移货位 1、相同渠道时，只需要操作批次库存 2、不同渠道时，需要分别操作仓库库存和批次库存
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchInventoryTransfer(List<BatchInventoryTransferDTO> batchInventoryTransferDTOList,
        PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        // 相同渠道时，操作批次库存
        List<BatchInventoryTransferDTO> withoutInventoryProcessList = batchInventoryTransferDTOList.stream()
            .filter(n -> n.getFromChannel().equals(n.getToChannel())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(withoutInventoryProcessList)) {
            batchInventoryTransferChannelSame(withoutInventoryProcessList, pickUpChangeRecordDTO);
        }

        // 不同渠道时，操作仓库库存和批次库存
        List<BatchInventoryTransferDTO> haveInventoryProcessList = batchInventoryTransferDTOList.stream()
            .filter(n -> !n.getFromChannel().equals(n.getToChannel())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(haveInventoryProcessList)) {
            batchInventoryTransferChannelDiffer(haveInventoryProcessList);
        }
    }

    /**
     * 相同渠道库存转移（只处理批次库存）
     */
    private void batchInventoryTransferChannelSame(List<BatchInventoryTransferDTO> withoutInventoryProcessList,
        PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        LOGGER.info(String.format("相同渠道库存转移：%s", JSON.toJSONString(withoutInventoryProcessList)));
        List<ProductStoreBatchPO> updateProductStoreBatchList = new ArrayList<>();
        List<ProductStoreBatchPO> insertProductStoreBatchList = new ArrayList<>();

        // 获取来源货位的批次库存信息
        Map<String, ProductStoreBatchPO> fromProductStoreBatchMap =
            getFromProductStoreBatchMap(withoutInventoryProcessList);

        for (BatchInventoryTransferDTO batchInventoryTransferDTO : withoutInventoryProcessList) {
            // 1、处理来源货位
            ProductStoreBatchPO fromProductStoreBatchPOFromMap =
                fromProductStoreBatchMap.get(batchInventoryTransferDTO.getStoreBatchId());
            AssertUtils.notNull(fromProductStoreBatchPOFromMap,
                "被转移库存不存在 storeId:" + batchInventoryTransferDTO.getProductStoreId());
            ProductStoreBatchPO fromProductStoreBatchPO = new ProductStoreBatchPO();
            BeanUtils.copyProperties(fromProductStoreBatchPOFromMap, fromProductStoreBatchPO);
            fromProductStoreBatchPO.setTotalCount(
                (batchInventoryTransferDTO.getPackageCount().multiply(batchInventoryTransferDTO.getPackageQuantity())
                    .add(batchInventoryTransferDTO.getUnitCount())).multiply(new BigDecimal(-1)));
            updateProductStoreBatchList.add(fromProductStoreBatchPO);

            // 2、处理目标货位
            ProductStoreBatchPO toProductStoreBatchPO =
                createToProductStoreBatchPO(batchInventoryTransferDTO, fromProductStoreBatchPO);

            // 判断目标库位是否存在
            String batchInventoryIsExist = batchInventoryProductStoreBatchMapper.findBatchInventoryIsExist(
                toProductStoreBatchPO.getProductStoreId(), toProductStoreBatchPO.getLocationId(),
                toProductStoreBatchPO.getBatchAttributeInfoNo());
            if (StringUtils.isNotEmpty(batchInventoryIsExist)) {
                // 若存在，则修改批次库存数量
                toProductStoreBatchPO.setId(batchInventoryIsExist);
                updateProductStoreBatchList.add(toProductStoreBatchPO);
            } else {
                // 若不存在，则新增批次库存
                toProductStoreBatchPO.setId(UUIDUtil.getUUID().replaceAll("-", ""));
                if (Objects.equals(pickUpChangeRecordDTO.getBatchProperty(),
                    ProductStoreBatchPropertyEnum.自动转入.getType())) {
                    toProductStoreBatchPO.setBatchProperty(pickUpChangeRecordDTO.getBatchProperty());
                }
                // 新增批次库存时批次时间为当前时间
                toProductStoreBatchPO.setBatchTime(new Date());
                insertProductStoreBatchList.add(toProductStoreBatchPO);
            }
        }
        // 修改批次库存
        updateBatchInventory(updateProductStoreBatchList, pickUpChangeRecordDTO);
        // 新增批次库存
        insertBatchInventoryPOList(insertProductStoreBatchList, pickUpChangeRecordDTO);
    }

    /**
     * 查询来源货位的批次库存信息
     *
     * @return
     */
    private Map<String, ProductStoreBatchPO>
        getFromProductStoreBatchMap(List<BatchInventoryTransferDTO> withoutInventoryProcessList) {
        List<String> storeBatchIds =
            withoutInventoryProcessList.stream().map(p -> p.getStoreBatchId()).distinct().collect(Collectors.toList());
        // 根据主键id查询批次库存信息
        List<ProductStoreBatchPO> fromProductStoreBatchList =
            batchInventoryProductStoreBatchMapper.findProductStoreBatchListById(storeBatchIds);
        Map<String, ProductStoreBatchPO> fromProductStoreBatchMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(fromProductStoreBatchList)) {
            for (ProductStoreBatchPO p : fromProductStoreBatchList) {
                fromProductStoreBatchMap.put(p.getId(), p);
            }
        }
        return fromProductStoreBatchMap;
    }

    /**
     * 组建目标货位批次库存对象
     *
     * @return
     */
    private ProductStoreBatchPO createToProductStoreBatchPO(BatchInventoryTransferDTO batchInventoryTransferDTO,
        ProductStoreBatchPO fromProductStoreBatchPO) {
        ProductStoreBatchPO toProductStoreBatchPO =
            BatchInventoryConvert.BatchInventoryTransferDTO2ProductStoreBatchPO(batchInventoryTransferDTO);

        fromProductStoreBatchPO
            .setProductionDate(batchAttributeInfoBL.formatDate(fromProductStoreBatchPO.getProductionDate()));
        toProductStoreBatchPO.setProductionDate(fromProductStoreBatchPO.getProductionDate());
        toProductStoreBatchPO.setExpireTime(fromProductStoreBatchPO.getExpireTime());
        toProductStoreBatchPO.setBatchTime(fromProductStoreBatchPO.getBatchTime());
        toProductStoreBatchPO.setCreateUserId(fromProductStoreBatchPO.getCreateUserId());
        toProductStoreBatchPO.setTotalCount(fromProductStoreBatchPO.getTotalCount().multiply(new BigDecimal(-1)));
        String batchAttributeNo = batchAttributeInfoBL.getProductBatchAttributeInfoNo(
            toProductStoreBatchPO.getProductStoreId(), null, toProductStoreBatchPO.getProductionDate(),
            toProductStoreBatchPO.getBatchTime(), new ArrayList<>(), true);
        toProductStoreBatchPO.setBatchAttributeInfoNo(batchAttributeNo);
        return toProductStoreBatchPO;
    }

    /**
     * 不同渠道库存转移（处理仓库库存和批次库存）
     */
    private void batchInventoryTransferChannelDiffer(List<BatchInventoryTransferDTO> haveInventoryProcessList) {
        LOGGER.info(String.format("跨渠道库存转移：%s", JSON.toJSONString(haveInventoryProcessList)));
        ArrayList<WarehouseInventoryTransfersDTO> warehouseInventoryTransfersDTOS = new ArrayList<>();
        // todo findSkuIdByStoreId 换批量 k->v
        for (BatchInventoryTransferDTO batchInventoryTransferDTO : haveInventoryProcessList) {
            // 根据storeId查找skuId和仓库id
            WarehouseInventoryTransferPO warehouseInventoryTransferPO =
                productStoreMapper.findSkuIdByStoreId(batchInventoryTransferDTO.getProductStoreId());
            AssertUtils.notNull(warehouseInventoryTransferPO,
                "被转移库存不存在 storeId:" + batchInventoryTransferDTO.getProductStoreId());

            // 构建移库对象
            WarehouseInventoryTransfersDTO warehouseInventoryTransfersDTO = new WarehouseInventoryTransfersDTO();
            warehouseInventoryTransfersDTO.setProductSkuId(warehouseInventoryTransferPO.getProductSkuId());
            warehouseInventoryTransfersDTO.setUnitCount(
                (batchInventoryTransferDTO.getPackageCount().multiply(batchInventoryTransferDTO.getPackageQuantity())
                    .add(batchInventoryTransferDTO.getUnitCount())));
            warehouseInventoryTransfersDTO.setBeChannel(batchInventoryTransferDTO.getFromChannel());
            warehouseInventoryTransfersDTO.setChannel(batchInventoryTransferDTO.getToChannel());
            warehouseInventoryTransfersDTO.setFromLocationId(batchInventoryTransferDTO.getFromLocationId());
            warehouseInventoryTransfersDTO.setToLocationId(batchInventoryTransferDTO.getLocationId());
            warehouseInventoryTransfersDTO.setWarehouseId(warehouseInventoryTransferPO.getWarehouseId());
            warehouseInventoryTransfersDTO.setBatchTime(batchInventoryTransferDTO.getBatchTime());
            warehouseInventoryTransfersDTO.setExpireTime(batchInventoryTransferDTO.getExpireTime());
            warehouseInventoryTransfersDTO.setProductionDate(batchInventoryTransferDTO.getProductionDate());
            warehouseInventoryTransfersDTOS.add(warehouseInventoryTransfersDTO);
        }
        iWarehouseInventoryManageService.batchModTransfersWarehouseInventory(warehouseInventoryTransfersDTOS, 0);
    }

    /**
     * 修改批次库存
     */
    public void updateBatchInventory(List<ProductStoreBatchPO> productStoreBatchPOS,
        PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        if (CollectionUtils.isNotEmpty(productStoreBatchPOS)) {
            List<ProductStoreBatchPO> updateProductStoreBatchPOS = new ArrayList<>();
            Map<String, List<ProductStoreBatchPO>> ProductStoreBatchPOMap =
                productStoreBatchPOS.stream().collect(Collectors.groupingBy(ProductStoreBatchPO::getId));
            ProductStoreBatchPOMap.forEach((key, values) -> {
                if (CollectionUtils.isNotEmpty(values)) {
                    ProductStoreBatchPO storeBatchForUpdate = new ProductStoreBatchPO();
                    BeanUtils.copyProperties(values.get(0), storeBatchForUpdate);
                    if (values.size() > 1) {
                        BigDecimal totalCount = BigDecimal.ZERO;
                        for (ProductStoreBatchPO po : values) {
                            totalCount = totalCount.add(po.getTotalCount());
                        }
                        storeBatchForUpdate.setTotalCount(totalCount);
                    }
                    updateProductStoreBatchPOS.add(storeBatchForUpdate);
                }

            });
            if (CollectionUtils.isNotEmpty(updateProductStoreBatchPOS)) {
                batchInventoryProductStoreBatchMapper.updateBatchInventory(updateProductStoreBatchPOS);
                // 新增批次库存变更记录
                productStoreBatchChangeRecordBL.createStoreBatchChangeRecordByPickUp(pickUpChangeRecordDTO,
                    updateProductStoreBatchPOS);

                defectiveProductPriceEvent.updateDefectiveProductPrice(pickUpChangeRecordDTO.getCityId(),
                    productStoreBatchPOS.get(0).getWarehouseId(), productStoreBatchPOS);

                // 批次库存数量修改到0，则删除促销批次库存
                deletePromotionStoreBatch(updateProductStoreBatchPOS);
            }

        }
    }

    /**
     * 新增批次库存
     */
    public void insertBatchInventoryPOList(List<ProductStoreBatchPO> productStoreBatchPOS,
        PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        // 合并重复数据
        List<ProductStoreBatchPO> insertPOS = mergeDuplicateProductStoreBatch(productStoreBatchPOS);
        if (CollectionUtils.isNotEmpty(insertPOS)) {
            batchInventoryProductStoreBatchMapper.insertBatchInventoryPOList(insertPOS);
            // 新增批次库存变更记录
            productStoreBatchChangeRecordBL.createStoreBatchChangeRecordByPickUp(pickUpChangeRecordDTO, insertPOS);

            defectiveProductPriceEvent.updateDefectiveProductPrice(pickUpChangeRecordDTO.getCityId(),
                productStoreBatchPOS.get(0).getWarehouseId(), productStoreBatchPOS);
        }
    }

    /**
     * 批次库存合并重复数据
     */
    private List<ProductStoreBatchPO> mergeDuplicateProductStoreBatch(List<ProductStoreBatchPO> productStoreBatchPOS) {
        List<ProductStoreBatchPO> ProductStoreBatchList = new ArrayList<>();
        Map<String, List<ProductStoreBatchPO>> productStoreBatchMap = productStoreBatchPOS.stream()
            .collect(Collectors.groupingBy(ProductStoreBatchPO::getMergeProductStoreBatch));
        productStoreBatchMap.forEach((no, productStoreBatch) -> {
            ProductStoreBatchPO productStoreBatchPO = productStoreBatch.get(0);
            BigDecimal totalCount = productStoreBatch.stream().map(ProductStoreBatchPO::getTotalCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            productStoreBatchPO.setTotalCount(totalCount);
            ProductStoreBatchList.add(productStoreBatchPO);
        });

        return ProductStoreBatchList;
    }

    /**
     * 产品拣货按二级货主优先级排序
     *
     * @return
     */
    private List<PickUpDTO> getSortPickUpDTOS(List<PickUpDTO> pickUpDTOList) {
        if (CollectionUtils.isEmpty(pickUpDTOList)) {
            return Collections.EMPTY_LIST;
        }
        // 获取优先级规则
        OrderProcessRuleConfigDTO orderProcessRuleConfigDTO =
            iOrderProcessRuleConfigService.findByWarehouseIdAndOrderType(pickUpDTOList.get(0).getWarehouseId(),
                ProcessRuleOrderTypeEnum.拣货移库.getType(), true);
        if (orderProcessRuleConfigDTO == null) {
            LOGGER.warn("[拣货移库]优先级为空！");
            return pickUpDTOList;
        }
        ProcessOrderTypeEnum orderTypeEnum = ProcessOrderTypeEnum.getEnum(orderProcessRuleConfigDTO.getProcessType());
        // 按二级货主排序
        pickUpDTOList = pickUpDTOList.stream()
            .sorted(Comparator.nullsFirst(Comparator.comparing(PickUpDTO::getProductSkuId)
                .thenComparing(PickUpDTO::getSecOwnerId, new SecOwnerIdComparator(orderTypeEnum))))
            .collect(Collectors.toList());
        return pickUpDTOList;
    }

    /**
     * 上下架产品拣货去重
     *
     * @param pickUpDTOList
     * @return
     */
    private List<PickUpDTO> getDistinctPickUpDTOS(List<PickUpDTO> pickUpDTOList) {
        List<PickUpDTO> lstNewDTOs = new ArrayList<>();
        pickUpDTOList.forEach(p -> {
            // 将相同上下架产品的拣货数量规整
            if (lstNewDTOs.stream().anyMatch(q -> q.toStringForCheck().equals(p.toStringForCheck()))) {
                PickUpDTO pickUpDTO = lstNewDTOs.stream().filter(q -> q.toStringForCheck().equals(p.toStringForCheck()))
                    .findFirst().get();
                pickUpDTO.setCount(pickUpDTO.getCount().add(p.getCount()));
            } else {
                lstNewDTOs.add(p);
            }
        });
        return lstNewDTOs;
    }

    /**
     * 过滤出开启货位库存的拣货产品
     *
     * @param pickUpDTOList
     * @return
     */
    // private List<PickUpDTO> getOpenLocationPickUpDTOs(List<PickUpDTO> pickUpDTOList){
    // Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
    // List<Long> skuIds = pickUpDTOList.stream().map(p->p.getProductSkuId()).distinct().collect(Collectors.toList());
    // Map<Long, ProductCategoryDTO> categoryDTOMap =
    // iProductCategoryService.findOpenLocationCategoryBySkuIds(warehouseId, skuIds);
    //
    // List<PickUpDTO> lstNewDTOs = new ArrayList<>();
    // if (null != categoryDTOMap && categoryDTOMap.size() != 0) {
    // lstNewDTOs =
    // pickUpDTOList.stream().filter(p->categoryDTOMap.keySet().contains(p.getProductSkuId())).collect(Collectors.toList());
    // }
    // return lstNewDTOs;
    // }

    /**
     * 波次拣货完成/上架移库
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public List<PickUpDTO> batchInventoryTransferBySku(List<PickUpDTO> pickUpDTOList,
        PickUpChangeRecordDTO pickUpChangeRecordDTO, Boolean isIngoreProductionDate,
        Boolean isIngoreHasNotEnoughStore) {
        List<PickUpDTO> realPickUpDTOList = new ArrayList<>();
        // 校验移库
        List<BatchInventoryTransferDTO> batchInventoryTransferDTOList =
            checkInventoryTransfer(pickUpDTOList, isIngoreProductionDate, isIngoreHasNotEnoughStore, realPickUpDTOList);
        if (CollectionUtils.isEmpty(batchInventoryTransferDTOList)) {
            return Collections.EMPTY_LIST;
        }

        // 转换[to]_location货位为货位组
        transformBatchInventoryTransferToLocGroup(batchInventoryTransferDTOList, pickUpDTOList.get(0).getWarehouseId());

        // 批次库存转移货位
        batchInventoryTransfer(batchInventoryTransferDTOList, pickUpChangeRecordDTO);
        // 返回实际移库数量
        return realPickUpDTOList;
    }

    /**
     * 校验移库
     *
     * @return
     */
    public List<BatchInventoryTransferDTO> checkInventoryTransfer(List<PickUpDTO> pickUpDTOList,
        Boolean isIngoreProductionDate, Boolean isIngoreHasNotEnoughStore, List<PickUpDTO> realPickUpDTOList) {
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return null;
        }
        // 是否开启货位组
        boolean isOpenLocationGroup = warehouseConfigService.isOpenLocationGroup(warehouseId);

        // 转换[from]_Location货位为货位组
        List<PickUpDTO> pickUpDTOTransformList =
            isOpenLocationGroup ? transformPickUpToLocGroup(pickUpDTOList) : pickUpDTOList;

        // 1、相同产品去重
        pickUpDTOList = getDistinctPickUpDTOS(pickUpDTOList);
        // 2、按二级货主优先级排序
        pickUpDTOList = getSortPickUpDTOS(pickUpDTOList);
        // 3、获取目标货位配置信息，并且校验是否存在
        List<LocationReturnDTO> lstLocation = getLocationDTOs(pickUpDTOList);
        // 4、获取产品sku信息，并且校验是否存在
        List<ProductSkuPO> lstProductSku = getProductSkuPOs(pickUpDTOList);
        // 找出需要进行库龄管控的产品
        // List<Long> storeAgeSkuIds = findStoreAgeSku(lstProductSku, warehouseId);
        // 5、获取来源货位库存信息
        Map<String, List<BatchLocationInfoDTO>> batchLocationInfoMap =
            getBatchLocationInfoMap(pickUpDTOTransformList, isIngoreProductionDate);
        // Map<Long, BigDecimal> pickedOccupySkuCountMap = getPickedOccupySkuCount(pickUpDTOList);
        LOGGER.info("来源货位库存信息：{}", JSON.toJSONString(batchLocationInfoMap));
        LOGGER.info("pickUpDTOTransformList={}", JSON.toJSONString(pickUpDTOTransformList));
        LOGGER.info("isOpenLocationGroup={}", isOpenLocationGroup);
        // 6、移库操作
        List<BatchInventoryTransferDTO> batchInventoryTransferDTOList = new ArrayList<>();
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            Long productSkuId = pickUpDTO.getProductSkuId();
            // 转换后的fromLocationId
            Long fromLocationId = isOpenLocationGroup
                ? pickUpDTOTransformList.stream().filter(e -> Objects.equals(e.getTempId(), pickUpDTO.getTempId()))
                    .map(PickUpDTO::getFromLocationId).findAny().orElse(null)
                : pickUpDTO.getFromLocationId();

            Long locationId = pickUpDTO.getLocationId();
            Long secOwnerId = pickUpDTO.getSecOwnerId();
            Boolean isAutoAllot = pickUpDTO.getAutoAllotFlag();
            // 校验产品sku
            ProductSkuPO productSkuPO =
                lstProductSku.stream().filter(p -> p.getProductSkuId().equals(productSkuId)).findAny().orElse(null);
            AssertUtils.notNull(productSkuPO, String.format("产品不存在！SkuID:%s", productSkuId));
            // 校验目标货位
            LocationReturnDTO locationReturnDTO =
                lstLocation.stream().filter(p -> p.getId().equals(locationId)).findAny().orElse(null);
            AssertUtils.notNull(locationReturnDTO, String.format("找不到目标货位！Sku:%s,%s,LocationId:%s",
                productSkuPO.getProductSkuId(), productSkuPO.getName(), locationId));
            // 校验来源货位
            LOGGER.info("校验来源货位：{}", productSkuId + "|" + fromLocationId + "|" + pickUpDTO.getFromSource() + "|"
                + pickUpDTO.getProductionDateStr() + "|" + pickUpDTO.getFromChannel());
            List<BatchLocationInfoDTO> batchLocationInfoDTOS =
                batchLocationInfoMap.get(productSkuId + "|" + fromLocationId + "|" + pickUpDTO.getFromSource() + "|"
                    + pickUpDTO.getProductionDateStr() + "|" + pickUpDTO.getFromChannel());

            // 若不自动分配库存，则严格按照二级货主筛选来源货位
            if (Objects.equals(isAutoAllot, false)) {
                batchLocationInfoDTOS = batchLocationInfoDTOS.stream()
                    .filter(p -> Objects.equals(p.getSecOwnerId(), secOwnerId)).collect(Collectors.toList());
            }

            AssertUtils.notEmpty(batchLocationInfoDTOS, String.format("来源货位不存在或库存不足，请先盘点或移库！Sku:%s,%s",
                productSkuPO.getProductSkuId(), productSkuPO.getName()));

            // 按照生产日期排序
            batchLocationInfoDTOS = batchLocationInfoSortedByProductionDate(batchLocationInfoDTOS);

            // 按照二级货主排序（优先处理跟自身相同的二级货主，其次处理其他）
            batchLocationInfoDTOS = batchLocationInfoSortedBySecOwnerId(batchLocationInfoDTOS, secOwnerId);

            // 转移的总数量
            BigDecimal transferTotalCount = pickUpDTO.getCount();
            for (int i = 0; i < batchLocationInfoDTOS.size(); i++) {
                BatchLocationInfoDTO batchLocationInfoDTO = batchLocationInfoDTOS.get(i);
                if (transferTotalCount.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }

                BatchInventoryTransferDTO batchInventoryTransferDTO =
                    getBatchInventoryTransferDTO(pickUpDTO, locationReturnDTO, batchLocationInfoDTO);
                // 如果开启货位组，使用货区ID，否则，使用货位ID
                batchInventoryTransferDTO.setFromLocationId(fromLocationId);
                // 当前批次库存的总数量
                BigDecimal batchCount = batchLocationInfoDTO.getTotalCount();
                // 当前批次库存的转移数量
                BigDecimal changeCount;

                // 如果要转移的数量，大于批次库存的数量，本次只转移批次库存数量
                // 如果要转移的数量，小于等于批次库存数量，则全部转移
                if (transferTotalCount.compareTo(batchCount) > 0) {
                    // 如果原库存小于0，本次新增库存，则优先补到0，剩余的轮训其他负库存货位
                    if (transferTotalCount.compareTo(BigDecimal.ZERO) < 0
                        && batchCount.compareTo(BigDecimal.ZERO) < 0) {
                        changeCount = (transferTotalCount.abs().min(batchCount.abs())).multiply(new BigDecimal(-1));
                        LOGGER.info(String.format("[负库存补零-移库]库存ID:%s,货位[%s-%s]库存:%s小件，补%s小件",
                            batchLocationInfoDTO.getProductStoreId(), batchLocationInfoDTO.getLocationId(),
                            batchLocationInfoDTO.getLocationName(), batchCount.stripTrailingZeros().toPlainString(),
                            changeCount.abs().stripTrailingZeros().toPlainString()));
                    } else {
                        changeCount = batchCount;
                    }
                } else {
                    changeCount = transferTotalCount;
                }

                // // 最后一个，且剩余待移库数量不等于0，如果库存不足，全部扣到最后一个货位上
                // if (i == batchLocationInfoDTOS.size() - 1) {
                // if (transferTotalCount.compareTo(batchCount) != 0 && isIngoreHasNotEnoughStore) {
                // LOGGER.info(String.format("SKU:%s-%s,来源货位[%s]库存不足,缺%s小件，忽略继续扣！", productSkuPO.getProductSkuId(),
                // productSkuPO.getName(), batchLocationInfoDTO.getLocationName(),
                // transferTotalCount.stripTrailingZeros().toPlainString()));
                // changeCount = transferTotalCount;
                // }
                // }
                boolean isLastMoveByRealCount = false;
                if (i == batchLocationInfoDTOS.size() - 1 && transferTotalCount.compareTo(batchCount) != 0) {
                    if (pickUpDTO.getMoveByRealFlag()) {
                        // 最后一个，且剩余待移库数量不等于0，如果库存不足，按实际数量扣除
                        LOGGER
                            .info(String.format("SKU:%s-%s,来源货位[%s]库存不足,缺%s小件，按实际数量扣！", productSkuPO.getProductSkuId(),
                                productSkuPO.getName(), batchLocationInfoDTO.getLocationName(),
                                transferTotalCount.stripTrailingZeros().toPlainString()));
                        isLastMoveByRealCount = true;
                        pickUpDTO.setCount(pickUpDTO.getCount().add(transferTotalCount.subtract(batchCount)));
                    } else if (isIngoreHasNotEnoughStore && !pickUpDTO.getMoveByRealFlag()) {
                        // 原忽略逻辑，最后一个，且剩余待移库数量不等于0，如果库存不足，全部扣到最后一个货位上,但会产生负库存
                        LOGGER.info(String.format("SKU:%s-%s,来源货位[%s]库存不足,缺%s小件，忽略继续扣！", productSkuPO.getProductSkuId(),
                            productSkuPO.getName(), batchLocationInfoDTO.getLocationName(),
                            transferTotalCount.stripTrailingZeros().toPlainString()));
                        changeCount = transferTotalCount;
                    }
                }
                batchInventoryTransferDTO.setUnitCount(changeCount);
                transferTotalCount = isLastMoveByRealCount ? BigDecimal.ZERO : transferTotalCount.subtract(changeCount);
                batchLocationInfoDTO.setTotalCount(batchCount.subtract(changeCount));

                if (batchInventoryTransferDTO.getUnitCount().compareTo(BigDecimal.ZERO) != 0) {
                    LOGGER.info(String.format("SKU:%s-%s,本次移库数量：%s，总移库数量：%s，剩余数量：%s", productSkuPO.getProductSkuId(),
                        productSkuPO.getName(), changeCount, pickUpDTO.getCount(), transferTotalCount));
                    batchInventoryTransferDTOList.add(batchInventoryTransferDTO);

                    // 记录实际移库数据
                    if (realPickUpDTOList != null) {
                        PickUpDTO realPickUpDTO = new PickUpDTO();
                        BeanUtils.copyProperties(pickUpDTO, realPickUpDTO);
                        realPickUpDTO.setCount(batchInventoryTransferDTO.getUnitCount());
                        realPickUpDTO.setSecOwnerId(batchLocationInfoDTO.getSecOwnerId());
                        realPickUpDTO.setFromLocationName(batchLocationInfoDTO.getLocationName());
                        realPickUpDTO.setLocationName(batchInventoryTransferDTO.getLocationName());
                        realPickUpDTOList.add(realPickUpDTO);
                    }
                }
            }

            if (transferTotalCount.compareTo(BigDecimal.ZERO) != 0) {
                Set<Long> skuSet = new HashSet<>(Collections.singletonList(productSkuPO.getProductSkuId()));
                String code = "";
                try {
                    Map<Long, ProductCodeDTO> productCodeDTOMap =
                        iProductSkuService.getPackageAndUnitCode(skuSet, productSkuPO.getCityId());
                    ProductCodeDTO productCodeDTO = productCodeDTOMap.get(productSkuPO.getProductSkuId());
                    if (CollectionUtils.isNotEmpty(productCodeDTO.getUnitCode())) {
                        code = productCodeDTO.getUnitCode().get(0);
                    } else if (CollectionUtils.isNotEmpty(productCodeDTO.getPackageCode())) {
                        code = productCodeDTO.getPackageCode().get(0);
                    }
                } catch (Exception e) {
                    LOGGER.warn("查询条码失败" + productSkuPO.getProductSkuId(), e);
                }
                throw new DataValidateException(String.format("来源货位[%s]库存不足！Sku:%s; 条码: %s,%s,缺%s小件",
                    (CollectionUtils.isNotEmpty(batchLocationInfoDTOS) && batchLocationInfoDTOS.get(0) != null)
                        ? batchLocationInfoDTOS.get(0).getLocationName() : "",
                    productSkuPO.getProductSkuId(), code, productSkuPO.getName(),
                    transferTotalCount.stripTrailingZeros().toPlainString()));
            }
        }
        return batchInventoryTransferDTOList;
    }

    /**
     * SCM-9962 周转区、集货区 负库存优化治理<br/>
     * 周转区、集货位库存要跟分拣占用数量挂钩，分拣占用了，就不允许移库，只能通过出库或者反架动作操作分拣占用库存，出库扣减库存，反架移动库存 只有大于分拣占用数量的库存，允许移库操作
     */
    private void validateLocationPickedOccupyCount(List<BatchLocationInfoDTO> batchLocationInfoDTOS,
        PickUpDTO pickUpDTO, Long fromLocationId) {}

    private Map<Long, BigDecimal> getPickedOccupySkuCount(List<PickUpDTO> pickUpDTOList) {
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        Warehouse warehouse = warehouseQueryService.findWarehouseById(warehouseId);
        List<Long> skuIds = pickUpDTOList.stream().map(PickUpDTO::getProductSkuId).collect(Collectors.toList());
        return iBatchTaskQueryService.findPickedCountBySkuIdForSCM25(warehouse.getCityId(), warehouseId, skuIds,
            Boolean.FALSE);
    }

    @Transactional(rollbackFor = Exception.class)
    public void putAwayTaskBatchPickUp(List<PickUpRecordDTO> pickUpChangeRecordList, Integer warehouseId) {
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return;
        }
        for (PickUpRecordDTO recordDTO : pickUpChangeRecordList) {
            batchInventoryTransferBySku(recordDTO.getPickUpDTOList(), recordDTO.getPickUpChangeRecordDTO(), false,
                false);
        }
    }

    /**
     * 批次库存优先处理指定二级货主的，其次处理其他
     *
     * @return
     */
    public List<BatchLocationInfoDTO>
        batchLocationInfoSortedBySecOwnerId(List<BatchLocationInfoDTO> batchLocationInfoDTOS, Long secOwnerId) {
        List<BatchLocationInfoDTO> lstBatchLocationInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(batchLocationInfoDTOS)) {
            // 优先二级货主跟自身相同的
            List<BatchLocationInfoDTO> selfTmp = batchLocationInfoDTOS.stream()
                .filter(p -> Objects.equals(p.getSecOwnerId(), secOwnerId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(selfTmp)) {
                lstBatchLocationInfoList.addAll(selfTmp);
            }
            // 其次二级货主不等于自身的
            List<BatchLocationInfoDTO> otherTmp = batchLocationInfoDTOS.stream()
                .filter(p -> !Objects.equals(p.getSecOwnerId(), secOwnerId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(otherTmp)) {
                lstBatchLocationInfoList.addAll(otherTmp);
            }
        }
        return lstBatchLocationInfoList;
    }

    /**
     * 库存排序规则-（生产日期+批次时间） 1、生产日期（为空的排最前） 2、批次时间（为空的排最前）
     *
     * @param batchLocationInfoDTOS
     * @return
     */
    private List<BatchLocationInfoDTO>
        batchLocationInfoSortedByProductionDate(List<BatchLocationInfoDTO> batchLocationInfoDTOS) {
        List<BatchLocationInfoDTO> lstBatchLocationInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(batchLocationInfoDTOS)) {
            List<Date> lstAllProductionDates = new ArrayList<>();
            lstAllProductionDates.add(null);
            List<Date> lstTmpProductionDates = batchLocationInfoDTOS.stream().filter(p -> p.getProductionDate() != null)
                .map(BatchLocationInfoDTO::getProductionDate).distinct().sorted().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lstTmpProductionDates)) {
                lstAllProductionDates.addAll(lstTmpProductionDates);
            }

            for (Date dtProducttionDate : lstAllProductionDates) {
                List<BatchLocationInfoDTO> lstTmp =
                    batchLocationInfoSortedByBatchTime(batchLocationInfoDTOS, dtProducttionDate);
                if (CollectionUtils.isNotEmpty(lstTmp)) {
                    lstBatchLocationInfoList.addAll(lstTmp);
                }
            }
            if (lstTmpProductionDates.size() > 1) {
                LOGGER.info(String.format("存在多个生产日期！生产日期：%s，批次库存排序结果：%s", JSON.toJSONString(lstAllProductionDates),
                    JSON.toJSONString(lstBatchLocationInfoList)));
            }
        }
        return lstBatchLocationInfoList;
    }

    /**
     * 同一生产日期的批次库存排序规则 批次时间（为空的排最前）
     *
     * @param batchLocationInfoDTOS
     * @return
     */
    private List<BatchLocationInfoDTO>
        batchLocationInfoSortedByBatchTime(List<BatchLocationInfoDTO> batchLocationInfoDTOS, Date dtProducttionDate) {
        List<BatchLocationInfoDTO> lstTmp = batchLocationInfoDTOS.stream()
            .filter(p -> Objects.equals(dtProducttionDate, p.getProductionDate())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lstTmp)) {
            // 批次为空的数据
            List<BatchLocationInfoDTO> lstNoBatchDate =
                lstTmp.stream().filter(p -> p.getBatchTime() == null).collect(Collectors.toList());
            // 优先扣批次日期为空或者批次临期的
            List<BatchLocationInfoDTO> lstHasBatchDate = lstTmp.stream().filter(p -> p.getBatchTime() != null)
                .sorted(Comparator.nullsFirst(Comparator.comparing(BatchLocationInfoDTO::getBatchTime)))
                .collect(Collectors.toList());
            lstTmp = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(lstNoBatchDate)) {
                lstTmp.addAll(lstNoBatchDate);
            }
            if (CollectionUtils.isNotEmpty(lstHasBatchDate)) {
                lstTmp.addAll(lstHasBatchDate);
            }
        }
        return lstTmp;
    }

    /**
     * 查找库龄产品
     *
     * @param productSkuList
     * @param warehouseId
     * @return
     */
    private List<Long> findStoreAgeSku(List<ProductSkuPO> productSkuList, Integer warehouseId) {
        List<Long> skuIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(productSkuList)) {
            return skuIds;
        }
        // 有货主的默认为库龄产品
        List<Long> ownerSkuIds = productSkuList.stream().filter(sku -> sku.getCompanyId() != null)
            .map(ProductSkuPO::getProductSkuId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ownerSkuIds)) {
            skuIds.addAll(ownerSkuIds);
        }
        StockAgeStrategyConfigQueryDTO stockAgeStrategyConfigQueryDTO = new StockAgeStrategyConfigQueryDTO();
        stockAgeStrategyConfigQueryDTO.setWarehouseId(warehouseId);
        stockAgeStrategyConfigQueryDTO.setRelatedIds(
            productSkuList.stream().map(ProductSkuPO::getProductSkuId).distinct().collect(Collectors.toList()));
        List<StockAgeStrategyConfigDTO> stockAgeStrategyConfigList =
            stockAgeStrategyService.pageListStockAgeStrategyConfig(stockAgeStrategyConfigQueryDTO).getDataList();
        if (CollectionUtils.isNotEmpty(stockAgeStrategyConfigList)) {
            skuIds.addAll(stockAgeStrategyConfigList.stream().map(StockAgeStrategyConfigDTO::getRelatedId)
                .collect(Collectors.toList()));
        }
        return skuIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 封装库存转移对象
     *
     * @return
     */
    private BatchInventoryTransferDTO getBatchInventoryTransferDTO(PickUpDTO pickUpDTO,
        LocationReturnDTO locationReturnDTO, BatchLocationInfoDTO batchLocationInfoDTO) {
        BatchInventoryTransferDTO batchInventoryTransferDTO = new BatchInventoryTransferDTO();
        batchInventoryTransferDTO.setStoreBatchId(batchLocationInfoDTO.getStoreBatchId());
        batchInventoryTransferDTO.setProductStoreId(batchLocationInfoDTO.getProductStoreId());
        batchInventoryTransferDTO.setFromLocationId(pickUpDTO.getFromLocationId());
        batchInventoryTransferDTO.setLocationId(pickUpDTO.getLocationId());
        batchInventoryTransferDTO.setLocationName(locationReturnDTO.getName());
        batchInventoryTransferDTO.setLocationCategory(Integer.valueOf(locationReturnDTO.getCategory()));
        batchInventoryTransferDTO.setSubcategory(Integer.valueOf(locationReturnDTO.getSubcategory()));
        batchInventoryTransferDTO.setFromChannel(pickUpDTO.getFromChannel());
        batchInventoryTransferDTO.setToChannel(pickUpDTO.getToChannel());
        batchInventoryTransferDTO.setBatchTime(pickUpDTO.getBatchTime());
        batchInventoryTransferDTO.setExpireTime(pickUpDTO.getExpireTime());
        batchInventoryTransferDTO.setProductionDate(pickUpDTO.getProductionDate());
        return batchInventoryTransferDTO;
    }

    /**
     * 获取目标货位信息
     *
     * @return
     */
    private List<LocationReturnDTO> getLocationDTOs(List<PickUpDTO> pickUpDTOList) {
        List<String> lstLocationIds = pickUpDTOList.stream().filter(p -> p.getLocationId() != null)
            .map(p -> p.getLocationId().toString()).distinct().collect(Collectors.toList());
        AssertUtils.notEmpty(lstLocationIds, "货位不能为空！");
        List<LocationReturnDTO> lstLocation = locationAreaService.findLocationListById(lstLocationIds);
        List<String> lstNoLocationIds =
            lstLocationIds.stream().filter(p -> !lstLocation.stream().anyMatch(q -> q.getId().toString().equals(p)))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lstNoLocationIds)) {
            throw new BusinessException(String.format("找不到目标货位！LocationIds:%s", JSON.toJSONString(lstNoLocationIds)));
        }
        return lstLocation;
    }

    /**
     * 获取产品sku信息
     *
     * @return
     */
    private List<ProductSkuPO> getProductSkuPOs(List<PickUpDTO> pickUpDTOList) {
        List<Long> lstSkuIds =
            pickUpDTOList.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        AssertUtils.notEmpty(lstSkuIds, "产品Id不能为空！");
        List<ProductSkuPO> lstProductSku = batchInventoryProductSkuMapper.getProductSkuListByIds(lstSkuIds);
        List<Long> lstNoSkuIds =
            lstSkuIds.stream().filter(p -> !lstProductSku.stream().anyMatch(q -> q.getProductSkuId().equals(p)))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lstNoSkuIds)) {
            throw new BusinessException(String.format("产品Id不存在！SkuIDS:%s", JSON.toJSONString(lstNoSkuIds)));
        }
        return lstProductSku;
    }

    /**
     * 根据仓库id+类型查询货位或货区
     *
     * @param warehouseId
     * @return
     */
    public LocationReturnDTO getLoactionByWarehouseId(Integer warehouseId, Integer cityId, Byte subcategory,
        BigDecimal changeCount) {
        // 根据仓库id+类型查询货位或货区，不存在时则自动创建货位
        LocationReturnDTO locationReturnDTO =
            iLocationService.getLocationByWarehouseIdByAutoCreate(warehouseId, cityId, subcategory);
        AssertUtils.notNull(locationReturnDTO, "找不到货区:仓库id" + warehouseId);

        // if (changeCount > 0) {
        // //如果有多个货位满足条件，根据货位容量，取最大容量的一个货位
        // if (locationList.size() > 1) {
        // List<Long> lstLocationIds = locationList.stream().filter(p -> p.getId() !=
        // null).map(LocationReturnDTO::getId).collect(Collectors.toList());
        // final List<LocationCapacityPO> lstLocationCapacitys =
        // productStoreBatchMapper.getLocationCapacityPOs(lstLocationIds)
        // .stream().sorted((o1, o2) ->
        // o2.getLeftCapacity().compareTo(o1.getLeftCapacity())).collect(Collectors.toList());
        // LOGGER.info(String.format("获取推荐货位结果：%s",lstLocationCapacitys));
        // //分配到货位剩余容量最大的一个货位
        // result = locationList.stream().filter(p -> p.getId() != null &&
        // p.getId().equals(lstLocationCapacitys.get(0).getLocation_id())).findAny().get();
        // }
        // }
        // LOGGER.info(String.format("查询仓库货位，仓库Id：%s,货区类别：%s,结果：%s", warehouseId, subcategory,
        // JSON.toJSONString(result)));
        return locationReturnDTO;
    }

    /**
     * 根据拣货产品的sku查询货区相关信息
     */
    private Map<String, List<BatchLocationInfoDTO>> getBatchLocationInfoMap(List<PickUpDTO> pickUpDTOList,
        Boolean isIngoreProductionDate) {
        List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS = new ArrayList<>();
        // 根据skuId查询货区货位信息.
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            BatchLocationInfoQueryDTO batchLocationInfoQueryDTO = new BatchLocationInfoQueryDTO();
            batchLocationInfoQueryDTO.setProductSkuId(pickUpDTO.getProductSkuId());
            batchLocationInfoQueryDTO.setWarehouseId(pickUpDTO.getWarehouseId());
            batchLocationInfoQueryDTO.setChannel(pickUpDTO.getFromChannel());
            batchLocationInfoQueryDTO.setLocationId(pickUpDTO.getFromLocationId());
            batchLocationInfoQueryDTO.setSource(pickUpDTO.getFromSource());
            batchLocationInfoQueryDTO.setProductionDate(pickUpDTO.getProductionDate());
            batchLocationInfoQueryDTO.setExcludeNegativeFlag(pickUpDTO.getExcludeNegativeFlag());
            batchLocationInfoQueryDTOS.add(batchLocationInfoQueryDTO);
        }
        return batchInventoryQueryBL.findBatchLocationDTOBySkuAndCityId(batchLocationInfoQueryDTOS,
            isIngoreProductionDate);
    }

    // /**
    // * 根据拣货产品的sku及货区Id查询货位相关信息
    // */
    // private Map<Long, List<BatchLocationInfoDTO>> getBatchHuoWeiDTOInfoMap(List<PickUpDTO> pickUpDTOList) {
    // List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS = new ArrayList<>();
    // //根据skuId查询货区货位信息.
    // for (PickUpDTO pickUpDTO : pickUpDTOList) {
    // BatchLocationInfoQueryDTO batchLocationInfoQueryDTO = new BatchLocationInfoQueryDTO();
    // batchLocationInfoQueryDTO.setProductSkuId(pickUpDTO.getProductSkuId());
    // batchLocationInfoQueryDTO.setWarehouseId(pickUpDTO.getWarehouseId());
    // batchLocationInfoQueryDTO.setChannel(pickUpDTO.getFromChannel());
    // batchLocationInfoQueryDTO.setSource(pickUpDTO.getFromSource());
    // batchLocationInfoQueryDTO.setLocationId(pickUpDTO.getFromLocationId());
    // batchLocationInfoQueryDTOS.add(batchLocationInfoQueryDTO);
    // }
    // return batchInventoryQueryBL.findBatchHuoWeiDTOBySku(batchLocationInfoQueryDTOS);
    // }

    // /**
    // * 根据货区查可用货位（所有货位，库存信息可能不存在）(areaId必传)
    // *
    // * @param pickUpDTOList
    // * @return
    // */
    // public Map<Long, List<BatchLocationInfoDTO>> getBatchHuoWeiDTOInfoMapByArea(List<PickUpDTO> pickUpDTOList) {
    // List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS = new ArrayList<>();
    // //根据skuId查询货区货位信息.
    // for (PickUpDTO pickUpDTO : pickUpDTOList) {
    // BatchLocationInfoQueryDTO batchLocationInfoQueryDTO = new BatchLocationInfoQueryDTO();
    // batchLocationInfoQueryDTO.setProductSkuId(pickUpDTO.getProductSkuId());
    // batchLocationInfoQueryDTO.setWarehouseId(pickUpDTO.getWarehouseId());
    // batchLocationInfoQueryDTO.setChannel(pickUpDTO.getFromChannel());
    // batchLocationInfoQueryDTO.setSource(pickUpDTO.getFromSource());
    // batchLocationInfoQueryDTO.setLocationId(pickUpDTO.getFromLocationId());
    // batchLocationInfoQueryDTOS.add(batchLocationInfoQueryDTO);
    // }
    // return batchInventoryQueryBL.findBatchHuoWeiDTOBySkuAndArea(batchLocationInfoQueryDTOS);
    // }

    /**
     * 修改货位库存（只修改数量）
     *
     * @param productStoreBatchDTOS
     */
    public void updateProductStoreBatch(List<ProductStoreBatchDTO> productStoreBatchDTOS, String operaterUser) {
        if (CollectionUtils.isEmpty(productStoreBatchDTOS)) {
            return;
        }
        List<ProductStoreBatchPO> poList = productStoreBatchDTOS.stream().map(dto -> {
            ProductStoreBatchPO po = new ProductStoreBatchPO();
            BeanUtils.copyProperties(dto, po);
            return po;
        }).collect(Collectors.toList());
        batchInventoryProductStoreBatchMapper.updateBatchInventoryAdd(poList);

        // 新增批次库存变更记录
        PickUpChangeRecordDTO pickUpChangeRecordDTO = new PickUpChangeRecordDTO();
        pickUpChangeRecordDTO.setOrderNo(productStoreBatchDTOS.get(0).getBatchAttributeInfoNo());
        pickUpChangeRecordDTO.setOrderType(StoreOrderType.OP_MANUAL_ORDER);
        pickUpChangeRecordDTO.setJiupiEventType(JiupiEventType.手动修改.getType());
        pickUpChangeRecordDTO.setDescription(InventoryChangeTypeEnum.设置货位库存数量.name());
        pickUpChangeRecordDTO.setCreateUser(operaterUser);
        productStoreBatchChangeRecordBL.createStoreBatchChangeRecordByPickUp(pickUpChangeRecordDTO, poList);
    }

    public List<BatchInventoryInfoUpdateDTO>
        updateBatchNoByList(List<BatchInventoryInfoUpdateDTO> batchInventoryInfoUpdateDTOS) {
        if (CollectionUtils.isEmpty(batchInventoryInfoUpdateDTOS)) {
            return batchInventoryInfoUpdateDTOS;
        }
        batchInventoryInfoUpdateDTOS.forEach(info -> {
            if (info.getProductionDate() != null) {
                String productBatchAttributeInfoNo =
                    batchAttributeInfoBL.getProductBatchAttributeInfoNo(info.getProductStoreId(), info.getOwnerType(),
                        info.getProductionDate(), info.getBatchTime(), new ArrayList<>(), true);
                // 只更新批次编号不一样的
                if (!Objects.equals(productBatchAttributeInfoNo, info.getBatchAttributeInfoNo())) {
                    info.setUpdateBatchNo(true);
                    info.setBatchAttributeInfoNo(productBatchAttributeInfoNo);
                }
            }
        });

        batchInventoryInfoUpdateDTOS = batchInventoryInfoUpdateDTOS
            .stream().filter(info -> info.getProductionDate() != null
                && !StringUtils.isEmpty(info.getBatchAttributeInfoNo()) && info.isUpdateBatchNo())
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(batchInventoryInfoUpdateDTOS)) {
            fixExpireTime(batchInventoryInfoUpdateDTOS);
            LOGGER.info("修改批次库存信息：{}", JSON.toJSONString(batchInventoryInfoUpdateDTOS));
            batchInventoryProductStoreBatchMapper.updateProductStoreBatchNoAndDate(batchInventoryInfoUpdateDTOS);
        }

        return batchInventoryInfoUpdateDTOS;
    }

    /**
     * 2022-04-25 修改生产日期时，重新计算过期时间
     *
     * @param lstDTO
     */
    private void fixExpireTime(List<BatchInventoryInfoUpdateDTO> lstDTO) {
        List<String> lstStoreId = lstDTO.stream().filter(p -> !Objects.equals(null, p.getProductionDate()))
            .map(BatchInventoryInfoUpdateDTO::getProductStoreId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstStoreId)) {
            return;
        }
        List<BatchInventoryPO> lstShelfUnitInfo =
            batchInventoryProductStoreBatchMapper.findInventoryShelfUnitInfoById(lstStoreId);
        lstDTO.forEach(p -> {
            if (p.getProductionDate() == null) {
                return;
            }
            Optional<BatchInventoryPO> any = lstShelfUnitInfo.stream()
                .filter(q -> Objects.equals(q.getProductStoreId(), p.getProductStoreId())).findAny();
            if (!any.isPresent()) {
                return;
            }
            BatchInventoryPO batchInventoryPO = any.get();
            // 计算新的过期时间
            Date expireTime = BatchInventoryConvert.countExpireTime(p.getProductionDate(),
                batchInventoryPO.getMonthOfShelfLife(), batchInventoryPO.getShelfLifeUnit());
            p.setExpireTime(expireTime);
        });
    }

    /**
     * 更新货位库存信息
     *
     * @param batchInventoryInfoUpdateDTOS 更新内容
     * @param ignoreProductionDateNull 生产日期是否能为null
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<BatchInventoryInfoUpdateDTO> updateBatchInventoryInfo(
        List<BatchInventoryInfoUpdateDTO> batchInventoryInfoUpdateDTOS, boolean ignoreProductionDateNull) {
        if (CollectionUtils.isEmpty(batchInventoryInfoUpdateDTOS)) {
            return batchInventoryInfoUpdateDTOS;
        }
        LOGGER.info("修改批次库存信息：{}", JSON.toJSONString(batchInventoryInfoUpdateDTOS));
        batchInventoryInfoUpdateDTOS.forEach(info -> {
            if (info.getProductionDate() != null || ignoreProductionDateNull) {
                String productBatchAttributeInfoNo =
                    batchAttributeInfoBL.getProductBatchAttributeInfoNo(info.getProductStoreId(), info.getOwnerType(),
                        info.getProductionDate(), info.getBatchTime(), new ArrayList<>(), true);

                info.setBatchAttributeInfoNo(productBatchAttributeInfoNo);
            }
        });

        List<String> storeBatchIdS =
            batchInventoryInfoUpdateDTOS.stream().filter(p -> StringUtils.isNotEmpty(p.getStoreBatchId()))
                .map(p -> p.getStoreBatchId()).distinct().collect(Collectors.toList());
        Map<String,
            Date> productDateMap = batchInventoryProductStoreBatchMapper.findProductStoreBatchListById(storeBatchIdS)
                .stream().filter(p -> p != null && p.getProductionDate() != null)
                .collect(Collectors.toMap(p -> p.getId(), p -> p.getProductionDate(), (v1, v2) -> v1));

        Map<String,
            WarehouseInventoryTransferPO> skuStoreMap = productStoreMapper
                .listSkuIdByStoreIds(batchInventoryInfoUpdateDTOS.stream().map(p -> p.getProductStoreId()).distinct()
                    .collect(Collectors.toList()))
                .stream().filter(p -> p != null)
                .collect(Collectors.toMap(p -> p.getProductStoreId(), Function.identity(), (key1, key2) -> key2));

        Integer userId = batchInventoryInfoUpdateDTOS.stream().findFirst().get().getLastUpdateUserId();
        // 获取更新前批次库存信息
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setStoreBatchIds(storeBatchIdS);
        List<BatchInventoryDTO> oldBatchInventoryDTOS =
            batchInventoryProductStoreBatchMapper.listStoreBatchWithPromotion(batchInventoryQueryDTO);

        batchInventoryInfoUpdateDTOS = batchInventoryInfoUpdateDTOS.stream()
            .filter(info -> (info.getProductionDate() != null || ignoreProductionDateNull)
                && !StringUtils.isEmpty(info.getBatchAttributeInfoNo()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(batchInventoryInfoUpdateDTOS)) {
            fixExpireTime(batchInventoryInfoUpdateDTOS);
            batchInventoryProductStoreBatchMapper.updateProductStoreBatchNoAndDate(batchInventoryInfoUpdateDTOS);
        }

        // 生产日期治理任务完成消息
        List<ProductDateTaskMessage> dateTaskMessageList = new ArrayList<>();

        List<ProductInventoryChangeRecordDTO> inventoryChangeRecordDTOS = new ArrayList<>();
        List<ProductStoreBatchChangeRecordPO> batchChangeRecordPOS = new ArrayList<>();
        batchInventoryInfoUpdateDTOS.stream().forEach(update -> {
            String changeRecordId = UUIDUtil.getUUID();
            ProductStoreBatchChangeRecordPO changeRecordPO = new ProductStoreBatchChangeRecordPO();
            changeRecordPO.setChangeCount(BigDecimal.ZERO);
            changeRecordPO.setBatchId(update.getStoreBatchId());
            changeRecordPO.setChangeRecordId(changeRecordId);
            batchChangeRecordPOS.add(changeRecordPO);

            ProductInventoryChangeRecordDTO inventoryChangeRecordDTO = new ProductInventoryChangeRecordDTO();
            inventoryChangeRecordDTO.setId(changeRecordId);
            inventoryChangeRecordDTO.setProductStoreId(update.getProductStoreId());
            inventoryChangeRecordDTO.setCityId(0);
            inventoryChangeRecordDTO.setJiupiEventType(-1);
            inventoryChangeRecordDTO.setCountMaxUnit(BigDecimal.ZERO);
            inventoryChangeRecordDTO.setCountMinUnit(BigDecimal.ZERO);
            inventoryChangeRecordDTO.setTotalCount(BigDecimal.ZERO);
            inventoryChangeRecordDTO.setSourceTotalCount(BigDecimal.ZERO);
            String oldProductionDate = "";
            String taslProductDate = "";
            if (productDateMap != null && !productDateMap.isEmpty()
                && productDateMap.get(update.getStoreBatchId()) != null) {
                oldProductionDate = DateFormatUtils.format(productDateMap.get(update.getStoreBatchId()), "yyyy-MM-dd");
                taslProductDate =
                    DateFormatUtils.format(productDateMap.get(update.getStoreBatchId()), "yyyy-MM-dd HH:mm:ss");
            }
            String productionDate = update.getProductionDate() != null
                ? DateFormatUtils.format(update.getProductionDate(), "yyyy-MM-dd") : "";
            inventoryChangeRecordDTO
                .setDescription(oldProductionDate + PRODUCTION_DATE_CHANEG_EVENT_TYPE_NAME + productionDate);
            inventoryChangeRecordDTO.setCreateTime(new Date());
            inventoryChangeRecordDTO.setCreateUser(String.valueOf(update.getLastUpdateUserId()));
            inventoryChangeRecordDTO.setStoreType(InventoryChangeTypes.WAREHOUSE);
            inventoryChangeRecordDTOS.add(inventoryChangeRecordDTO);

            WarehouseInventoryTransferPO inventoryPO = skuStoreMap.get(update.getProductStoreId());
            if (Objects.nonNull(inventoryPO) && !StringUtils.isEmpty(taslProductDate)) {
                ProductDateTaskMessage dateTaskMessage = new ProductDateTaskMessage();
                dateTaskMessage.setWarehouseId(inventoryPO.getWarehouseId());
                dateTaskMessage.setSkuId(inventoryPO.getProductSkuId());
                dateTaskMessage.setProductionDate(taslProductDate);
                dateTaskMessage.setUserId(update.getLastUpdateUserId());
                dateTaskMessage.setStoreBatchId(update.getStoreBatchId());
                dateTaskMessageList.add(dateTaskMessage);
            }
        });

        // 新增仓库库存变更记录
        if (CollectionUtils.isNotEmpty(inventoryChangeRecordDTOS)) {
            iProductInventoryRecordManagerService.saveProductStoreChangeRecord(inventoryChangeRecordDTOS);
        }
        // 保存批次库存变更信息
        if (CollectionUtils.isNotEmpty(batchChangeRecordPOS)) {
            productStoreBatchChangeRecordBL.addStoreBatchChangeRecordS(batchChangeRecordPOS);
        }

        // // 检查并批量转移批次库存
        // promotionStoreBatchBL.transferStoreBatch(oldBatchInventoryDTOS, userId);
        // 删除促销批次
        deletePromotionBatch(oldBatchInventoryDTOS);

        // 发送同步生产日期消息
        productionDateChangeEventBL.sendProductionDateChangeEvent(batchInventoryInfoUpdateDTOS);
        // 发送生产日期治理任务完成消息
        productionDateTaskEventBL.notifyProductionDateTaskCompleteBatch(dateTaskMessageList);
        return batchInventoryInfoUpdateDTOS;
    }

    public void deletePromotionBatch(List<BatchInventoryDTO> batchInventoryDTOS) {
        if (CollectionUtils.isEmpty(batchInventoryDTOS)) {
            return;
        }

        List<String> batchAttributeInfoNos = batchInventoryDTOS.stream()
            .filter(p -> org.springframework.util.StringUtils.hasText(p.getBatchAttributeInfoNo()))
            .map(p -> p.getBatchAttributeInfoNo()).distinct().collect(Collectors.toList());
        promotionStoreBatchBL.deletePromotionBatches(batchAttributeInfoNos);

    }

    public boolean isDefectiveGoodsLocation(ProductInventoryChangeRecordPO changeRecordPO) {
        List<Long> locations = new ArrayList<>();
        if (changeRecordPO.getLocationId() != null) {
            locations.add(changeRecordPO.getLocationId());
        }
        if (CollectionUtils.isNotEmpty(changeRecordPO.getLocationIds())) {
            locations.addAll(changeRecordPO.getLocationIds());
        }
        // 是否有残次品位
        boolean hasDefectiveGoodsLocation = false;
        if (CollectionUtils.isNotEmpty(locations)) {
            // 判断货位类型是否为：残次品位或者残次品区
            List<LoactionDTO> locationInfoList = iLocationService.findLocationByIds(locations);
            if (CollectionUtils.isNotEmpty(locationInfoList)) {
                hasDefectiveGoodsLocation = locationInfoList.stream()
                    .anyMatch(e -> e != null && (LocationEnum.残次品位.getType()
                        .equals((e.getSubcategory() == null ? null : e.getSubcategory().intValue()))
                        || LocationAreaEnum.残次品区.getType()
                            .equals((e.getSubcategory() == null ? null : e.getSubcategory().intValue()))));
            }
        }
        return hasDefectiveGoodsLocation;
    }

    /**
     * 指定货位入库
     *
     * @param productInventoryChangeDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void processLocationInventory(ProductInventoryChangeDTO productInventoryChangeDTO) {
        LOGGER.info("指定货位入库操作参数:{}", JSON.toJSONString(productInventoryChangeDTO));
        if (productInventoryChangeDTO == null) {
            LOGGER.error("指定货位入库失败，参数为空");
            return;
        }
        ProductInventoryChangeRecordPO productInventoryChangeRecordPO = new ProductInventoryChangeRecordPO();
        BeanUtils.copyProperties(productInventoryChangeDTO, productInventoryChangeRecordPO);

        boolean isOpenLocationStock =
            warehouseConfigService.isOpenLocationStock(productInventoryChangeRecordPO.getWarehouseId());
        if (!isOpenLocationStock) {
            // 是否有残次品位
            boolean hasDefectiveGoodsLocation = isDefectiveGoodsLocation(productInventoryChangeRecordPO);
            if (!hasDefectiveGoodsLocation) {
                // 2.5没开启货位扣的，有推荐货位信息传过来，需要处理掉
                // 非处理品限定其货位类型：不能是【LocationEnum：残次品位】
                productInventoryChangeRecordPO.setLocationIds(null);
                productInventoryChangeRecordPO.setLocationId(null);
                productInventoryChangeRecordPO.setLocationName(null);
            }
        }
        // 新增库存变更记录
        ProductInventoryChangeRecordDTO productInventoryChangeRecord =
            createProductInventoryChangeRecordDTO(productInventoryChangeRecordPO);

        if (Objects.nonNull(productInventoryChangeDTO.getProcessType())) {
            processLocationInventoryBL
                .processLocationInventory(new ProcessLocationInventoryBO(productInventoryChangeRecordPO,
                    productInventoryChangeDTO.getLocationType(), productInventoryChangeDTO.getExSubcategory(),
                    productInventoryChangeDTO.getNeedRandom(), productInventoryChangeDTO.getProcessType()));
        } else {
            // 这个接口慢慢废弃，走重构接口
            productStoreBatchBL.processLocationInventory(productInventoryChangeRecordPO,
                productInventoryChangeDTO.getLocationType(), productInventoryChangeDTO.getExSubcategory(),
                productInventoryChangeDTO.getNeedRandom(), null);
        }

        iProductInventoryRecordManagerService.saveProductStoreChangeRecord(Arrays.asList(productInventoryChangeRecord));
    }

    private ProductInventoryChangeRecordDTO
        createProductInventoryChangeRecordDTO(ProductInventoryChangeRecordPO productInventoryChangeRecordPO) {
        ProductInventoryChangeRecordDTO productInventoryChangeRecordDTO = new ProductInventoryChangeRecordDTO();
        BeanUtils.copyProperties(productInventoryChangeRecordPO, productInventoryChangeRecordDTO);
        productInventoryChangeRecordDTO.setId(UUIDUtil.getUUID());

        // 避免在仓库库存变更记录中被查出
        productInventoryChangeRecordDTO.setProductStoreId("");

        // 回写仓库库存变更记录
        productInventoryChangeRecordPO.setId(productInventoryChangeRecordDTO.getId());
        productInventoryChangeRecordDTO.setCountMaxUnit(BigDecimal.ZERO);
        productInventoryChangeRecordDTO.setCountMinUnit(BigDecimal.ZERO);
        productInventoryChangeRecordDTO.setTotalCount(BigDecimal.ZERO);
        productInventoryChangeRecordDTO.setSourceTotalCount(BigDecimal.ZERO);
        productInventoryChangeRecordDTO.setStoreType(InventoryChangeTypes.WAREHOUSE);
        return productInventoryChangeRecordDTO;
    }

    /**
     * 未开启货位库存变更产品批次属性
     */
    @Transactional(rollbackFor = Exception.class)
    @DistributeLock(conditions = "#noteNo", sleepMills = 3000, key = "processBatchInventoryByStoreCheck",
        lockType = DistributeLock.LockType.WAITLOCK, expireMills = 30000)
    public void nonOpenStockChangeBatchInventoryByStoreCheck(StoreCheckUpdateBatchInventoryDTO changeDTO) {
        LOGGER.info("盘点变更批次库存信息参数：{}", JSON.toJSONString(changeDTO));
        AssertUtils.notNull(changeDTO, "批次属性变更参数不能为空");
        AssertUtils.notNull(changeDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notNull(changeDTO.getOrgId(), "城市ID不能为空");
        AssertUtils.notEmpty(changeDTO.getChangeItemList(), "变更详情不能为空");
        List<StoreCheckUpdateBatchInventoryItemDTO> changeItemList = changeDTO.getChangeItemList();
        boolean hasEmptySkuId = changeItemList.stream().anyMatch(it -> it.getProductSkuId() == null);
        AssertUtils.isTrue(!hasEmptySkuId, "变更详情存在产品SKU信息为空产品");
        Integer warehouseId = changeDTO.getWarehouseId();
        Integer orgId = changeDTO.getOrgId();
        List<StoreCheckUpdateBatchInventoryItemDTO> processChangeItems = changeItemList.stream().filter(
            it -> it != null && it.getUnitTotalCount() != null && it.getUnitTotalCount().compareTo(BigDecimal.ZERO) > 0)
            .collect(Collectors.toList());

        // 排除已经处理过的产品(同一个盘点单，同一个产品，可能生成多个盘盈盘亏单单)
        List<Long> lstProcessedSku = new ArrayList<>();
        List<Long> lstProcessSku = processChangeItems.stream()
            .map(StoreCheckUpdateBatchInventoryItemDTO::getProductSkuId).distinct().collect(Collectors.toList());
        lstProcessSku.forEach(skuId -> {
            String strKey = String.format("%s_%s", changeDTO.getNoteNo(), skuId);
            String strProcessed = redisUtil.getHash(RedisKeyConstant.PRODUCT_STORE_CHECK, strKey);
            if (!StringUtils.isEmpty(strProcessed)) {
                lstProcessedSku.add(skuId);
            } else {
                redisUtil.setHash(RedisKeyConstant.PRODUCT_STORE_CHECK, strKey, changeDTO.getNoteNo(), 7,
                    TimeUnit.DAYS);
            }
        });
        processChangeItems.removeIf(p -> lstProcessedSku.contains(p.getProductSkuId()));
        if (CollectionUtils.isEmpty(processChangeItems)) {
            LOGGER.info("仓库[{}]根据实盘小单位数量过滤后没有需要变更批次产品！", warehouseId);
            return;
        }
        Map<Long, List<StoreCheckUpdateBatchInventoryItemDTO>> changeItemGroup = processChangeItems.stream()
            .collect(Collectors.groupingBy(StoreCheckUpdateBatchInventoryItemDTO::getProductSkuId));
        // sku 信息
        List<Long> skuIdList = Lists.newArrayList(changeItemGroup.keySet());
        // 查找产品批次库存记录
        BatchInventoryQueryDTO queryDTO = new BatchInventoryQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setProductSkuIdList(skuIdList);
        List<BatchInventoryDTO> batchInventoryDTOS = batchInventoryQueryBL.findBatchStoreBySkuId(queryDTO);
        if (CollectionUtils.isEmpty(batchInventoryDTOS)) {
            LOGGER.info("仓库[{}]没有找到产品[{}]库存记录，不变更批次信息！", warehouseId, JSON.toJSONString(skuIdList));
            return;
        }
        Map<Long, List<BatchInventoryDTO>> batchInventoryGroup = batchInventoryDTOS.stream().filter(Objects::nonNull)
            .collect(Collectors.groupingBy(BatchInventoryDTO::getProductSkuId));
        LOGGER.info("仓库[{}]产品原始批次库存记录: {}", warehouseId, JSON.toJSONString(batchInventoryGroup));
        // 需要修改的批次信息
        List<ProductStoreBatchPO> updateProductStoreBatchList = new ArrayList<>();
        // 需要新增的批次信息
        List<ProductStoreBatchPO> insertProductStoreBatchList = new ArrayList<>();
        // 处理匹配上的批次信息
        createNeedUpdateStoreBatch(changeItemGroup, batchInventoryGroup, updateProductStoreBatchList,
            insertProductStoreBatchList);
        LOGGER.info("仓库[{}]产品需要更新记录: {}", warehouseId, JSON.toJSONString(updateProductStoreBatchList));
        LOGGER.info("仓库[{}]产品新增批次记录: {}", warehouseId, JSON.toJSONString(insertProductStoreBatchList));
        // 创建总库存变更记录
        List<ProductInventoryChangeRecordDTO> inventoryChangeRecordList = createInventoryChangeRecord(changeDTO,
            updateProductStoreBatchList, insertProductStoreBatchList, batchInventoryDTOS);
        // 创建批次库存变更记录
        List<ProductStoreBatchChangeRecordPO> batchChangeRecord = createStoreBatchChangeRecord(
            inventoryChangeRecordList, updateProductStoreBatchList, insertProductStoreBatchList);
        // 保存总库存变更信息
        if (CollectionUtils.isNotEmpty(inventoryChangeRecordList)) {
            // 盘点生产日期改动只是批次库存变动,总库存不变,总库存变更记录只是为了给批次库存变更记录展示用
            inventoryChangeRecordList.stream().filter(Objects::nonNull).forEach(inv -> inv.setProductStoreId(""));
            iProductInventoryRecordManagerService.saveProductStoreChangeRecord(inventoryChangeRecordList);
        }
        // 更新批次库存信息：只修改数量
        if (CollectionUtils.isNotEmpty(updateProductStoreBatchList)) {
            List<ProductStoreBatchPO> updatePOS =
                updateProductStoreBatchList.stream().filter(Objects::nonNull).map(up -> {
                    ProductStoreBatchPO updateBatch = new ProductStoreBatchPO();
                    updateBatch.setId(up.getId());
                    updateBatch.setTotalCount(up.getChangeCount());
                    return updateBatch;
                }).collect(Collectors.toList());
            batchInventoryProductStoreBatchMapper.updateBatchInventoryAdd(updatePOS);
        }
        // 新增批次库存信息
        if (CollectionUtils.isNotEmpty(insertProductStoreBatchList)) {
            batchInventoryProductStoreBatchMapper.insertBatchInventoryPOList(insertProductStoreBatchList);
        }
        // 保存批次库存变更信息
        if (CollectionUtils.isNotEmpty(inventoryChangeRecordList)) {
            productStoreBatchChangeRecordBL.addStoreBatchChangeRecordS(batchChangeRecord);
        }
    }

    /**
     * 根据盘点单生产日期及数量创建需要处理的批次信息
     */
    private void createNeedUpdateStoreBatch(Map<Long, List<StoreCheckUpdateBatchInventoryItemDTO>> changeItemGroup,
        Map<Long, List<BatchInventoryDTO>> batchInventoryGroup, List<ProductStoreBatchPO> updateProductStoreBatchList,
        List<ProductStoreBatchPO> insertProductStoreBatchList) {
        List<ProductStoreBatchPO> updateList = new ArrayList<>();
        List<ProductStoreBatchPO> insertList = new ArrayList<>();
        // 组装变更信息
        for (Map.Entry<Long, List<BatchInventoryDTO>> entry : batchInventoryGroup.entrySet()) {
            Long skuId = entry.getKey();
            List<BatchInventoryDTO> batchInventoryList = entry.getValue();
            // 按货区或货位类型排序后更新生产日期
            Collections.sort(batchInventoryList, Comparator.comparing(BatchInventoryDTO::getLocationSubcategory,
                Comparator.nullsLast(Integer::compareTo)));
            List<StoreCheckUpdateBatchInventoryItemDTO> changeItems = changeItemGroup.get(skuId);
            // 统计同一个生产日期内有多少需要分分摊
            Map<String, List<StoreCheckUpdateBatchInventoryItemDTO>> productionDateTotalCountMap =
                changeItems.stream().collect(Collectors.groupingBy(
                    c -> c.getProductionDate() == null ? "0" : String.valueOf(c.getProductionDate().getTime())));
            for (Map.Entry<String, List<StoreCheckUpdateBatchInventoryItemDTO>> countEntry : productionDateTotalCountMap
                .entrySet()) {
                List<StoreCheckUpdateBatchInventoryItemDTO> productionDateItems = countEntry.getValue();
                if (CollectionUtils.isEmpty(productionDateItems)) {
                    continue;
                }
                BigDecimal productionDateCount =
                    productionDateItems.stream().filter(it -> it != null && it.getUnitTotalCount() != null)
                        .map(StoreCheckUpdateBatchInventoryItemDTO::getUnitTotalCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                if (productionDateCount.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                // 盘点明细中生产日期
                StoreCheckUpdateBatchInventoryItemDTO storeCheckUpdateItemDTO = productionDateItems.get(0);
                for (BatchInventoryDTO batchInventoryDTO : batchInventoryList) {
                    BigDecimal batchInventoryCount =
                        ObjectUtils.defaultIfNull(batchInventoryDTO.getStoreTotalCount(), BigDecimal.ZERO);
                    if (batchInventoryCount.compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    if (productionDateCount.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    BigDecimal subtract = productionDateCount.subtract(batchInventoryCount);
                    // if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                    // continue;
                    // }
                    if (subtract.compareTo(BigDecimal.ZERO) >= 0) {
                        // old : 由 storeTotalCount -> 0
                        // new : 由 0 -> storeTotalCount
                        // productionDateCount : subtract
                        // 创建更新批次库存实体
                        ProductStoreBatchPO updatePO = createNewProductStoreBatchPO(batchInventoryDTO, BigDecimal.ZERO,
                            batchInventoryCount.negate(), null, null, false);
                        // 创建新增批次库存实体
                        ProductStoreBatchPO insertPO = createNewProductStoreBatchPO(batchInventoryDTO,
                            batchInventoryCount, batchInventoryCount, storeCheckUpdateItemDTO.getProductionDate(),
                            storeCheckUpdateItemDTO.getBatchTime(), true);
                        updateList.add(updatePO);
                        insertList.add(insertPO);
                        // 变更数量
                        batchInventoryDTO.setStoreTotalCount(BigDecimal.ZERO);
                        productionDateCount = subtract;
                    } else {
                        // old : 由 storeTotalCount -> subtract.abs()
                        // new : 由 0 -> productionDateCount
                        // productionDateCount : 0
                        // 创建更新批次库存实体
                        ProductStoreBatchPO updatePO = createNewProductStoreBatchPO(batchInventoryDTO,
                            subtract.negate(), productionDateCount.negate(), null, null, false);
                        // 创建新增批次库存实体
                        ProductStoreBatchPO insertPO = createNewProductStoreBatchPO(batchInventoryDTO,
                            productionDateCount, productionDateCount, storeCheckUpdateItemDTO.getProductionDate(),
                            storeCheckUpdateItemDTO.getBatchTime(), true);
                        updateList.add(updatePO);
                        insertList.add(insertPO);
                        // 变更数量
                        batchInventoryDTO.setStoreTotalCount(subtract.negate());
                        productionDateCount = BigDecimal.ZERO;
                    }
                }
            }
        }
        // 需要更新的批次库存以批次 id 维度合并
        updateList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(ProductStoreBatchPO::getId))
            .forEach((batchId, batchPOList) -> {
                if (CollectionUtils.isNotEmpty(batchPOList)) {
                    ProductStoreBatchPO batchPO = batchPOList.get(0);
                    if (batchPOList.size() == 1) {
                        updateProductStoreBatchList.add(batchPO);
                    } else {
                        BigDecimal changeCount = batchPOList.stream().map(ProductStoreBatchPO::getChangeCount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        ProductStoreBatchPO newBatchPO = new ProductStoreBatchPO();
                        BeanUtils.copyProperties(batchPO, newBatchPO);
                        newBatchPO.setChangeCount(changeCount);
                        updateProductStoreBatchList.add(newBatchPO);
                    }
                }
            });
        // 新增批次库存：总库存ID + 货位ID + productionDate + batchTime 合并
        insertList.stream().filter(Objects::nonNull)
            .collect(Collectors.groupingBy(ProductStoreBatchPO::getCheckAddBatchKey)).forEach((key, batchPOList) -> {
                if (CollectionUtils.isNotEmpty(batchPOList)) {
                    ProductStoreBatchPO batchPO = batchPOList.get(0);
                    if (batchPOList.size() == 1) {
                        insertProductStoreBatchList.add(batchPO);
                    } else {
                        BigDecimal changeCount = batchPOList.stream().map(ProductStoreBatchPO::getChangeCount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal totalCount = batchPOList.stream().map(ProductStoreBatchPO::getTotalCount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        ProductStoreBatchPO newBatchPO = new ProductStoreBatchPO();
                        BeanUtils.copyProperties(batchPO, newBatchPO);
                        newBatchPO.setChangeCount(changeCount);
                        newBatchPO.setTotalCount(totalCount);
                        insertProductStoreBatchList.add(newBatchPO);
                    }
                }
            });
    }

    private ProductStoreBatchPO createNewProductStoreBatchPO(BatchInventoryDTO batchInventoryDTO, BigDecimal totalCount,
        BigDecimal changeCount, Date productionDate, Date batchTime, boolean isAdd) {
        ProductStoreBatchPO batchPO = new ProductStoreBatchPO();
        batchPO.setId(isAdd ? UUIDUtil.getUUID() : batchInventoryDTO.getStoreBatchId());
        batchPO.setProductStoreId(batchInventoryDTO.getProductStoreId());
        batchPO.setChangeCount(changeCount);
        if (isAdd) {
            batchPO.setTotalCount(totalCount);
            batchPO.setProductionDate(productionDate);
            batchPO.setBatchTime(batchTime == null ? batchInventoryDTO.getBatchTime() : batchTime);
            batchPO.setLocationId(batchInventoryDTO.getLocationId());
            batchPO.setLocationName(batchInventoryDTO.getLocationName());
            batchPO.setLocationCategory(batchInventoryDTO.getLocationCategory());
            batchPO.setSubcategory(batchInventoryDTO.getLocationSubcategory());
            // 计算新的过期时间
            Date expireTime = BatchInventoryConvert.countExpireTime(batchPO.getProductionDate(),
                batchInventoryDTO.getMonthOfShelfLife(), batchInventoryDTO.getShelfLifeUnit());
            batchPO.setExpireTime(expireTime);
            String batchAttributeInfoNo = batchAttributeInfoBL.getProductBatchAttributeInfoNo(
                batchInventoryDTO.getProductStoreId(), batchInventoryDTO.getOwnerType(), batchPO.getProductionDate(),
                batchPO.getBatchTime(), new ArrayList<>(), true);
            batchPO.setBatchAttributeInfoNo(batchAttributeInfoNo);
        }
        return batchPO;
    }

    /**
     * 创建批次库存变更记录
     */
    private List<ProductStoreBatchChangeRecordPO> createStoreBatchChangeRecord(
        List<ProductInventoryChangeRecordDTO> inventoryChangeRecordList,
        List<ProductStoreBatchPO> updateProductStoreBatchList, List<ProductStoreBatchPO> insertProductStoreBatchList) {
        List<ProductStoreBatchChangeRecordPO> productStoreBatchChangeRecordPOS = new ArrayList<>();
        Map<String, String> inventoryRecordIdMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(inventoryChangeRecordList)) {
            inventoryRecordIdMap = inventoryChangeRecordList.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(ProductInventoryChangeRecordDTO::getProductStoreId,
                    ProductInventoryChangeRecordDTO::getId, (v1, v2) -> v1 != null ? v1 : v2));
        }
        productStoreBatchChangeRecordPOS
            .addAll(createProductStoreBatchChangeRecordPOS(inventoryRecordIdMap, updateProductStoreBatchList));
        productStoreBatchChangeRecordPOS
            .addAll(createProductStoreBatchChangeRecordPOS(inventoryRecordIdMap, insertProductStoreBatchList));
        return productStoreBatchChangeRecordPOS;
    }

    private List<ProductStoreBatchChangeRecordPO> createProductStoreBatchChangeRecordPOS(
        Map<String, String> inventoryRecordIdMap, List<ProductStoreBatchPO> productStoreBatchPOList) {
        List<ProductStoreBatchChangeRecordPO> recordPOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(productStoreBatchPOList)) {
            return recordPOS;
        }
        for (ProductStoreBatchPO productStoreBatchPO : productStoreBatchPOList) {
            String inventoryChangeRecordId = inventoryRecordIdMap.get(productStoreBatchPO.getProductStoreId());
            ProductStoreBatchChangeRecordPO changeRecordPO = new ProductStoreBatchChangeRecordPO();
            changeRecordPO.setChangeCount(productStoreBatchPO.getChangeCount());
            changeRecordPO.setBatchId(productStoreBatchPO.getId());
            changeRecordPO
                .setChangeRecordId(StringUtils.isNotEmpty(inventoryChangeRecordId) ? inventoryChangeRecordId : "");
            recordPOS.add(changeRecordPO);
        }
        return recordPOS;
    }

    /**
     * 创建总库存变更记录
     */
    private List<ProductInventoryChangeRecordDTO> createInventoryChangeRecord(
        StoreCheckUpdateBatchInventoryDTO changeDTO, List<ProductStoreBatchPO> updateProductStoreBatchList,
        List<ProductStoreBatchPO> insertProductStoreBatchList, List<BatchInventoryDTO> batchInventoryDTOList) {
        List<String> storeIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(updateProductStoreBatchList)) {
            storeIdList.addAll(updateProductStoreBatchList.stream().filter(Objects::nonNull)
                .map(ProductStoreBatchPO::getProductStoreId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(insertProductStoreBatchList)) {
            storeIdList.addAll(insertProductStoreBatchList.stream().filter(Objects::nonNull)
                .map(ProductStoreBatchPO::getProductStoreId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(storeIdList)) {
            return Collections.emptyList();
        }
        Map<String, BatchInventoryDTO> inventoryDTOMap = batchInventoryDTOList.stream()
            .filter(bi -> bi != null && storeIdList.contains(bi.getProductStoreId())).collect(
                Collectors.toMap(bi -> bi.getProductStoreId(), Function.identity(), (v1, v2) -> v1 != null ? v1 : v2));
        List<ProductInventoryChangeRecordDTO> inventoryChangeRecordList = new ArrayList<>(inventoryDTOMap.size());
        inventoryDTOMap.forEach((storeId, inventory) -> {
            ProductInventoryChangeRecordDTO productInventoryChangeRecordDTO = new ProductInventoryChangeRecordDTO();
            productInventoryChangeRecordDTO.setId(UUIDUtil.getUUID());
            productInventoryChangeRecordDTO.setProductStoreId(storeId);
            productInventoryChangeRecordDTO.setCityId(changeDTO.getOrgId());
            productInventoryChangeRecordDTO.setOrderType(StoreOrderTypeEnum.库存盘点单.getType());
            productInventoryChangeRecordDTO.setOrderId(changeDTO.getNoteId());
            productInventoryChangeRecordDTO.setOrderNo(changeDTO.getNoteNo());
            productInventoryChangeRecordDTO.setJiupiEventType(JiupiEventType.erp库存同步.getType());
            productInventoryChangeRecordDTO.setCountMaxUnit(BigDecimal.ZERO);
            productInventoryChangeRecordDTO.setCountMinUnit(BigDecimal.ZERO);
            productInventoryChangeRecordDTO.setTotalCount(BigDecimal.ZERO);
            productInventoryChangeRecordDTO.setSourceTotalCount(inventory.getUnitTotolCount());
            productInventoryChangeRecordDTO.setDescription("库存盘点单");
            productInventoryChangeRecordDTO.setCreateTime(new Date());
            productInventoryChangeRecordDTO.setStoreType(InventoryChangeTypes.WAREHOUSE);
            inventoryChangeRecordList.add(productInventoryChangeRecordDTO);
        });
        return inventoryChangeRecordList;
    }

    /**
     * 转换batchInventoryTransferDTO的location货位为货位组
     */
    public void transformBatchInventoryTransferToLocGroup(List<BatchInventoryTransferDTO> batchInventoryTransferDTOList,
        Integer warehouseId) {
        boolean isOpenLocationGroup = false;
        boolean isOpenStock = false;
        if (warehouseId != null) {
            isOpenLocationGroup = warehouseConfigService.isOpenLocationGroup(warehouseId);;
            isOpenStock = warehouseConfigService.isOpenLocationStock(warehouseId);
        }

        if (isOpenLocationGroup && isOpenStock) {
            List<String> locIdList = batchInventoryTransferDTOList.stream().filter(e -> e.getLocationId() != null)
                .map(e -> e.getLocationId().toString()).distinct().collect(Collectors.toList());
            List<LocationReturnDTO> locGroupIdList =
                locationAreaService.findLocationAreaListExcludeDefective(locIdList);
            if (CollectionUtils.isEmpty(locGroupIdList)) {
                return;
            }

            batchInventoryTransferDTOList.forEach(elem -> {
                LocationReturnDTO locationReturnDTO =
                    locGroupIdList.stream().filter(p -> p.getId().equals(elem.getLocationId())).findAny().orElse(null);
                if (locationReturnDTO == null) {
                    return;
                }

                elem.setLocationId(locationReturnDTO.getArea_Id());
                elem.setLocationName(locationReturnDTO.getArea());
            });
        }
    }

    /**
     * 转换PickUpDTO的fromLocation货位为货位组
     */
    public List<PickUpDTO> transformPickUpToLocGroup(List<PickUpDTO> pickUpDTOList) {
        // 查询货位的货位组
        List<String> fromLocIdList = pickUpDTOList.stream().filter(e -> e.getFromLocationId() != null)
            .map(e -> e.getFromLocationId().toString()).distinct().collect(Collectors.toList());
        List<LocationReturnDTO> fromLocGroupIdList =
            locationAreaService.findLocationAreaListExcludeDefective(fromLocIdList);

        // 对原pickUpDTO进行转化
        List<PickUpDTO> pickUpDTOTransformList = pickUpDTOList.stream().map(elem -> {

            // 用于关联转换前后的PickUpDTO
            String tempId = UUIDUtil.getUUID();
            elem.setTempId(tempId);

            PickUpDTO newPickUp = new PickUpDTO();
            BeanUtils.copyProperties(elem, newPickUp);

            if (CollectionUtils.isNotEmpty(fromLocGroupIdList)) {
                LocationReturnDTO locationReturnDTO = fromLocGroupIdList.stream()
                    .filter(p -> p.getId().equals(elem.getFromLocationId())).findAny().orElse(null);
                if (locationReturnDTO != null) {
                    newPickUp.setFromLocationId(locationReturnDTO.getArea_Id());
                    newPickUp.setFromLocationName(locationReturnDTO.getArea());
                }
            }

            return newPickUp;
        }).collect(Collectors.toList());

        return pickUpDTOTransformList;
    }

    /**
     * 货位库存，置空小于2013年的生产日期，更新货位库存
     *
     * @param warehouseIdList 仓库ID列表
     * @param endProductionDate 截止生产日期（yyyy-MM-dd HH:mm:ss）
     */
    public void clearBeforeProductionDate(List<Integer> warehouseIdList, Date endProductionDate) {
        String endProductionDateStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endProductionDate);

        BatchInventoryQueryDTO storeBatchQuery = new BatchInventoryQueryDTO();
        storeBatchQuery.setEndProductionDate(endProductionDate);
        storeBatchQuery.setPageSize(2000);

        LocalDateTime startTime = LocalDateTime.now();
        long totalRowCount = 0;
        long totalPageCount = 0;

        LOGGER.info("[货位库存生产时间置空]开始，生产日期小于[{}]的记录，开始时间：{}，仓库数量：{}", endProductionDateStr, startTime,
            warehouseIdList.size());

        // 1、遍历所有仓库
        for (int i = 0; i < warehouseIdList.size(); i++) {
            Integer warehouseId = warehouseIdList.get(i);
            storeBatchQuery.setWarehouseId(warehouseId);
            int pageCount = 1;
            int rowCount = 0;

            List<BatchInventoryInfoUpdateDTO> batchInventoryInfoUpdateDTOS = new ArrayList<>();
            for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
                storeBatchQuery.setPageNum(pageNum);

                // 2、查询出生产日期小于2013年的货位库存
                PageList<ProductStoreBatchDTO> productStoreBatchDTOS =
                    batchInventoryQueryBL.listProductStoreBatch(storeBatchQuery);

                if (productStoreBatchDTOS == null || CollectionUtils.isEmpty(productStoreBatchDTOS.getDataList())) {
                    continue;
                }

                if (pageNum == 1) {
                    pageCount = productStoreBatchDTOS.getPager().getTotalPage();
                    rowCount = productStoreBatchDTOS.getPager().getRecordCount();
                    totalPageCount += pageCount;
                    totalRowCount += rowCount;
                }
                LOGGER.info("[货位库存生产时间置空]生产日期小于[{}]的记录，仓库ID：{}，仓库记录数：{}，共{}个批次，正在同步第{}个批次，未处理仓库数量：{}，开始时间：{}",
                    endProductionDateStr, warehouseId, rowCount, pageCount, pageNum, warehouseIdList.size() - i - 1,
                    startTime);

                // 3、组装更新信息
                productStoreBatchDTOS.getDataList().forEach(batchInventoryElem -> {
                    BatchInventoryInfoUpdateDTO updateDTO = new BatchInventoryInfoUpdateDTO();
                    updateDTO.setStoreBatchId(batchInventoryElem.getId());
                    updateDTO.setProductStoreId(batchInventoryElem.getProductStoreId());
                    updateDTO.setOwnerType(batchInventoryElem.getOwnerType());
                    updateDTO.setProductionDate(null);
                    updateDTO.setBatchTime(batchInventoryElem.getBatchTime());
                    batchInventoryInfoUpdateDTOS.add(updateDTO);
                });
            }

            // 4、更新批次库存
            Lists.partition(batchInventoryInfoUpdateDTOS, 1000).forEach(updateDTOList -> {
                this.updateBatchInventoryInfo(updateDTOList, true);
            });
        }

        LOGGER.info("[货位库存生产时间置空]全部结束，生产日期小于[{}]的记录，仓库数量：{}，总记录数：{}，总共{}个批次，开始时间：{}", endProductionDateStr,
            warehouseIdList.size(), totalRowCount, totalPageCount, startTime);
    }

    /**
     * 获取sku成本价
     *
     * @param orgId 城市id
     * @param warehouseId 仓库id
     * @param skuCount sku的批次库存信息（skuId -> sku批次库存数量）
     * @return
     */
    public BigDecimal sumProductPriceTotalAmount(Integer orgId, Integer warehouseId, Map<Long, BigDecimal> skuCount) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (skuCount == null || skuCount.size() == 0) {
            return totalAmount;
        }
        ProductCostPriceQueryDTO productCostPriceQueryDTO = new ProductCostPriceQueryDTO();
        productCostPriceQueryDTO.setCityId(orgId);
        productCostPriceQueryDTO.setWarehouseId(warehouseId);
        productCostPriceQueryDTO.setSkuIds(new ArrayList<>(skuCount.keySet()));
        Map<Long, BigDecimal> productCostPriceMap =
            iProductSkuQueryService.getProductCostPriceMap(productCostPriceQueryDTO);

        if (productCostPriceMap == null || productCostPriceMap.size() == 0) {
            return totalAmount;
        }

        for (Map.Entry<Long, BigDecimal> count : skuCount.entrySet()) {
            BigDecimal amount = productCostPriceMap.get(count.getKey());
            if (amount != null) {
                totalAmount = totalAmount.add(amount.multiply(count.getValue()));
            }
        }

        LOGGER.info("[残次品金额]仓库ID：{},sku数量：{},sku金额成本：{},本次总成本：{}", warehouseId, skuCount, productCostPriceMap,
            totalAmount);
        return totalAmount;
    }

    /**
     * 查询破损区额度配置
     */
    private BigDecimal getMaxAmountConfig(Integer warehouseId) {
        VariableValueQueryDTO cfgQueryDTO = new VariableValueQueryDTO();
        cfgQueryDTO.setWarehouseId(warehouseId);
        cfgQueryDTO.setVariableKey("CCP_PRICE_MAX");
        VariableDefAndValueDTO variableDefAndValueDTO = variableValueService.detailVariable(cfgQueryDTO);
        if (variableDefAndValueDTO == null || variableDefAndValueDTO.getVariableData() == null) {
            LOGGER.warn("[残次品金额]未设置破损区金钱额度上限，仓库ID：{}", warehouseId);
            return null;
        }
        LOGGER.info("[残次品金额]仓库ID：{}，残次品金额上限：{}", warehouseId, variableDefAndValueDTO.getVariableData());
        return new BigDecimal(variableDefAndValueDTO.getVariableData());
    }

    /**
     * 根据批次库存，获取残次品的金钱限额（全量）
     *
     * @param orgId
     * @param warehouseId
     */
    private BigDecimal calTotalAmountByInventory(Integer orgId, Integer warehouseId) {
        Map<Long, BigDecimal> skuCount = new HashMap<>(16);

        // 1.查询残次品批次库存
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setWarehouseId(warehouseId);
        List<Integer> subCategoryList = new ArrayList<>();
        subCategoryList.add(LocationAreaEnum.残次品区.getType());
        subCategoryList.add(LocationEnum.残次品位.getType());
        batchInventoryQueryDTO.setSubCategoryList(subCategoryList);
        PageList<BatchInventoryDTO> batchInventoryList =
            batchInventoryQueryBL.findBatchInventoryList(batchInventoryQueryDTO);

        if (CollectionUtils.isNotEmpty(batchInventoryList.getDataList())) {
            // 自动转入类型的，不计算残次品增量额度
            batchInventoryList.getDataList()
                .removeIf(p -> Objects.equals(p.getBatchProperty(), ProductStoreBatchPropertyEnum.自动转入.getType()));
        }

        if (CollectionUtils.isNotEmpty(batchInventoryList.getDataList())) {
            batchInventoryList.getDataList().stream().collect(Collectors.groupingBy(BatchInventoryDTO::getProductSkuId))
                .forEach((skuId, inventoryList) -> {
                    BigDecimal unitTotalCount = inventoryList.stream().map(BatchInventoryDTO::getStoreTotalCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    skuCount.put(skuId, unitTotalCount);
                });
        }
        BigDecimal totalAmount = this.sumProductPriceTotalAmount(orgId, warehouseId, skuCount);
        LOGGER.info("[残次品金额]仓库ID：{},sku数量：{},全量聚合后：{}", warehouseId, skuCount.size(), totalAmount);
        return totalAmount;
    }

    /**
     * 根据skuCount，获取残次品的金钱限额（增量）
     *
     * @param skuCount 需要额外累积计算的数据（skuId -> sku批次库存数量）
     */
    public BigDecimal calTotalAmountBySkuCount(Integer orgId, Integer warehouseId, Map<Long, BigDecimal> skuCount) {
        // 1.历史已用额度
        BigDecimal redisTotalAmount = this.getTotalAmountByRedis(orgId, warehouseId);
        // 2.本地使用额度
        BigDecimal curTotalAmount = this.sumProductPriceTotalAmount(orgId, warehouseId, skuCount);
        // 3.聚合
        BigDecimal newTotalAmount = redisTotalAmount.add(curTotalAmount);

        LOGGER.info("[残次品金额]仓库ID：{},本次sku数量：{},增量聚合后：{}", warehouseId, skuCount, newTotalAmount);
        return newTotalAmount;
    }

    /**
     * 获取破损区的残次品相关额度从redis
     */
    public BigDecimal getTotalAmountByRedis(Integer orgId, Integer warehouseId) {
        String totalMountString =
            redisUtil.getHash(RedisKeyConstant.CCP_PRICE_QUOTA, String.format("%s_%s", orgId, warehouseId));
        if (StringUtils.isEmpty(totalMountString)) {
            // 1.全量聚合
            BigDecimal totalAmount = this.calTotalAmountByInventory(orgId, warehouseId);
            // 2.存入redis
            this.saveTotalAmountToRedis(orgId, warehouseId, totalAmount);
            return totalAmount;
        } else {
            LOGGER.info("[残次品金额]仓库id：{},redis历史金额：{}", warehouseId, totalMountString);
            try {
                return new BigDecimal(totalMountString);
            } catch (Exception ex) {
                LOGGER.info("[残次品金额]仓库id：{},redis历史金额格式错误：{},重新计算", totalMountString, warehouseId);
                BigDecimal totalAmount = this.calTotalAmountByInventory(orgId, warehouseId);
                this.saveTotalAmountToRedis(orgId, warehouseId, totalAmount);
                return totalAmount;
            }
        }
    }

    /**
     * 存储残次品已使用金额到redis
     */
    private void saveTotalAmountToRedis(Integer orgId, Integer warehouseId, BigDecimal totalAmount) {
        redisUtil.setHash(RedisKeyConstant.CCP_PRICE_QUOTA, String.format("%s_%s", orgId, warehouseId),
            String.valueOf(totalAmount), 365, TimeUnit.DAYS);
    }

    /**
     * 保存破损区的残次品相关额度到redis
     */
    public DefectiveInventoryPriceDTO saveCcpPriceByInventory(Integer orgId, Integer warehouseId) {
        AssertUtils.notNull(orgId, "城市ID不能为空！");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空！");

        // 1.查询破损区额度配置
        BigDecimal maxAmount = this.getMaxAmountConfig(warehouseId);
        if (maxAmount == null) {
            return null;
        }

        // 2.已使用残次品金额额度（全量）
        BigDecimal totalAmount = this.calTotalAmountByInventory(orgId, warehouseId);
        // 3.存入redis
        this.saveTotalAmountToRedis(orgId, warehouseId, totalAmount);
        LOGGER.info("[残次品金额]仓库ID：{},全量存redis成功，已用金额：{}", warehouseId, totalAmount);
        return new DefectiveInventoryPriceDTO(maxAmount, totalAmount, maxAmount.subtract(totalAmount));
    }

    /**
     * 保存破损区的残次品相关额度到redis
     */
    public DefectiveInventoryPriceDTO saveCcpPriceBySkuCount(Integer orgId, Integer warehouseId,
        Map<Long, BigDecimal> skuCount) {
        AssertUtils.notNull(orgId, "城市ID不能为空！");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空！");

        // 1.查询破损区额度配置
        BigDecimal maxAmount = this.getMaxAmountConfig(warehouseId);
        if (maxAmount == null) {
            return null;
        }

        // 2.已使用残次品金额额度
        BigDecimal totalAmount = this.calTotalAmountBySkuCount(orgId, warehouseId, skuCount);
        // 3.存入redis
        this.saveTotalAmountToRedis(orgId, warehouseId, totalAmount);
        LOGGER.info("[残次品金额]仓库ID：{},增量存redis成功，已用金额：{}", warehouseId, totalAmount);
        return new DefectiveInventoryPriceDTO(maxAmount, totalAmount, maxAmount.subtract(totalAmount));
    }

    /**
     * 获取破损区的残次品相关额度从redis
     */
    public DefectiveInventoryPriceDTO getCcpPrice(Integer orgId, Integer warehouseId) {
        AssertUtils.notNull(orgId, "城市ID不能为空！");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空！");

        // 1.查询破损区额度配置
        BigDecimal maxAmount = this.getMaxAmountConfig(warehouseId);
        if (maxAmount == null) {
            return null;
        }
        // 2.已使用残次品金额额度
        BigDecimal totalAmount = this.getTotalAmountByRedis(orgId, warehouseId);
        return new DefectiveInventoryPriceDTO(maxAmount, totalAmount, maxAmount.subtract(totalAmount));
    }

    /**
     * 根据skuCount，计算破损区的残次品相关额度（只计算，不会存入redis）
     */
    public DefectiveInventoryPriceDTO calCcpPriceBySkuCount(Integer orgId, Integer warehouseId,
        Map<Long, BigDecimal> skuCount) {
        AssertUtils.notNull(orgId, "城市ID不能为空！");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空！");

        // 残次品额度计算过滤绝对滞销sku
        filterUnsalableSkuId(warehouseId, skuCount);
        if (skuCount.isEmpty()) {
            return null;
        }
        // 1.查询破损区额度配置
        BigDecimal maxAmount = this.getMaxAmountConfig(warehouseId);
        if (maxAmount == null) {
            return null;
        }
        // 2.聚合本次sku数量
        BigDecimal newTotalAmount = this.calTotalAmountBySkuCount(orgId, warehouseId, skuCount);

        return new DefectiveInventoryPriceDTO(maxAmount, newTotalAmount, maxAmount.subtract(newTotalAmount));
    }

    /**
     * 根据产品规格修改批次库存生产日期
     */
    public void updateProductionDateBySpec(BatchInventoryDTO batchInventoryDTO) {
        AssertUtils.notNull(batchInventoryDTO.getWarehouseId(), "仓库ID不能为空！");
        AssertUtils.notNull(batchInventoryDTO.getProductSpecificationId(), "规格ID不能为空！");

        Warehouse warehouse = warehouseQueryService.findWarehouseById(batchInventoryDTO.getWarehouseId());
        if (warehouse == null) {
            throw new BusinessException("仓库不存在，仓库ID：" + batchInventoryDTO.getWarehouseId());
        }
        ProductSkuDTO productSkuDTO = iProductSkuQueryService.getProductSkuBySpecId(warehouse.getCityId(),
            batchInventoryDTO.getProductSpecificationId(), batchInventoryDTO.getOwnerId(), null);
        if (productSkuDTO == null || productSkuDTO.getProductSkuId() == null) {
            throw new BusinessException(String.format("sku不存在，城市id：%s，规格Id：%s，货主Id：%s", warehouse.getCityId(),
                batchInventoryDTO.getProductSpecificationId(), batchInventoryDTO.getOwnerId()));
        }

        BatchInventoryQueryDTO batchQueryDTO = new BatchInventoryQueryDTO();
        batchQueryDTO.setCityId(warehouse.getCityId());
        batchQueryDTO.setWarehouseId(batchInventoryDTO.getWarehouseId());
        batchQueryDTO.setProductSkuId(productSkuDTO.getProductSkuId());
        batchQueryDTO.setPageSize(2000);

        // batchQueryDTO.setLocationName(queryDTO.getLocationName());
        // batchQueryDTO.setBatchAttributeInfoNo(queryDTO.getBatchAttributeInfoNo());
        // showAll为null,查询不等于0的批次库存
        batchQueryDTO.setShowAll(
            batchInventoryDTO.getIsDisplayZero() != null && batchInventoryDTO.getIsDisplayZero() ? (byte)1 : null);

        List<BatchInventoryInfoUpdateDTO> batchInventoryInfoUpdateDTOS = new ArrayList<>();

        int pageCount = 1;
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
            batchQueryDTO.setPageNum(pageNum);

            // 查询批次库存
            PageList<BatchInventoryDTO> batchInventoryDTOS =
                batchInventoryQueryBL.findBatchInventoryList(batchQueryDTO);
            if (batchInventoryDTOS == null || CollectionUtils.isEmpty(batchInventoryDTOS.getDataList())) {
                continue;
            }

            if (pageNum == 1) {
                pageCount = batchInventoryDTOS.getPager().getTotalPage();
            }

            batchInventoryDTOS.getDataList().forEach(batchInventoryElem -> {
                BatchInventoryInfoUpdateDTO updateDTO = new BatchInventoryInfoUpdateDTO();
                updateDTO.setStoreBatchId(batchInventoryElem.getStoreBatchId());
                updateDTO.setProductStoreId(batchInventoryElem.getProductStoreId());
                updateDTO.setOwnerType(batchInventoryElem.getOwnerType());
                updateDTO.setProductionDate(batchInventoryDTO.getProductionDate());
                updateDTO.setBatchTime(batchInventoryElem.getBatchTime());
                batchInventoryInfoUpdateDTOS.add(updateDTO);

            });
        }

        // 更新批次库存
        Lists.partition(batchInventoryInfoUpdateDTOS, 1000).forEach(updateDTOList -> {
            this.updateBatchInventoryInfo(updateDTOList, true);
        });

    }

    /**
     * 累加残次品金额到redis
     */
    @DistributeLock(conditions = "#warehouseId", key = "asyncCcpPriceBySkuCount", sleepMills = 1000, retryTimes = 8,
        lockType = DistributeLock.LockType.WAITLOCK)
    public BigDecimal saveDeltaCcpAmount(Integer orgId, Integer warehouseId, BigDecimal deltaAmount) {
        // 1.历史已用额度
        BigDecimal redisTotalAmount = this.getTotalAmountByRedis(orgId, warehouseId);
        // 2. 合计金额
        BigDecimal totalAmount = redisTotalAmount.add(deltaAmount);
        // 3.存入redis
        this.saveTotalAmountToRedis(orgId, warehouseId, totalAmount);
        return totalAmount;
    }

    public void deletePromotionStoreBatch(List<ProductStoreBatchPO> productStoreBatchPOS) {
        try {
            if (CollectionUtils.isEmpty(productStoreBatchPOS)) {
                return;
            }

            List<String> storeBatchIds =
                productStoreBatchPOS.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(storeBatchIds)) {
                return;
            }

            // 根据主键id查询批次库存信息,过滤数量为0的批次库存
            List<ProductStoreBatchPO> storeBatchPOS =
                batchInventoryProductStoreBatchMapper.findProductStoreBatchListById(storeBatchIds);
            LOGGER.info("查询待删除促销批次库存结果：" + JSON.toJSONString(storeBatchPOS));
            if (CollectionUtils.isEmpty(storeBatchPOS)) {
                return;
            }

            List<String> batchAttributeInfoNos = storeBatchPOS.stream()
                .filter(p -> p != null && org.springframework.util.StringUtils.hasText(p.getBatchAttributeInfoNo())
                    && p.getTotalCount().compareTo(BigDecimal.ZERO) <= 0)
                .map(p -> p.getBatchAttributeInfoNo()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(batchAttributeInfoNos)) {
                return;
            }

            promotionStoreBatchBL.batchDeleteWithCheck(batchAttributeInfoNos);
        } catch (Exception e) {
            LOGGER.error("根据批次编号直接删除促销批次库存异常：" + JSON.toJSONString(productStoreBatchPOS), e);
        }
    }

    public void filterUnsalableSkuId(Integer warehouseId, Map<Long, BigDecimal> skuCount) {
        if (warehouseId == null || skuCount == null) {
            return;
        }

        List<Long> skuIds = new ArrayList<>(skuCount.keySet());
        // 绝对滞销skuid不参与残次品额度处理
        List<Long> unsalableSkuIds = iProductSkuConfigService.listUnsalableSkuIds(warehouseId, skuIds);
        if (CollectionUtils.isEmpty(unsalableSkuIds)) {
            return;
        }
        LOGGER.info("残次品额度过滤绝对滞销sku：{}", JSON.toJSONString(unsalableSkuIds));
        skuCount.keySet().removeAll(unsalableSkuIds);
    }

}
