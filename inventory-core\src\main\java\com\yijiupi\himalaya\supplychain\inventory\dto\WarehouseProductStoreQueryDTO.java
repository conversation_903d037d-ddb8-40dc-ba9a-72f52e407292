package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.util.List;

import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/25
 */
public class WarehouseProductStoreQueryDTO extends PageCondition {

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 产品skuId集合
     */
    private List<Long> productSkuIds;

    /**
     * 规格id和ownerId集合
     */
    private List<ProductSpecAndOwnerIdDTO> specAndOwnerIds;

    /**
     * ownerId
     */
    private Long ownerId;

    /**
     * 产品规格id集合（同规格不同货主的都会被查询出来）
     */
    private List<Long> productSpecIds;

    /**
     * 规格id和ownerId和secOwnerId集合
     */
    private List<ProductSpecAndOwnerIdDTO> specAndOwnerIdAndSecOwnerIds;

    /**
     * 获取 城市id
     *
     * @return cityId 城市id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市id
     *
     * @param cityId 城市id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品skuId集合
     *
     * @return productSkuIds 产品skuId集合
     */
    public List<Long> getProductSkuIds() {
        return this.productSkuIds;
    }

    /**
     * 设置 产品skuId集合
     *
     * @param productSkuIds 产品skuId集合
     */
    public void setProductSkuIds(List<Long> productSkuIds) {
        this.productSkuIds = productSkuIds;
    }

    /**
     * 获取 规格id和ownerId集合
     *
     * @return specAndOwnerIds 规格id和ownerId集合
     */
    public List<ProductSpecAndOwnerIdDTO> getSpecAndOwnerIds() {
        return this.specAndOwnerIds;
    }

    /**
     * 设置 规格id和ownerId集合
     *
     * @param specAndOwnerIds 规格id和ownerId集合
     */
    public void setSpecAndOwnerIds(List<ProductSpecAndOwnerIdDTO> specAndOwnerIds) {
        this.specAndOwnerIds = specAndOwnerIds;
    }

    /**
     * 获取 ownerId
     *
     * @return ownerId ownerId
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置 ownerId
     *
     * @param ownerId ownerId
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 产品规格id集合（同规格不同货主的都会被查询出来）
     *
     * @return productSpecIds 产品规格id集合（同规格不同货主的都会被查询出来）
     */
    public List<Long> getProductSpecIds() {
        return this.productSpecIds;
    }

    /**
     * 设置 产品规格id集合（同规格不同货主的都会被查询出来）
     *
     * @param productSpecIds 产品规格id集合（同规格不同货主的都会被查询出来）
     */
    public void setProductSpecIds(List<Long> productSpecIds) {
        this.productSpecIds = productSpecIds;
    }

    public List<ProductSpecAndOwnerIdDTO> getSpecAndOwnerIdAndSecOwnerIds() {
        return specAndOwnerIdAndSecOwnerIds;
    }

    public void setSpecAndOwnerIdAndSecOwnerIds(List<ProductSpecAndOwnerIdDTO> specAndOwnerIdAndSecOwnerIds) {
        this.specAndOwnerIdAndSecOwnerIds = specAndOwnerIdAndSecOwnerIds;
    }
}
