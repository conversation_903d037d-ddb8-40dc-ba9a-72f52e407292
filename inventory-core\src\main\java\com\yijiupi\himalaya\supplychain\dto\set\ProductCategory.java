package com.yijiupi.himalaya.supplychain.dto.set;

import java.io.Serializable;

/**
 * 
 * @author: tangkun
 * @date: 2017年3月31日 下午3:25:30
 */
public class ProductCategory implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 类目体系
     */
    private Integer categoryClass;
    /**
     * 一级类目
     */
    private Integer productCategoryId;
    /**
     * 类目名称
     */
    private String productCategoryName;

    public Integer getCategoryClass() {
        return categoryClass;
    }

    public void setCategoryClass(Integer categoryClass) {
        this.categoryClass = categoryClass;
    }

    public Integer getProductCategoryId() {
        return productCategoryId;
    }

    public void setProductCategoryId(Integer productCategoryId) {
        this.productCategoryId = productCategoryId;
    }

    public String getProductCategoryName() {
        return productCategoryName;
    }

    public void setProductCategoryName(String productCategoryName) {
        this.productCategoryName = productCategoryName;
    }

}
