
package com.yijiupi.himalaya.supplychain.batchinventory.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.PromotionStoreBatchBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryMoveDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.BaseResult;

/**
 * 批次库存
 *
 * <AUTHOR>
 * @date 2025/5/28
 */
@RestController
public class StoreBatchController {
    private static final Logger LOG = LoggerFactory.getLogger(StoreBatchController.class);

    @Autowired
    private PromotionStoreBatchBL promotionStoreBatchBL;

    /**
     * 批次库存自动转残次品
     */
    @RequestMapping(value = "/storeBatch/transferToDefective", method = RequestMethod.POST)
    public BaseResult storeBatchTransferToDefective(@RequestBody BatchInventoryMoveDTO moveDTO) {
        LOG.info("批次库存自动转残次品入参：{}", JSON.toJSONString(moveDTO));
        promotionStoreBatchBL.storeBatchTransferToDefective(moveDTO);
        return BaseResult.getSuccessResult();
    }
}
