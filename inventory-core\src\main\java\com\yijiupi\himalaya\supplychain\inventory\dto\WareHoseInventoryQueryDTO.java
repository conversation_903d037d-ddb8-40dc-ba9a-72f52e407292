package com.yijiupi.himalaya.supplychain.inventory.dto;

import com.yijiupi.himalaya.base.search.PagerCondition;

import java.util.List;

/**
 * 仓库库存查询dto(入参)
 * 
 * <AUTHOR> 2017/12/12
 */
public class WareHoseInventoryQueryDTO extends PagerCondition {
    /**
     * 产品信息规格id
     */
    private Long productSpecificationId;
    /**
     * ownerType=1 合作商库存,2 入住商,0酒批
     */
    private Integer OwnType;
    /**
     * ownId
     */
    private Long ownId;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;

    /**
     * 产品skuId
     */
    private List<Long> productSkuIds;

    /**
     * 产品规格id和货主id
     */
    private List<ProductSpecAndOwnerIdDTO> specAndOwnerIds;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    /**
     * 是否查询库存时抹去货主 0 或者 null:不抹去货主，其它则抹去货主
     *
     */
    private Integer eraseOwnerId;

    /**
     * 产品规格id集合（同规格不同货主的都会被查询出来）
     */
    private List<Long> productSpecIds;

    /**
     * 一级类目id
     */
    private List<Long> statisticsClassList;

    /**
     * 规格id和ownerId和secOwnerId集合
     */
    private List<ProductSpecAndOwnerIdDTO> specAndOwnerIdAndSecOwnerIds;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;

    public List<Long> getStatisticsClassList() {
        return statisticsClassList;
    }

    public void setStatisticsClassList(List<Long> statisticsClassList) {
        this.statisticsClassList = statisticsClassList;
    }

    public List<Long> getProductSpecIds() {
        return productSpecIds;
    }

    public void setProductSpecIds(List<Long> productSpecIds) {
        this.productSpecIds = productSpecIds;
    }

    public List<ProductSpecAndOwnerIdDTO> getSpecAndOwnerIds() {
        return specAndOwnerIds;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public void setSpecAndOwnerIds(List<ProductSpecAndOwnerIdDTO> specAndOwnerIds) {
        this.specAndOwnerIds = specAndOwnerIds;
    }

    public List<Long> getProductSkuIds() {
        return productSkuIds;
    }

    public void setProductSkuIds(List<Long> productSkuIds) {
        this.productSkuIds = productSkuIds;
    }

    /**
     * 获取 产品信息规格id
     *
     * @return productSpecificationId 产品信息规格id
     */
    public Long getProductSpecificationId() {
        return this.productSpecificationId;
    }

    /**
     * 设置 产品信息规格id
     *
     * @param productSpecificationId 产品信息规格id
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 ownerType=1 合作商库存2 入住商0酒批
     *
     * @return OwnType ownerType=1 合作商库存2 入住商0酒批
     */
    public Integer getOwnType() {
        return this.OwnType;
    }

    /**
     * 设置 ownerType=1 合作商库存2 入住商0酒批
     *
     * @param OwnType ownerType=1 合作商库存2 入住商0酒批
     */
    public void setOwnType(Integer OwnType) {
        this.OwnType = OwnType;
    }

    /**
     * 获取 ownId
     *
     * @return ownId ownId
     */
    public Long getOwnId() {
        return this.ownId;
    }

    /**
     * 设置 ownId
     *
     * @param ownId ownId
     */
    public void setOwnId(Long ownId) {
        this.ownId = ownId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 城市id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 库存渠道,0:酒批，1:大宗产品
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 库存渠道,0:酒批，1:大宗产品
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getEraseOwnerId() {
        return eraseOwnerId;
    }

    public void setEraseOwnerId(Integer eraseOwnerId) {
        this.eraseOwnerId = eraseOwnerId;
    }

    public List<ProductSpecAndOwnerIdDTO> getSpecAndOwnerIdAndSecOwnerIds() {
        return specAndOwnerIdAndSecOwnerIds;
    }

    public void setSpecAndOwnerIdAndSecOwnerIds(List<ProductSpecAndOwnerIdDTO> specAndOwnerIdAndSecOwnerIds) {
        this.specAndOwnerIdAndSecOwnerIds = specAndOwnerIdAndSecOwnerIds;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }
}
