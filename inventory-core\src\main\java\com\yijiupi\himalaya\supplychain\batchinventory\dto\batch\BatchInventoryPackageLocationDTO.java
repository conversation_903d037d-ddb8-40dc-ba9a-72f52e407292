package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.order.OrderItemDTO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: BatchInventoryPackageLocationDTO
 * @description:
 * @date 2023-03-02 09:57
 */
public class BatchInventoryPackageLocationDTO implements Serializable {
    /**
     *
     */
    private List<OrderItemDTO> orderItemDTOList;


    /**
     * 获取
     *
     * @return orderItemDTOList
     */
    public List<OrderItemDTO> getOrderItemDTOList() {
        return this.orderItemDTOList;
    }

    /**
     * 设置
     *
     * @param orderItemDTOList
     */
    public void setOrderItemDTOList(List<OrderItemDTO> orderItemDTOList) {
        this.orderItemDTOList = orderItemDTOList;
    }
}
