package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.util.Date;

public class BatchNOQueryResultDTO implements Serializable {

    /**
     * 产品skuId
     */
    private Long skuId;

    /**
     * 产品批次编码
     */
    private String batchInfoNO;

    /**
     * 批次时间【入库时间】
     */
    private Date batchTime;
    /**
     * 生产日期
     */
    private Date productionDate;

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getBatchInfoNO() {
        return batchInfoNO;
    }

    public void setBatchInfoNO(String batchInfoNO) {
        this.batchInfoNO = batchInfoNO;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }
}
