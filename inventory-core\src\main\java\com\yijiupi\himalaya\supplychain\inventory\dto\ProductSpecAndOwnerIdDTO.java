package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * 产品规格id和货主id
 *
 * <AUTHOR>
 * @date 2019/4/1 14:16
 */
public class ProductSpecAndOwnerIdDTO implements Serializable {

    /**
     * 产品规格id
     */
    private Long productSpecId;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }
}
