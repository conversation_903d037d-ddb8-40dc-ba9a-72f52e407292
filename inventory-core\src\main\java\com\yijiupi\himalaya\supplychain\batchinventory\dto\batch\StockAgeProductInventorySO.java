package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class StockAgeProductInventorySO implements Serializable {

    /**
     * 库龄下限
     */
    private Long minStockAge;

    /**
     * 库龄上限
     */
    private Long maxStockAge;

    /**
     * skuId
     */
    private List<Long> skuIds;

    /**
     * 库龄范围
     */
    private Long stockAgeRange;

    /**
     * 超期范围
     */
    private Long overdueRange;

    public Long getMinStockAge() {
        return minStockAge;
    }

    public void setMinStockAge(Long minStockAge) {
        this.minStockAge = minStockAge;
    }

    public Long getMaxStockAge() {
        return maxStockAge;
    }

    public void setMaxStockAge(Long maxStockAge) {
        this.maxStockAge = maxStockAge;
    }

    public List<Long> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    public Long getStockAgeRange() {
        return stockAgeRange;
    }

    public void setStockAgeRange(Long stockAgeRange) {
        this.stockAgeRange = stockAgeRange;
    }

    public Long getOverdueRange() {
        return overdueRange;
    }

    public void setOverdueRange(Long overdueRange) {
        this.overdueRange = overdueRange;
    }
}
