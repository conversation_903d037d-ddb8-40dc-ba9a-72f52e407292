package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStockStoreDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStockStoreQuery;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;

/**
 * @author: lidengfeng
 * @date 2018/9/27 17:34
 */
public interface IProductChargeConfigService {

    /**
     * 新增或修改产品标准费率
     * 
     * @param dto
     * @return: void
     */
    void saveOrUpdateChargeConfig(ProductChargeConfigDTO dto);

    void saveProductChargeConfig(List<ProductChargeConfigDTO> list);

    /**
     * 产品标准费率明细查询
     * 
     * @param productCountQuery
     * @return
     */
    ProductChargeConfigDTO selectProductChargeConfigById(ProductCountQuery productCountQuery);

    /**
     * 根据产品id集合查询标准费率
     * 
     * @param
     * @return
     */
    Map<Long, ProductChargeConfigDTO> selectProductChargeList(ProductChargeQuery productChargeQuery);

    /**
     * 得到产品费率是否配置
     * 
     * @param productCountQuery
     * @return
     */
    Boolean selectCountByProductId(ProductCountQuery productCountQuery);

    /**
     * 获取经销商 的托管产品
     * 
     * @param dealerProductQuery
     * @return
     */
    PageList<DealerProductDTO> selectDealerProductList(DealerProductQuery dealerProductQuery);

    /**
     * 查询申请入库商品和经销商的费用配置
     *
     * @param productInStockQuery
     * @return
     */
    PageList<ProductStoreStockDTO> selectProductInStockList(ProductInStockQuery productInStockQuery);

    /**
     * 查询申请入库商品和经销商的费用配置明细 经销商仓配申请入库产品
     * 
     * @param dealerProductDetailQuery
     * @return
     */
    ProductStoreStockDTO selectDealerProductDetail(DealerProductDetailQuery dealerProductDetailQuery);

    /**
     * 根据经销商id，仓库id查询仓库商品的库存
     * 
     * @param productStockStoreQuery
     * @return
     */
    PageList<ProductStockStoreDTO> findProductStoreList(ProductStockStoreQuery productStockStoreQuery);

    /**
     * 启用停用经销商费用配置
     *
     * @param dto
     * @return
     */
    void updateProductConfigStatus(ProductChargeConfigDTO dto);

    /**
     * 查询产品
     * 
     * @param productProDetailQuery
     * @return
     */
    PageList<ProductProDetailDTO> findProductProList(ProductProDetailQuery productProDetailQuery);

}
