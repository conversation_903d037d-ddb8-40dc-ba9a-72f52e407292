package com.yijiupi.himalaya.supplychain.batchinventory.constant;

public enum ERPDisposedInventoryType {
    /**
     * 全部
     */
    全部(-1),
    /**
     * 残次品
     */
    残次品(0),
    /**
     * 陈列品
     */
    陈列品(1);

    private Integer type;

    ERPDisposedInventoryType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    @Override
    public String toString() {
        return String.valueOf(this.type);
    }

    public static ERPDisposedInventoryType getEnum(Integer type) {
        ERPDisposedInventoryType e = null;

        if (type != null) {
            for (ERPDisposedInventoryType o : values()) {
                if (o.getType().equals(type)) {
                    e = o;
                }
            }
        }
        return e;
    }
}
