package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.util.List;

public class StoreCheckUpdateBatchInventoryDTO implements Serializable {

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 城市ID
     */
    private Integer orgId;

    /**
     * 盘点单编号
     */
    private String noteNo;
    /**
     * 盘点单ID
     */
    private String noteId;

    /**
     * 产品变更信息
     */
    private List<StoreCheckUpdateBatchInventoryItemDTO> changeItemList;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getNoteNo() {
        return noteNo;
    }

    public void setNoteNo(String noteNo) {
        this.noteNo = noteNo;
    }

    public String getNoteId() {
        return noteId;
    }

    public void setNoteId(String noteId) {
        this.noteId = noteId;
    }

    public List<StoreCheckUpdateBatchInventoryItemDTO> getChangeItemList() {
        return changeItemList;
    }

    public void setChangeItemList(List<StoreCheckUpdateBatchInventoryItemDTO> changeItemList) {
        this.changeItemList = changeItemList;
    }
}
