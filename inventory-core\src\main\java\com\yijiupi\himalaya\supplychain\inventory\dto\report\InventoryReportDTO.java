package com.yijiupi.himalaya.supplychain.inventory.dto.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存报表DTO
 * 
 * @author: wangdan
 * @date: 2017年9月8日
 */
public class InventoryReportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long sequence;
    /**
     * 产品库存ID
     */
    private String productStoreId;
    /**
     * 商品ID
     */
    private Long productSkuId;
    /**
     * 商品
     */
    private Long productId;
    /**
     * 商品名称
     */
    private String productSkuName;
    /**
     * 仓库Id
     */
    private Integer warhouseId;
    /**
     * 库存分类
     */
    private Integer storeOwnerType;
    /**
     * 供应商 ownerName代替
     */
    @Deprecated
    private String supplierName;
    /**
     * 所属人ID
     */
    private Long ownerId;
    /**
     * 所属人类型
     */
    private Integer ownerType;
    /**
     * 所属人名称
     */
    private String ownerName;
    /**
     * 库存总数量
     */
    private BigDecimal storeTotalCount;
    /**
     * 库存大数量
     */
    private BigDecimal storeCountMax;
    /**
     * 库存小数量
     */
    private BigDecimal storeCountMin;
    /**
     * 库存预警数量
     */
    private BigDecimal storeWaringCount;
    /**
     * 预售库存数量
     */
    private BigDecimal storePreCount;
    /**
     * 销售库存数量
     */
    private BigDecimal storeSaleCount;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 发货中数量
     */
    private BigDecimal deliveryedCount;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 产品信息规格ID
     */
    private Long productSpecificationId;
    /**
     * 二级货主名称
     */
    private String secOwnerName;

    /**
     * 产品状态,下架(0), 作废(1), 上架(2)
     */
    private Byte productState;

    private String inventoryPinProperty;

    /**
     * 平均库龄
     */
    private Long averageStockAge;
    /**
     * ProductTypeEnums
     * 产品类型：1.成品;2.半成品;3.包装材料
     */
    private Byte productType;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;

    public String getInventoryPinProperty() {
        return inventoryPinProperty;
    }

    public void setInventoryPinProperty(String inventoryPinProperty) {
        this.inventoryPinProperty = inventoryPinProperty;
    }

    public Byte getProductState() {
        return productState;
    }

    public void setProductState(Byte productState) {
        this.productState = productState;
    }

    public String getSecOwnerName() {
        return secOwnerName;
    }

    public void setSecOwnerName(String secOwnerName) {
        this.secOwnerName = secOwnerName;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getSequence() {
        return sequence;
    }

    public void setSequence(Long sequence) {
        this.sequence = sequence;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Integer getWarhouseId() {
        return warhouseId;
    }

    public void setWarhouseId(Integer warhouseId) {
        this.warhouseId = warhouseId;
    }

    public Integer getStoreOwnerType() {
        return storeOwnerType;
    }

    public void setStoreOwnerType(Integer storeOwnerType) {
        this.storeOwnerType = storeOwnerType;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public BigDecimal getStoreCountMax() {
        return storeCountMax;
    }

    public void setStoreCountMax(BigDecimal storeCountMax) {
        this.storeCountMax = storeCountMax;
    }

    public BigDecimal getStoreCountMin() {
        return storeCountMin;
    }

    public void setStoreCountMin(BigDecimal storeCountMin) {
        this.storeCountMin = storeCountMin;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public String getProductSkuName() {
        return productSkuName;
    }

    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public BigDecimal getStoreTotalCount() {
        return storeTotalCount;
    }

    public void setStoreTotalCount(BigDecimal storeTotalCount) {
        this.storeTotalCount = storeTotalCount;
    }

    public BigDecimal getStoreWaringCount() {
        return storeWaringCount;
    }

    public void setStoreWaringCount(BigDecimal storeWaringCount) {
        this.storeWaringCount = storeWaringCount;
    }

    public BigDecimal getStorePreCount() {
        return storePreCount;
    }

    public void setStorePreCount(BigDecimal storePreCount) {
        this.storePreCount = storePreCount;
    }

    public BigDecimal getStoreSaleCount() {
        return storeSaleCount;
    }

    public void setStoreSaleCount(BigDecimal storeSaleCount) {
        this.storeSaleCount = storeSaleCount;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public BigDecimal getDeliveryedCount() {
        return deliveryedCount;
    }

    public void setDeliveryedCount(BigDecimal deliveryedCount) {
        this.deliveryedCount = deliveryedCount;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Long getAverageStockAge() {
        return averageStockAge;
    }

    public void setAverageStockAge(Long averageStockAge) {
        this.averageStockAge = averageStockAge;
    }

    /**
     * 获取 ProductTypeEnums      产品类型：1.成品;2.半成品;3.包装材料
     *
     * @return productType ProductTypeEnums      产品类型：1.成品;2.半成品;3.包装材料
     */
    public Byte getProductType() {
        return this.productType;
    }

    /**
     * 设置 ProductTypeEnums      产品类型：1.成品;2.半成品;3.包装材料
     *
     * @param productType ProductTypeEnums      产品类型：1.成品;2.半成品;3.包装材料
     */
    public void setProductType(Byte productType) {
        this.productType = productType;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }
}
