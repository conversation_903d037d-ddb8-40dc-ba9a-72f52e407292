package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import com.yijiupi.himalaya.base.search.PageCondition;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 批次库存查询dto
 *
 * <AUTHOR> 2018/3/28
 */
public class BatchLocationInfoQueryDTO extends PageCondition implements Serializable {
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 产品ID
     */
    private Long productSkuId;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Integer secOwnerId;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位id
     */
    private Long areaId;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 入库批次时间
     */
    private Date batchTime;

    /**
     * 货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）
     */
    private Byte locationSubcategory;

    /**
     * 是否排除负库存 否：false 是：true
     */
    private Boolean excludeNegativeFlag;

    /**
     * 当前二级货主(上架策略使用)
     */
    private Long curSecOwnerId;

    /**
     * 产品skuID列表
     */
    private List<Long> productSkuIdList;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 是否存在生产日期
     */
    private Boolean existProductionDate = true;

    public Boolean getExcludeNegativeFlag() {
        return excludeNegativeFlag;
    }

    public void setExcludeNegativeFlag(Boolean excludeNegativeFlag) {
        this.excludeNegativeFlag = excludeNegativeFlag;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public String getProductionDateStr() {
        return productionDate == null ? "" : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(productionDate);
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    /**
     * 获取 仓库ID
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库ID
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品ID
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品ID
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 库存渠道,0:酒批，1:大宗产品
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 库存渠道,0:酒批，1:大宗产品
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 二级货主Id
     */
    public Integer getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主Id
     */
    public void setSecOwnerId(Integer secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public Byte getLocationSubcategory() {
        return locationSubcategory;
    }

    public void setLocationSubcategory(Byte locationSubcategory) {
        this.locationSubcategory = locationSubcategory;
    }

    public Long getCurSecOwnerId() {
        return curSecOwnerId;
    }

    public void setCurSecOwnerId(Long curSecOwnerId) {
        this.curSecOwnerId = curSecOwnerId;
    }

    public List<Long> getProductSkuIdList() {
        return productSkuIdList;
    }

    public void setProductSkuIdList(List<Long> productSkuIdList) {
        this.productSkuIdList = productSkuIdList;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Boolean getExistProductionDate() {
        return existProductionDate;
    }

    public void setExistProductionDate(Boolean existProductionDate) {
        this.existProductionDate = existProductionDate;
    }
}
