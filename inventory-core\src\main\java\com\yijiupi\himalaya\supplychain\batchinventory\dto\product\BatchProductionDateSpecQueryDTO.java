package com.yijiupi.himalaya.supplychain.batchinventory.dto.product;

import java.io.Serializable;

public class BatchProductionDateSpecQueryDTO implements Serializable {

    private static final long serialVersionUID = 9054345263732837239L;

    /**
     * 规格ID
     */
    private Long productSpecificationId;

    /**
     * 货主ID
     */
    private Long ownerId;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }
}
