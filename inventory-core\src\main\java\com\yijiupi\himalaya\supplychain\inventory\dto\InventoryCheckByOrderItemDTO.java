package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 根据订单矫正仓库库存
 *
 * <AUTHOR>
 * @date 11/28/20 2:52 PM
 */
public class InventoryCheckByOrderItemDTO implements Serializable {

    private static final long serialVersionUID = 4864025442287551971L;

    /**
     * 订单项Id
     */
    private Long itemId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 订单项detail集合
     */
    private List<InventoryCheckByOrderItemDetailDTO> detailList;

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public List<InventoryCheckByOrderItemDetailDTO> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<InventoryCheckByOrderItemDetailDTO> detailList) {
        this.detailList = detailList;
    }
}
