package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.util.List;

import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 * 
 * <AUTHOR>
 * @date 2023/12/22
 */
public class LocationBatchInventoryQueryInfoDTO extends PageCondition {
    /**
     * 货位全名称
     */
    private String locationFullName;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 货位id列表
     */
    private List<Long> locationIds;

    /**
     * 获取 货位全名称
     *
     * @return locationFullName 货位全名称
     */
    public String getLocationFullName() {
        return this.locationFullName;
    }

    /**
     * 设置 货位全名称
     *
     * @param locationFullName 货位全名称
     */
    public void setLocationFullName(String locationFullName) {
        this.locationFullName = locationFullName;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 货位id列表
     *
     * @return locationIds 货位id列表
     */
    public List<Long> getLocationIds() {
        return this.locationIds;
    }

    /**
     * 设置 货位id列表
     *
     * @param locationIds 货位id列表
     */
    public void setLocationIds(List<Long> locationIds) {
        this.locationIds = locationIds;
    }
}
