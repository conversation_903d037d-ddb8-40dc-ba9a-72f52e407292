package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.model.StoreBatchQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventorySyncPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.LocationCapacityPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 批次库存
 *
 * <AUTHOR> 2018/1/25
 */
@Mapper
public interface BatchInventoryProductStoreBatchMapper {
    /**
     * 根据storeId查询批次库存信息(>0的批次)
     */
    List<ProductStoreBatchPO> findProductStoreBatch(@Param("productStoreId") String productStoreId);

    /**
     * 根据storeId查询批次库存信息
     */
    ProductStoreBatchPO findProductStoreBatchByBatchId(@Param("productStoreId") String productStoreId,
        @Param("batchId") String orderId);

    /**
     * 根据storeId查询最后一个批次库存信息
     *
     * @param productStoreId
     * @return
     */
    ProductStoreBatchPO findLastProductStoreBatch(@Param("productStoreId") String productStoreId);

    /**
     * 批量修改批次库存信息(只修改数量)
     */
    void updateBatchInventory(@Param("list") List<ProductStoreBatchPO> list);

    /**
     * 批量修改批次库存信息(只修改数量,在原有基础上修改)
     */
    void updateBatchInventoryAdd(@Param("list") List<ProductStoreBatchPO> list);

    /**
     * 批量修改批次库存信息(只修改数量,不在原有基础上修改,直接set)
     */
    void updateBatchInventoryNotAdd(@Param("list") List<ProductStoreBatchPO> list);

    /**
     * 批量修改仓库库存信息(在原有基础上修改)
     */
    void updateStoreInventoryNotAdd(@Param("list") List<BatchInventoryPO> list);

    /**
     * 批量修改批次库存信息(修改数量及货位信息)
     */
    void updateBatchInventoryLocation(@Param("list") List<ProductStoreBatchPO> list);

    /**
     * 创建批次库存
     */
    void addBatchInventory(ProductStoreBatchPO productStoreBatchPO);

    /**
     * 查看批次库存信息
     *
     * @param batchInventoryQueryDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchInventoryPO> findBatchInventoryList(@Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 查看批次库存信息
     *
     * @param batchInventoryQueryDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchInventoryPO> findBatchInventoryListBySpecification(
        @Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 查看批次库存信息(根据货位或产品名称获取批次库存)
     *
     * @param batchInventoryQueryDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchInventoryPO> findBatchInventoryListNew(@Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 查询一条库存为0的批次库存
     * 
     * @param batchInventoryQueryDTO
     * @return
     */
    BatchInventoryPO queryZeroBatchInventory(@Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 查看批次库存信息(根据货位或产品名称获取批次库存，支持skuIds)
     *
     * @param batchInventoryQueryDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchInventoryPO> findBatchInventoryListBatchNew(
        @Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 根据skuid获取库存货位
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    List<BatchInventoryPO> findInventoryLocationBySkuId(@Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 根据库存ID，查询关联ProductInfo保质期信息
     *
     * @param storeIdS
     * @return
     */
    List<BatchInventoryPO> findInventoryShelfUnitInfoById(@Param("storeIdS") List<String> storeIdS);

    /**
     * 根据主键id查询批次库存信息List
     *
     * @param storeBatchIdS
     * @return
     */
    List<ProductStoreBatchPO> findProductStoreBatchListById(@Param("storeBatchIdS") List<String> storeBatchIdS);

    /**
     * 根据主键id查询批次库存信息List
     *
     * @return
     */
    List<ProductStoreBatchPO> findBatchListByProductStoreIds(@Param("storeIdS") List<String> storeIdS);

    /**
     * 根据主键id查询批次库存信息
     *
     * @param storeBatchId
     * @return
     */
    ProductStoreBatchPO findProductStoreBatchById(@Param("storeBatchId") String storeBatchId);

    /**
     * 库存表id+货位id+batchTime查询主键id
     *
     * @param productStoreId
     * @param locationId
     * @return
     */
    String findBatchInventoryIsExist(@Param("productStoreId") String productStoreId,
        @Param("locationId") Long locationId, @Param("batchAttributeInfoNo") String batchAttributeInfoNo);

    /**
     * 批量添加批次库存
     *
     * @param productStoreBatchPOS
     */
    void insertBatchInventoryPOList(@Param("list") List<ProductStoreBatchPO> productStoreBatchPOS);

    /**
     * 通过storeId和货区类查询批次库存
     *
     * @param productInventoryChangeRecordPO
     */
    List<ProductStoreBatchPO>
        selectTmpLocationInventory(@Param("po") ProductInventoryChangeRecordPO productInventoryChangeRecordPO);

    /**
     * 通过storeId和货区类查询批次库存
     *
     * @param productInventoryChangeRecordPO
     * @param subcategory
     */
    List<ProductStoreBatchPO> selectTmpLocationInventoryBySubCategory(
        @Param("po") ProductInventoryChangeRecordPO productInventoryChangeRecordPO,
        @Param("subcategory") Integer subcategory, @Param("exlist") List<Integer> exSubcategory);

    /**
     * 根据storeId查询仓库id
     *
     * @param storeId
     * @return
     */
    Integer findWarehouseIdByStoreId(@Param("storeId") String storeId);

    /**
     * 根据storeId查询城市id
     *
     * @param storeId
     * @return
     */
    Integer findCityIdByStoreId(@Param("storeId") String storeId);

    /**
     * 通过storeId和货位Id，判断货位上除了这个产品之外，是否有其他产品
     */
    List<String> checkExistOtherProductInLocation(@Param("list") Set<Long> lstLocationIds,
        @Param("productStoreId") String productStoreId);

    /**
     * 获取产品SKU信息对应仓库库存(条件productSkuId,warehouseId,channel)
     */
    List<String> findProductStoreId(@Param("warehouseId") Integer warehouseId, @Param("productSkuId") Long productSkuId,
        @Param("channel") Integer channel, @Param("source") Integer source, @Param("secOwnerId") Integer secOwnerId);

    List<LocationCapacityPO> getLocationCapacityPOs(@Param("list") List<Long> lstLocationIds);

    /**
     * 根据类别查找所有批次编号为空的
     *
     * @param ownerType
     * @return
     */
    List<ProductStoreBatchNoDTO> findProductStoreBatchByOwnerType(@Param("ownerType") Integer ownerType);

    void updateProductStoreBatchNo(@Param("list") List<ProductStoreBatchNoUpdateDTO> list);

    /**
     * 根据skuid获取库存货位（不关联location）
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    List<BatchInventoryPO> findBatchStoreBySkuId(@Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 查找需要同步的货位库存信息
     *
     * @return
     */
    List<BatchInventorySyncPO> listBatchInventoryBySync(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);

    PageResult<BatchInventorySyncPO> pageListBatchInventoryBySync(@Param("dto") BatchInventorySyncQueryDTO queryDTO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    List<ProductStoreBatchPO> listNegativeBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);

    List<BatchInventorySyncPO> getZeroBatchInventoryByWarehouseId(Integer warehouseId);

    void clearZeroBatchInventoryByWarehouseId(@Param("list") List<BatchInventorySyncPO> lstBatchStore);

    List<BatchInventorySyncPO> listAdjustBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);

    List<BatchInventorySyncPO> listTransferBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO);

    /**
     * 根据库存id删除批次库存
     */
    void deleteBatchInventoryByStoreIds(@Param("list") List<String> storeIds);

    /**
     * 查询货区所有货位库存信息
     */
    List<ProductStoreBatchPO> findProductStoreBatchByLocationArea(@Param("warehouseId") Integer warehouseId,
        @Param("locationArea") List<String> locationAreaList, @Param("ownerId") Long ownerId);

    /**
     * 查询货区所有货位库存信息
     */
    List<ProductStoreBatchPO> findPickingProductStoreBatchWithOutSku(@Param("warehouseId") Integer warehouseId,
        @Param("ownerId") Long ownerId);

    /**
     * 根据id删除批次库存
     */
    void deleteBatchInventoryByIds(@Param("productStoreBatchIds") List<String> productStoreBatchIds);

    /**
     * 查询库龄产品的批次库存
     */
    List<BatchInventoryDTO> findStockAgeProductInventory(
        @Param("so") List<StockAgeProductInventorySO> stockAgeProductInventorySOS,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 查询库龄产品的批次库存
     */
    List<BatchInventoryDTO> findStockAgeProductInventoryBySku(@Param("skuIds") List<Long> skuIds,
        @Param("warehouseId") Integer warehouseId);

    void updateBatchInventoryZeroByStoreIds(@Param("list") List<String> storeIds,
        @Param("excludeStoreBatchIdList") List<String> excludeStoreBatchIdList);

    List<ProductStoreBatchPO> findBatchStoreBySpec(
        @Param("specList") List<ProductionDateQueryDTO> productionDateQueryDTOS,
        @Param("warehouseId") Integer warehouseId);

    List<ProductionDateDTO> findProductionDate(@Param("warehouseId") Integer warehouseId,
        @Param("productBasicList") List<ProductionDateQueryDTO> productBasicList);

    List<ProductionDateDTO>
        findProductionDateList(@Param("productBasicList") List<ProductionDateQueryDTO> productBasicList);

    /**
     * 查看批次库存信息(根据货位或产品名称获取批次库存，支持skuIds)
     *
     * @param batchInventoryQueryDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchInventoryPO> findStoreAgeRefBatchInventoryList(
        @Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 修改批次编号和生产日期，批次时间
     *
     * @param batchInventoryInfoUpdateDTOS
     */
    void updateProductStoreBatchNoAndDate(
        @Param("list") List<BatchInventoryInfoUpdateDTO> batchInventoryInfoUpdateDTOS);

    /**
     * 查询所有负库存仓库
     *
     * @return
     */
    List<BatchInventoryNegativeDTO> listWarehouseByInventoryNegative();

    void batchAdd(@Param("list") List<ProductStoreBatchPO> productStoreBatchPOS);

    /**
     * 根据仓库及产品规格信息查询批次生产日期
     */
    List<BatchProductionDateDTO> findProductionDateFromStoreBatch(BatchProductionDateQueryDTO productionDateQueryDTO);

    /**
     * 根据货位、批次信息查询产品SKU基础信息
     */
    PageResult<BatchInventoryPO> findProductBaseInfoFromBatchInventory(
        @Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    List<ProductionDatePriceDTO> findProductionDatePriceBySkuIds(@Param("warehouseId") Integer warehouseId,
        @Param("skuIds") List<Long> skuIds);

    /**
     * 查询货位库存
     */
    PageResult<ProductStoreBatchPO> listProductStoreBatch(@Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    // /**
    // * 查询有批次库存、有生产日期的所有SkuId
    // */
    // List<BatchLocationInfoDTO> listProductStoreBatchBySku(@Param("dto")BatchLocationInfoQueryDTO queryDTO);

    // /**
    // * 查询有批次库存、有生产日期的所有SkuId
    // */
    // PageResult<BatchLocationInfoDTO> listProductStoreBatchBySku(@Param("dto")BatchLocationInfoQueryDTO
    // queryDTO,@Param("pageNum") Integer pageNum,
    // @Param("pageSize") Integer pageSize);

    /**
     * 查询批次库存信息
     */
    PageResult<BatchInventoryPO> findBatchInventoryInfo(@Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 分页查询库存信息
     *
     * @param storeBatchQueryDTO
     * @return
     */
    PageResult<ProductStoreBatchPO>
        pageListProductStoreBatch(@Param("storeBatchQueryDTO") StoreBatchQueryDTO storeBatchQueryDTO);

    /**
     * 查看产品关联货位的批次库存信息
     *
     * @param batchInventoryQueryDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchInventoryPO> findProductLocationBatchInventoryList(
        @Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 根据storeIds查询批次库存最老生产日期(>0的批次)
     */
    List<ProductStoreBatchPO> findProductionDateByProductStoreIds(
        @Param("productStoreIds") List<String> productStoreIds, @Param("exlist") List<Integer> locationCategory);

    int clearBatchInventoryProductDate(@Param("ids") List<String> ids);

    /**
     * 查询货区所有货位库存信息
     */
    List<ProductStoreBatchPO> findProductStoreBatchByCondition(@Param("queryDTO") BatchInventorySyncQueryDTO queryDTO);

    /**
     * 批次库存查询，返回促销id
     */
    List<BatchInventoryDTO> listStoreBatchWithPromotion(@Param("so") BatchInventoryQueryDTO batchInventoryQueryDTO);
}
