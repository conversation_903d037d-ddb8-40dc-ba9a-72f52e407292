package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;

/**
 * 查询商品返回参数
 * 
 * @author: lidengfeng
 * @date 2019/1/16 15:08
 */
public class ProductProDetailDTO implements Serializable {
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品skuid
     */
    private Long productSkuId;
    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 产品品牌
     */
    private String productBrand;

    /**
     * 获取 产品名称
     * 
     * @return
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置 产品名称
     * 
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 产品skuid
     * 
     * @return
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    /**
     * 设置 产品skuid
     * 
     * @param productSkuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取规格参数id
     * 
     * @return
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 规格参数id
     * 
     * @param productSpecificationId
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取包装规格
     * 
     * @return
     */
    public String getSpecificationName() {
        return specificationName;
    }

    /**
     * 设置 包装规格
     * 
     * @param specificationName
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    /**
     * 获取 产品品牌
     * 
     * @return
     */
    public String getProductBrand() {
        return productBrand;
    }

    /**
     * 设置 产品品牌
     * 
     * @param productBrand
     */
    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }
}
