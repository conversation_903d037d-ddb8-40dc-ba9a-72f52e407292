<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO">
        <id column="Id" property="id" jdbcType="VARCHAR"/>
        <result column="productstore_id" property="productStoreId" jdbcType="VARCHAR"/>
        <result column="totalcount_minunit" property="totalCount" jdbcType="VARCHAR"/>
        <result column="productiondate" property="productionDate" jdbcType="TIMESTAMP"/>
        <result column="location_id" property="locationId" jdbcType="BIGINT"/>
        <result column="location_name" property="locationName" jdbcType="VARCHAR"/>
        <result column="locationCategory" property="locationCategory" jdbcType="TINYINT"/>
        <result column="subcategory" property="subcategory" jdbcType="TINYINT"/>
        <result column="expiretime" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="batchtime" property="batchTime" jdbcType="TIMESTAMP"/>
        <result column="createuserId" property="createUserId" jdbcType="INTEGER"/>
        <result column="packageQuantity" property="packageQuantity" jdbcType="DECIMAL"/>
        <result column="BatchAttributeInfoNo" property="batchAttributeInfoNo" jdbcType="VARCHAR"/>
        <result column="OwnerType" property="ownerType" jdbcType="TINYINT"/>
        <result column="AdventId" property="adventId" jdbcType="BIGINT"/>
        <result column="batchProperty" property="batchProperty" jdbcType="TINYINT"/>
        <result column="businessType" property="businessType" jdbcType="TINYINT"/>
    </resultMap>

    <select id="findProductStoreBatch" resultMap="BaseResultMap">
        SELECT id,
               totalcount_minunit,
               productstore_id,
               batchtime,      -- 批次入库日期
               productiondate, -- 生产日期
               expiretime      -- 过期日期
                ,
               batchProperty,
               businessType
        from productstorebatch
        where productstore_id = #{productStoreId,jdbcType=VARCHAR}
          and totalcount_minunit &gt; 0
        order by createtime asc
    </select>
    <select id="findProductStoreBatchByBatchId"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO">
        SELECT id,
               totalcount_minunit as totalCount,
               productstore_id    as productStoreId,
               batchProperty,
               businessType
        from productstorebatch
        where productstore_id = #{productStoreId,jdbcType=VARCHAR}
          and id = #{batchId,jdbcType=VARCHAR} limit 1
    </select>

    <select id="findLastProductStoreBatch" resultMap="BaseResultMap">
        SELECT id,
               totalcount_minunit,
               productstore_id,
               batchtime,      -- 批次入库日期
               productiondate, -- 生产日期
               expiretime      -- 过期日期
                ,
               batchProperty,
               businessType
        from productstorebatch
        where productstore_id = #{productStoreId,jdbcType=VARCHAR}
        order by createtime desc limit 1
    </select>

    <update id="updateBatchInventory">
        update productstorebatch
        <trim prefix="set" suffixOverrides=",">
            lastupdatetime = NOW(),
            <trim prefix="totalcount_minunit =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then totalcount_minunit + #{item.totalCount}
                </foreach>
            </trim>
            <trim prefix="BatchAttributeInfoNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.batchAttributeInfoNo != null">
                        when id=#{item.id} then #{item.batchAttributeInfoNo}
                    </if>
                    <if test="item.batchAttributeInfoNo == null ">
                        when id=#{item.id} then productstorebatch.BatchAttributeInfoNo
                    </if>
                </foreach>
            </trim>
            <trim prefix="batchProperty =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.batchProperty != null">
                        when id=#{item.id} then #{item.batchProperty,jdbcType=TINYINT}
                    </if>
                    <if test="item.batchProperty == null ">
                        when id=#{item.id} then productstorebatch.batchProperty
                    </if>
                </foreach>
            </trim>
            <trim prefix="businessType =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.businessType != null">
                        when id=#{item.id} then #{item.businessType,jdbcType=TINYINT}
                    </if>
                    <if test="item.businessType == null ">
                        when id=#{item.id} then productstorebatch.businessType
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateBatchInventoryAdd">
        update productstorebatch
        <trim prefix="set" suffixOverrides=",">
            lastupdatetime = NOW(),
            <trim prefix="totalcount_minunit =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then totalcount_minunit + #{item.totalCount}
                </foreach>
            </trim>
            <trim prefix="batchProperty =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.batchProperty != null">
                        when id=#{item.id} then #{item.batchProperty,jdbcType=TINYINT}
                    </if>
                    <if test="item.batchProperty == null ">
                        when id=#{item.id} then productstorebatch.batchProperty
                    </if>
                </foreach>
            </trim>
            <trim prefix="businessType =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.businessType != null">
                        when id=#{item.id} then #{item.businessType,jdbcType=TINYINT}
                    </if>
                    <if test="item.businessType == null ">
                        when id=#{item.id} then productstorebatch.businessType
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateBatchInventoryNotAdd">
        update productstorebatch
        <trim prefix="set" suffixOverrides=",">
            lastupdatetime = NOW(),
            <trim prefix="totalcount_minunit =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.totalCount}
                </foreach>
            </trim>
            <trim prefix="batchProperty =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.batchProperty != null">
                        when id=#{item.id} then #{item.batchProperty,jdbcType=TINYINT}
                    </if>
                    <if test="item.batchProperty == null ">
                        when id=#{item.id} then productstorebatch.batchProperty
                    </if>
                </foreach>
            </trim>
            <trim prefix="businessType =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.businessType != null">
                        when id=#{item.id} then #{item.businessType,jdbcType=TINYINT}
                    </if>
                    <if test="item.businessType == null ">
                        when id=#{item.id} then productstorebatch.businessType
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateBatchInventoryLocation">
        update productstorebatch
        <trim prefix="set" suffixOverrides=",">
            lastupdatetime = NOW(),
            <trim prefix="totalcount_minunit =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then totalcount_minunit + #{item.totalCount}
                </foreach>
            </trim>
            <trim prefix="location_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.locationId}
                </foreach>
            </trim>
            <trim prefix="location_name =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.locationName}
                </foreach>
            </trim>
            <trim prefix="locationCategory =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.locationCategory}
                </foreach>
            </trim>
            <trim prefix="subcategory =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.subcategory}
                </foreach>
            </trim>
            <trim prefix="batchProperty =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.batchProperty != null">
                        when id=#{item.id} then #{item.batchProperty,jdbcType=TINYINT}
                    </if>
                    <if test="item.batchProperty == null ">
                        when id=#{item.id} then productstorebatch.batchProperty
                    </if>
                </foreach>
            </trim>
            <trim prefix="businessType =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.businessType != null">
                        when id=#{item.id} then #{item.businessType,jdbcType=TINYINT}
                    </if>
                    <if test="item.businessType == null ">
                        when id=#{item.id} then productstorebatch.businessType
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <insert id="addBatchInventory">
        INSERT INTO productstorebatch (id, productstore_id, totalcount_minunit, batchtime,
                                       createtime, lastupdatetime, expiretime, productiondate, BatchAttributeInfoNo,
                                       location_id, location_name, subcategory, InStockOrderNo, batchProperty,
                                       businessType)
        VALUES (#{id}, #{productStoreId}, #{totalCount}, #{batchTime}, NOW(), NOW(), #{expireTime}, #{productionDate},
                #{batchAttributeInfoNo},
                #{locationId}, #{locationName}, #{subcategory}, #{inStockOrderNo,jdbcType=VARCHAR},
                #{batchProperty,jdbcType=TINYINT}, #{businessType,jdbcType=TINYINT})
    </insert>

    <sql id="findBatchInventorySql">
        SELECT
        psb.BatchAttributeinfoNo as batchAttributeInfoNo,
        ps.Id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        CONCAT(case psku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, psku.NAME) AS
        productSkuName,
        ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId,
        ps.OwnerType as ownerType,
        psb.totalcount_minunit as storeTotalCount,
        ps.Channel as channel,
        psku.specificationName,
        psku.unitName,
        psku.packageName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id as secOwnerId,
        loc.id as locationId,
        loc.name as locationName,
        loc.category as locationCategory,
        loc.subcategory as locationSubcategory,
        loc.area as area,
        loc.sequence AS locationSequence,
        loc.BusinessType as locationBusinessType,
        area.subcategory as areaSubcategory,
        psb.batchtime as batchTime,
        psb.expiretime as expireTime,
        psb.productiondate as productionDate,
        psb.id as storeBatchId,
        psku.SaleModel as saleModel,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.OwnerName,
        own.OwnerName as secOwnerName,
        pc.ProductFeature,
        pc.MaxReplenishment,
        pc.MinReplenishment,
        pc.palletQuantity,
        pc.storageAttribute,
        psku.productBrand,
        IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit) as shelfLifeUnit,
        psku.ProductState as productState,
        ps.TotalCount_MinUnit as unitTotolCount,
        psb.createTime as createTime,
        psb.batchProperty
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id =ps.id
        LEFT JOIN productinfo pi on pi.id = psku.ProductInfo_Id
        LEFT JOIN Location loc on loc.id= psb.location_id and loc.warehouse_id = ps.warehouse_id
        <if test="so.warehouseId!=null">
            AND loc.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.cityId!=null">
            AND loc.City_Id = #{so.cityId}
        </if>
        LEFT JOIN Location area on loc.area_id = area.id
        LEFT JOIN owner own on ps.SecOwner_Id is not null and own.id = ps.SecOwner_Id
        <if test="so.limitSku == null or so.limitSku != 1">
            <if test="so.warehouseAllocationType == null or so.warehouseAllocationType == ''">
                LEFT JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id =
                pc.Warehouse_Id
            </if>

            <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
                INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id =
                pc.Warehouse_Id
                and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or
                pc.StorageAttribute is null)
            </if>
        </if>

        <if test="so.limitSku != null and so.limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id =
            pc.Warehouse_Id
            <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
                and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or
                pc.StorageAttribute is null)
            </if>
        </if>
        where 1=1
    </sql>

    <!--查询批次库存信息-->
    <select id="findBatchInventoryList"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        <include refid="findBatchInventorySql">
        </include>
        <if test="so.showAll == null">
            AND ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        </if>
        <if test="so.showAll != null and so.showAll == -1">
            AND <![CDATA[ psb.totalcount_minunit < 0 ]]>
        </if>
        <if test="so.channel!=null">
            AND ps.Channel=#{so.channel,jdbcType=INTEGER}
        </if>
        <if test="so.source!=null">
            AND psku.Source=#{so.source,jdbcType=INTEGER}
        </if>
        <if test="so.productSkuId!=null">
            AND psku.ProductSku_Id=#{so.productSkuId,jdbcType=BIGINT}
        </if>
        <if test="so.secOwnerId!=null">
            AND ps.SecOwner_Id=#{so.secOwnerId,jdbcType=BIGINT}
        </if>

        <if test="so.warehouseId!=null">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.ownerType!=null">
            AND ps.OwnerType=#{so.ownerType,jdbcType=INTEGER}
        </if>
        <if test="so.locationId!=null">
            AND psb.location_id=#{so.locationId,jdbcType=BIGINT}
        </if>

        <if test="so.locationName!=null and so.locationName!=''">
            AND psb.location_name like
            concat(#{so.locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.locationCategory!=null">
            AND loc.category = #{so.locationCategory}
        </if>
        <if test="so.subCategory!=null">
            AND loc.subcategory = #{so.subCategory}
        </if>
        <if test="so.subCategoryList!=null and so.subCategoryList.size()>0">
            AND loc.subcategory in
            <foreach collection="so.subCategoryList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="so.supplierId!=null">
            and ps.Owner_Id=#{so.supplierId,jdbcType=BIGINT}
            and ps.OwnerType = 1
        </if>
        <if test="so.agencyId!=null">
            and ps.Owner_Id=#{so.agencyId,jdbcType=BIGINT}
            and ps.OwnerType = 2
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like
            concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.batchAttributeInfoNo!=null and so.batchAttributeInfoNo!=''">
            AND
            psb.BatchAttributeinfoNo=#{so.batchAttributeInfoNo,jdbcType=VARCHAR}
        </if>
        <if test="so.skuIds!=null and so.skuIds.size()>0">
            and psku.productsku_id in
            <foreach collection="so.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.productStoreId != null">
            and psb.productstore_id = #{so.productStoreId,jdbcType=VARCHAR}
        </if>
        <if test="so.locationIds != null and so.locationIds.size() > 0">
            and psb.location_id in
            <foreach collection="so.locationIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="so.filterLocations!=null and so.filterLocations.size()!=0">
            and loc.subcategory not in
            <foreach collection="so.filterLocations" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="so.nonnegative != null and so.nonnegative == true">
            and <![CDATA[ psb.totalcount_minunit > 0 ]]>
        </if>
        <if test="so.deleted != null and so.deleted.size > 0">
            and psku.IsDelete in
            <foreach item="item" collection="so.deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="so.locationBusinessType != null">
            AND loc.BusinessType = #{so.locationBusinessType}
        </if>
        <if test="so.storeBatchIds != null and so.storeBatchIds.size() > 0">
            and psb.Id in
            <foreach collection="so.storeBatchIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="so.startTime != null">
            and <![CDATA[ psb.createtime >= #{so.startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="so.endTime != null">
            and <![CDATA[ psb.createtime <= #{so.endTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="so.batchProperty != null">
            and psb.batchProperty = #{so.batchProperty,jdbcType=TINYINT}
        </if>
        <if test="so.excludeLocationBusinessTypes != null and so.excludeLocationBusinessTypes.size() > 0">
            AND (loc.BusinessType is null or loc.BusinessType not in
            <foreach collection="so.excludeLocationBusinessTypes" index="index" item="item" open="(" separator=","
                     close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
            )
        </if>
        <choose>
            <when test="so.orderByBatchTime != null and so.orderByBatchTime == true">
                order by psb.batchTime desc
            </when>
            <otherwise>
                order by loc.subcategory asc,psb.productiondate desc,psb.totalcount_minunit desc
            </otherwise>
        </choose>
    </select>

    <!--查询批次库存信息-->
    <select id="findBatchInventoryListBySpecification"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        SELECT
        psb.BatchAttributeinfoNo as batchAttributeInfoNo,
        ps.Id as productStoreId,
        info.ProductName AS productSkuName,
        ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId,
        ps.OwnerType as ownerType,
        psb.totalcount_minunit as storeTotalCount,
        ps.Channel as channel,
        spec.Name as SpecificationName,
        spec.unitName,
        spec.packageName,
        spec.packageQuantity,
        ps.SecOwner_Id as secOwnerId,
        loc.id as locationId,
        loc.name as locationName,
        loc.category as locationCategory,
        loc.subcategory as locationSubcategory,
        loc.area as area,
        loc.sequence AS locationSequence,
        psb.batchtime as batchTime,
        psb.expiretime as expireTime,
        psb.productiondate as productionDate,
        psb.id as storeBatchId,
        spec.Id as productSpecificationId,
        own.OwnerName as ownerName,
        secOwn.OwnerName as secOwnerName,
        info.Brand as productBrand,
        info.MonthOfShelfLife as monthOfShelfLife,
        info.ShelfLifeUnit as shelfLifeUnit,
        ps.TotalCount_MinUnit as unitTotolCount
        FROM
        productstore ps
        INNER JOIN productinfospecification spec ON ps.ProductSpecification_Id=spec.Id
        INNER JOIN productinfo info ON spec.ProductInfo_Id=info.id
        INNER JOIN productstorebatch psb on psb.productstore_id =ps.id
        LEFT JOIN Location loc on loc.id= psb.location_id and loc.warehouse_id = ps.warehouse_id
        LEFT JOIN owner own on ps.Owner_Id is not null and own.id = ps.Owner_Id
        LEFT JOIN owner secOwn on ps.SecOwner_Id is not null and secOwn.id = ps.SecOwner_Id
        where 1=1
        <if test="so.showAll == null">
            AND ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        </if>
        <if test="so.channel!=null">
            AND ps.Channel=#{so.channel,jdbcType=INTEGER}
        </if>
        <if test="so.secOwnerId!=null">
            AND ps.SecOwner_Id=#{so.secOwnerId,jdbcType=BIGINT}
        </if>

        <if test="so.warehouseId!=null">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.ownerType!=null">
            AND ps.OwnerType=#{so.ownerType,jdbcType=INTEGER}
        </if>
        <if test="so.locationId!=null">
            AND psb.location_id=#{so.locationId,jdbcType=BIGINT}
        </if>

        <if test="so.locationName!=null and so.locationName!=''">
            AND psb.location_name like
            concat(#{so.locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.locationCategory!=null">
            AND loc.category = #{so.locationCategory}
        </if>
        <if test="so.subCategory!=null">
            AND loc.subcategory = #{so.subCategory}
        </if>
        <if test="so.subCategoryList!=null and so.subCategoryList.size()>0">
            AND loc.subcategory in
            <foreach collection="so.subCategoryList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="so.supplierId!=null">
            and ps.Owner_Id=#{so.supplierId,jdbcType=BIGINT}
            and ps.OwnerType = 1
        </if>
        <if test="so.agencyId!=null">
            and ps.Owner_Id=#{so.agencyId,jdbcType=BIGINT}
            and ps.OwnerType = 2
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND info.ProductName like
            concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.batchAttributeInfoNo!=null and so.batchAttributeInfoNo!=''">
            AND
            psb.BatchAttributeinfoNo=#{so.batchAttributeInfoNo,jdbcType=VARCHAR}
        </if>
        <if test="so.productStoreId != null">
            and psb.productstore_id = #{so.productStoreId,jdbcType=VARCHAR}
        </if>
        order by ps.warehouse_id,ps.Id, loc.subcategory asc,psb.productiondate desc,psb.totalcount_minunit desc
    </select>

    <!--查询批次库存信息(根据货位或产品名称获取批次库存)-->
    <select id="findBatchInventoryListNew"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        <include refid="findBatchInventorySql">
        </include>
        AND ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        <if test="so.productSkuId!=null and so.productSkuId!=''">
            AND psku.ProductSku_Id = #{so.productSkuId}
        </if>
        <if test="so.warehouseId!=null and so.warehouseId!=''">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.cityId!=null and so.cityId!=''">
            AND ps.City_Id = #{so.cityId}
        </if>
        <if test="so.locationName!=null and so.locationName!=''">
            AND loc.name like concat(#{so.locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.locationFullName!=null and so.locationFullName!=''">
            AND psb.location_name = #{so.locationFullName,jdbcType=VARCHAR}
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.source != null">
            AND psku.Source = #{so.source,jdbcType=INTEGER}
        </if>
        <if test="so.subCategoryList!=null and so.subCategoryList.size()>0">
            AND loc.subcategory in
            <foreach collection="so.subCategoryList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="so.skuIds!=null and so.skuIds.size()>0">
            and psku.productsku_id in
            <foreach collection="so.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.deleted != null and so.deleted.size > 0">
            and psku.IsDelete in
            <foreach item="item" collection="so.deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="so.locationIds != null and so.locationIds.size() > 0">
            AND psb.location_id in
            <foreach collection="so.locationIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by psb.productiondate desc,psb.totalcount_minunit desc
    </select>


    <!--查询批次库存信息(根据货位或产品名称获取批次库存，支持skuIds)-->
    <select id="findBatchInventoryListBatchNew"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        <include refid="findBatchInventorySql">
        </include>
        AND ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        <if test="so.productSkuId!=null and so.productSkuId!=''">
            AND psku.ProductSku_Id = #{so.productSkuId}
        </if>
        <if test="so.skuIds!=null and so.skuIds.size()>0">
            and psku.ProductSku_Id in
            <foreach collection="so.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.warehouseId!=null and so.warehouseId!=''">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.locationName!=null and so.locationName!=''">
            AND psb.location_name like concat(#{so.locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.locationFullName!=null and so.locationFullName!=''">
            AND psb.location_name = #{so.locationFullName,jdbcType=VARCHAR}
        </if>
        <if test="so.subCategory !=null and so.subCategory !=''">
            AND loc.subCategory = #{so.subCategory,jdbcType=INTEGER}
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.specIdAndOwnerIdList!=null and so.specIdAndOwnerIdList.size()>0">
            AND
            <foreach collection="so.specIdAndOwnerIdList" item="item" index="index" separator="or" open="(" close=")">
                (
                ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
                <if test="item.ownerId == null">
                    and ps.Owner_Id is null
                </if>
                <if test="item.ownerId != null">
                    and ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}
                </if>
                )
            </foreach>
        </if>
        <if test="so.locationIds != null and so.locationIds.size() > 0">
            AND psb.location_id in
            <foreach collection="so.locationIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        order by psb.productiondate desc,psb.totalcount_minunit desc
    </select>

    <select id="findInventoryLocationBySkuId"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        <include refid="findBatchInventorySql">
        </include>
        AND psb.totalcount_minunit != 0
        <if test="so.warehouseId!=null and so.warehouseId!=''">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.productSkuId!=null and so.productSkuId!=''">
            AND psku.ProductSku_Id = #{so.productSkuId}
        </if>
        <if test="so.productSkuIdList != null and so.productSkuIdList.size() > 0">
            AND psku.ProductSku_Id in
            <foreach collection="so.productSkuIdList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.subCategoryList != null and so.subCategoryList.size() > 0">
            AND loc.subcategory in
            <foreach collection="so.subCategoryList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
    </select>

    <select id="findInventoryShelfUnitInfoById"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        select distinct p.Id as productStoreId,info.monthOfShelfLife ,info.shelfLifeUnit
        from productstore p
        inner join productinfospecification spec on spec.id = p.ProductSpecification_Id
        inner join productinfo info on info.id = spec.ProductInfo_Id
        where p.id in
        <foreach collection="storeIdS" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findProductStoreBatchListById" resultMap="BaseResultMap">
        SELECT
        id,
        productstore_id,
        totalcount_minunit,
        productiondate, -- 生产日期
        location_id,
        location_name,
        locationCategory,
        subcategory,
        expiretime, -- 过期日期
        batchtime, -- 批次入库日期
        createuserId,
        BatchAttributeInfoNo, batchProperty, businessType
        from productstorebatch
        where id IN
        <foreach collection="storeBatchIdS" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        order by createtime asc
    </select>

    <select id="findBatchListByProductStoreIds"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO">
        SELECT
        psb.id,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.batchtime as batchTime, -- 批次入库日期
        psb.productiondate as productionDate, -- 生产日期
        psb.expiretime as expireTime, -- 过期日期
        psb.BatchAttributeInfoNo as batchAttributeInfoNo,
        psb.createuserId as createUserId,
        loc.id as locationId,
        loc.name as locationName,
        loc.category as locationCategory,
        loc.subcategory as subcategory,
        psku.packageQuantity as packageQuantity,
        psku.ProductSku_Id as productSkuId,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.Company_Id as ownerId,
        psku.Name as productName,
        psku.specificationName as specificationName,
        ps.warehouse_id as warehouseId, psb.batchProperty, psb.businessType
        from productstorebatch psb
        inner join productstore ps on psb.productstore_id =ps.id
        inner join productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id AND
        ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        left join location loc on loc.id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where psb.productstore_id IN
        <foreach collection="storeIdS" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findProductStoreBatchById" resultMap="BaseResultMap">
        SELECT psb.id,
               psb.productstore_id,
               psb.totalcount_minunit,
               psb.productiondate,
               psb.location_id,
               psb.location_name,
               psb.locationCategory,
               psb.subcategory,
               psb.expiretime,
               psb.batchtime,
               psb.createuserId,
               psb.BatchAttributeInfoNo,
               ifnull(pab.id, 0) as adventId,
               psb.batchProperty,
               psb.businessType
        from productstorebatch psb
                     left join productpromotionstorebatch pab
                on psb.BatchAttributeInfoNo = pab.BatchAttributeInfoNo and pab.IsDelete = 0
        where psb.id = #{storeBatchId}
        order by psb.createtime asc
    </select>

    <select id="findBatchInventoryIsExist" resultType="java.lang.String">
        SELECT id
        FROM productstorebatch
        WHERE productstore_id = #{productStoreId}
          AND location_id = #{locationId}
          and BatchAttributeInfoNo = #{batchAttributeInfoNo}
          and totalcount_minunit != 0 limit 1
    </select>
    <select id="insertBatchInventoryPOList">
        insert into productstorebatch (id, productstore_id, totalcount_minunit,BatchAttributeInfoNo,
        productiondate, location_id, location_name,
        locationCategory, subcategory, expiretime,
        batchtime, createtime, createuserId,
        lastupdatetime,InStockOrderNo, batchProperty)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR}, #{item.productStoreId,jdbcType=VARCHAR}, #{item.totalCount,jdbcType=DECIMAL},
            #{item.batchAttributeInfoNo,jdbcType=VARCHAR},
            #{item.productionDate,jdbcType=TIMESTAMP}, #{item.locationId,jdbcType=BIGINT},
            #{item.locationName,jdbcType=VARCHAR},
            #{item.locationCategory,jdbcType=TINYINT}, #{item.subcategory,jdbcType=TINYINT},
            #{item.expireTime,jdbcType=TIMESTAMP},
            #{item.batchTime,jdbcType=TIMESTAMP}, NOW(), #{item.createUserId,jdbcType=INTEGER},
            NOW(),#{item.inStockOrderNo,jdbcType=VARCHAR}, #{item.batchProperty,jdbcType=TINYINT}
            )
        </foreach>
    </select>
    <select id="selectTmpLocationInventoryBySubCategory" resultMap="BaseResultMap">
        SELECT
        ps.id,
        ps.productstore_id,
        ps.totalcount_minunit,
        ps.productiondate, -- 生产日期
        IFNULL(locL.id,ps.location_id) as location_id,
        IFNULL(locL.Name,ps.location_name) as location_name,
        IFNULL(locL.category,ps.locationCategory) as locationCategory,
        IFNULL(locL.subcategory,ps.subcategory) as subcategory,
        ps.expiretime, -- 过期日期
        ps.batchtime, -- 批次入库日期
        ps.createuserId,
        ps.BatchAttributeInfoNo,
        ifnull(pab.id,0) as adventId, ps.batchProperty, ps.businessType
        from productstorebatch ps
        left join productpromotionstorebatch pab on ps.BatchAttributeInfoNo = pab.BatchAttributeInfoNo and pab.IsDelete
        = 0
        inner join productstore s on s.id = ps.productstore_id
        inner JOIN location locL on locL.Warehouse_Id = s.Warehouse_Id and locL.id = ps.location_id
        left join location locArea on locL.area_id = locArea.id
        where ps.productstore_id = #{po.productStoreId}
        <if test="subcategory!=null">
            and (locL.subcategory = #{subcategory} or locArea.subcategory = #{subcategory})
        </if>
        <if test="exlist!=null and exlist.size()>0">
            and (
            locL.subcategory not in
            <foreach collection="exlist" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            and (
            locArea.id is null or locArea.subcategory not in
            <foreach collection="exlist" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
            )
        </if>
        <if test="po.locationId!=null">
            and ps.location_id = #{po.locationId}
        </if>
        <if test="po.productionDate!=null">
            and ps.productionDate = #{po.productionDate}
        </if>
        <if test="po.productionDate == null and po.ignoreNullProductionDate != null and po.ignoreNullProductionDate == false">
            and ps.productionDate is null
        </if>
        <if test="po.batchTime != null and po.storeAgeControlProduct != null and po.storeAgeControlProduct == true">
            and ps.batchtime = #{po.batchTime}
        </if>
        <if test="po.totalCount>0">
            and ps.BatchAttributeInfoNo = #{po.batchAttributeInfoNo}
            order by ps.productiondate desc
        </if>
        <if test="0>po.totalCount">
            order by ps.productiondate asc
        </if>
    </select>
    <select id="selectTmpLocationInventory" resultMap="BaseResultMap">
        SELECT
        ps.id,
        ps.productstore_id,
        ps.totalcount_minunit,
        ps.productiondate, -- 生产日期
        IFNULL(loc.id,ps.location_id) as location_id,
        IFNULL(loc.Name,ps.location_name) as location_name,
        IFNULL(loc.category,ps.locationCategory) as locationCategory,
        IFNULL(loc.subcategory,ps.subcategory) as subcategory,
        ps.expiretime, -- 过期日期
        ps.batchtime, -- 批次入库日期
        ps.createuserId,
        ps.BatchAttributeInfoNo,
        ifnull(pab.id,0) as adventId, ps.batchProperty, ps.businessType
        from productstorebatch ps
        left join productpromotionstorebatch pab on ps.BatchAttributeInfoNo = pab.BatchAttributeInfoNo and pab.IsDelete
        = 0
        inner join location loc on loc.id = ps.location_id
        inner join productstore s on s.id = ps.productstore_id and loc.warehouse_id = s.warehouse_id
        where productstore_id = #{po.productStoreId}
        <if test="po.locationId!=null">
            and ps.location_id = #{po.locationId}
        </if>
        <if test="po.locationIds!=null and po.locationIds.size()>0">
            and ps.location_id in
            <foreach collection="po.locationIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="po.productionDate!=null">
            and ps.productionDate = #{po.productionDate}
        </if>
        <if test="po.productionDate == null and po.ignoreNullProductionDate != null and po.ignoreNullProductionDate == false">
            and ps.productionDate is null
        </if>
        <if test="po.batchTime != null and po.storeAgeControlProduct != null and po.storeAgeControlProduct == true">
            and ps.batchtime = #{po.batchTime}
        </if>
        <if test="po.totalCount>0">
            and ps.BatchAttributeInfoNo = #{po.batchAttributeInfoNo}
            order by ps.productiondate desc
        </if>
        <if test="0>po.totalCount">
            order by ps.productiondate asc
        </if>
    </select>
    <select id="findWarehouseIdByStoreId" resultType="java.lang.Integer">
        select Warehouse_Id
        from productstore
        where id = #{storeId}
    </select>
    <select id="checkExistOtherProductInLocation" resultType="java.lang.String">
        select psb.location_id
        from productstorebatch psb
        where psb.location_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="productStoreId!=null">
            and psb.productstore_id != #{productStoreId}
        </if>
        GROUP BY psb.location_id
    </select>
    <select id="findProductStoreId"
            resultType="java.lang.String">
        SELECT
        ps.id
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where
        psku.ProductSku_Id = #{productSkuId}
        and psku.Source = #{source}
        and ps.Warehouse_Id =#{warehouseId}
        and ps.Channel =#{channel}
        <if test="secOwnerId==null">
            and ps.SecOwner_Id is null
        </if>
        <if test="secOwnerId!=null">
            and ps.SecOwner_Id = #{secOwnerId}
        </if>
    </select>
    <select id="getLocationCapacityPOs"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.LocationCapacityPO">
        select tmp.location_id,tmp.capacity,tmp.Area as areaName,tmp.area_id as areaId,tmp.category AS locationCategory
        ,ifnull(sum(tmp.totalcount_minunit/(case tmp.packageQuantity when null then 1 when 0 then 1 else
        tmp.packageQuantity end)),0) as count
        from
        (
        select DISTINCT loc.id as location_id, loc.Area,
        loc.area_id,loc.category
        ,sb.totalcount_minunit,sku.packageQuantity,sb.id as bid
        ,loc.LocationCapacity as capacity
        from location loc
        left join productstorebatch sb on loc.id = sb.location_id
        LEFT JOIN productstore s ON s.id = sb.productstore_id and loc.warehouse_id = s.warehouse_id
        LEFT JOIN productsku sku on sku.productspecification_id = s.productspecification_id and sku.City_Id = s.City_Id
        AND ((sku.Company_Id is null and s.Owner_Id is null) or (sku.Company_Id = s.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = s.secOwner_Id))
        where loc.id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ) tmp
        GROUP BY tmp.location_id,tmp.capacity
    </select>
    <select id="findProductStoreBatchByOwnerType"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchNoDTO">
        select psb.id as batchId,psb.productstore_id as productStoreId
        ,psb.productionDate,psb.batchTime,ps.ownerType, psb.batchProperty, psb.businessType
        from productstorebatch psb
        inner join productstore ps on ps.id = psb.productstore_id
        where psb.BatchAttributeInfoNo is null
        <if test="ownerType!=null">
            and ps.OwnerType = #{ownerType}
        </if>
        limit 10000
    </select>
    <update id="updateProductStoreBatchNo">
        update productstorebatch
        <trim prefix="set" suffixOverrides=",">
            lastupdatetime = NOW(),
            <trim prefix="BatchAttributeInfoNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.batchId} then #{item.batchNo}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.batchId}
        </foreach>
    </update>

    <update id="updateStoreInventoryNotAdd">
        update productstore
        <trim prefix="set" suffixOverrides=",">
            lastupdatetime = NOW(),
            <trim prefix="TotalCount_MinUnit =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then TotalCount_MinUnit + #{item.storeTotalCount}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.productStoreId}
        </foreach>
    </update>

    <select id="findBatchStoreBySkuId"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        SELECT
        psb.BatchAttributeinfoNo as batchAttributeInfoNo,
        ps.Id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        psku.Name as productSkuName,
        ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId,
        ps.OwnerType as ownerType,
        psb.totalcount_minunit as storeTotalCount,
        ps.TotalCount_MinUnit as unitTotolCount,
        ps.Channel as channel,
        psku.specificationName,
        psku.unitName,
        psku.packageName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id as secOwnerId,
        psb.location_id as locationId,
        psb.location_name as locationName,
        psb.locationCategory as locationCategory,
        psb.subcategory as locationSubcategory,
        psb.batchtime as batchTime,
        psb.expiretime as expireTime,
        psb.productiondate as productionDate,
        psb.id as storeBatchId,
        psku.SaleModel as saleModel,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.MonthOfShelfLife as monthOfShelfLife,
        psku.ShelfLifeUnit as shelfLifeUnit,
        psku.OwnerName,
        own.OwnerName as secOwnerName
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id =ps.id
        LEFT JOIN owner own on ps.SecOwner_Id is not null and own.id = ps.SecOwner_Id
        where psb.totalcount_minunit != 0
        <if test="so.warehouseId!=null and so.warehouseId!=''">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.productSkuId!=null and so.productSkuId!=''">
            AND psku.ProductSku_Id = #{so.productSkuId}
        </if>
        <if test="so.productSkuIdList != null and so.productSkuIdList.size() > 0">
            AND psku.ProductSku_Id in
            <foreach collection="so.productSkuIdList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="listBatchInventoryBySync"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventorySyncPO">
        SELECT
        ps.id as productStoreId,
        pl.productsku_id as productSkuId,
        pl.location_id as locationId,
        loc.name as locationName,
        loc.category as category,
        loc.subcategory as subcategory,
        ps.totalcount_minunit as totalCount,
        ps.ProductSpecification_Id as productSpecificationId,
        ps.Owner_id as ownerId,
        ps.secOwner_Id secOwnerId,
        ps.warehouse_Id warehouseId
        FROM productlocation pl
        INNER JOIN productsku sku on sku.productsku_id = pl.productsku_id
        INNER JOIN productstore ps ON ps.ProductSpecification_Id = sku.ProductSpecification_Id
        AND ps.City_Id = sku.City_Id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        LEFT JOIN Location loc ON loc.id = pl.location_id AND loc.warehouse_id = ps.warehouse_id
        WHERE
        ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND loc.Name is not null
        <if test="locationAreaList != null and locationAreaList.size() > 0">
            AND loc.Area in
            <foreach collection="locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="locationIdList != null and locationIdList.size() > 0">
            AND pl.location_id in
            <foreach collection="locationIdList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        order by pl.productsku_id,ps.id,pl.createtime desc
    </select>

    <select id="pageListBatchInventoryBySync"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventorySyncPO">
        SELECT
        ps.id as productStoreId,
        pl.productsku_id as productSkuId,
        pl.location_id as locationId,
        loc.name as locationName,
        loc.category as category,
        loc.subcategory as subcategory,
        ps.totalcount_minunit as totalCount,
        ps.ProductSpecification_Id as productSpecificationId,
        ps.Owner_id as ownerId,
        ps.secOwner_Id secOwnerId,
        ps.warehouse_Id warehouseId
        FROM productlocation pl
        INNER JOIN productsku sku on sku.productsku_id = pl.productsku_id
        INNER JOIN productstore ps ON ps.ProductSpecification_Id = sku.ProductSpecification_Id
        AND ps.City_Id = sku.City_Id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        LEFT JOIN Location loc ON loc.id = pl.location_id AND loc.warehouse_id = ps.warehouse_id
        WHERE
        ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        AND loc.Name is not null
        <if test="dto.locationAreaList != null and dto.locationAreaList.size() > 0">
            AND loc.Area in
            <foreach collection="dto.locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by pl.productsku_id,ps.id,pl.createtime desc
    </select>

    <select id="listNegativeBatchInventory"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO">
        SELECT
        psb.id,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.batchtime as batchTime, -- 批次入库日期
        psb.productiondate as productionDate, -- 生产日期
        psb.expiretime as expireTime, -- 过期日期
        psb.BatchAttributeInfoNo as batchAttributeInfoNo,
        psb.createuserId as createUserId,
        loc.id as locationId,
        loc.name as locationName,
        loc.category as locationCategory,
        loc.subcategory as subcategory,
        psku.packageQuantity as packageQuantity,
        psku.ProductSku_Id as productSkuId,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.Company_Id as ownerId,
        psku.Name as productName,
        psku.specificationName as specificationName,
        ps.warehouse_id as warehouseId, psb.batchProperty, psb.businessType
        from productstorebatch psb
        inner join productstore ps on psb.productstore_id =ps.id
        inner join productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id AND
        ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        left join location loc on loc.id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        LEFT JOIN Location locArea ON locArea.id = loc.area_id
        WHERE psb.totalcount_minunit &lt; 0
        AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="locationIdList != null and locationIdList.size() > 0">
            AND psb.location_id in
            <foreach collection="locationIdList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="locationAreaList != null and locationAreaList.size() > 0">
            AND (loc.Area in
            <foreach collection="locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            or psb.location_name in
            <foreach collection="locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="locationTypeList != null and locationTypeList.size() > 0">
            AND (locArea.subcategory in
            <foreach collection="locationTypeList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
            or loc.subcategory in
            <foreach collection="locationTypeList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
            )
        </if>
    </select>

    <select id="listAdjustBatchInventory"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventorySyncPO">
        select productstore_id as productStoreId,
        TotalCount_MinUnit as psTotalCount,
        pbCount as
        totalCount,
        (TotalCount_MinUnit - pbCount) as diffCount
        from (SELECT pb.productstore_id,
        ps.TotalCount_MinUnit,
        sum(pb.totalcount_minunit) pbCount
        FROM productstorebatch pb
        INNER JOIN productstore ps ON ps.id = pb.productstore_id
        AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="productStoreIds != null and productStoreIds.size() > 0">
            AND ps.id in
            <foreach collection="productStoreIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        GROUP BY pb.productstore_id,
        ps.TotalCount_MinUnit
        HAVING ps.TotalCount_MinUnit != sum(pb.totalcount_minunit)) a
        order by DiffCount desc
    </select>

    <select id="listTransferBatchInventory"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventorySyncPO">
        SELECT
        pb.id as productStoreBatchId,
        pb.productstore_id as productStoreId,
        pb.totalcount_minunit as totalCount
        FROM productstorebatch pb
        INNER JOIN productstore ps ON ps.id = pb.productstore_id
        LEFT JOIN Location loc ON loc.id = pb.location_id AND loc.warehouse_id = ps.warehouse_id
        WHERE pb.totalcount_minunit != 0
        AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="ownerId!=null and ownerId!=''">
            AND ps.Owner_Id = #{ownerId}
        </if>
        <if test="locationAreaList != null and locationAreaList.size() > 0">
            AND (loc.Area in
            <foreach collection="locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            or pb.location_name in
            <foreach collection="locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
    </select>

    <delete id="deleteBatchInventoryByStoreIds">
        delete from productstorebatch
        where productstore_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="getZeroBatchInventoryByWarehouseId"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventorySyncPO">
        SELECT psb.id                 as productStoreBatchId,
               psb.productstore_id    as productStoreId,
               psb.totalcount_minunit as totalCount
        from productstorebatch psb
                     inner join productstore ps
                on ps.id = psb.ProductStore_Id and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        where ps.TotalCount_MinUnit = 0
          and psb.TotalCount_MinUnit <![CDATA[<>]]> 0
    </select>

    <update id="clearZeroBatchInventoryByWarehouseId">
        update productstorebatch
        set TotalCount_MinUnit = 0
        where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.productStoreBatchId,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="findProductStoreBatchByLocationArea"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO">
        select psb.id, psb.productstore_id as productStoreId, psb.totalcount_minunit as totalCount, psb.productiondate
        as productionDate,
        psb.expiretime as expireTime, psb.batchtime as batchTime,
        psb.createuserId, psb.BatchAttributeInfoNo, psb.location_id as locationId,
        psb.location_name as locationName, psb.locationCategory, psb.subcategory,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.ProductSku_Id as productSkuId,ps.owner_id as ownerId,ps.secOwner_Id as secOwnerId
        , psb.batchProperty, psb.businessType
        from productstorebatch psb
        inner join productstore ps on ps.Id = psb.productstore_id
        inner join productsku sku on sku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        and sku.City_Id = ps.City_Id
        inner join location loc on loc.Id = psb.location_id AND loc.Warehouse_Id = ps.warehouse_id
        where ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="ownerId!=null and ownerId!=''">
            AND ps.Owner_Id = #{ownerId}
        </if>
        and (
        loc.Area in
        <foreach collection="locationArea" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        or
        loc.`Name` in
        <foreach collection="locationArea" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ) and psb.totalcount_minunit != 0
    </select>

    <select id="findPickingProductStoreBatchWithOutSku"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO">
        select psb.id, psb.productstore_id as productStoreId, psb.totalcount_minunit as totalCount, psb.productiondate
        as productionDate,
        psb.expiretime as expireTime, psb.batchtime as batchTime,
        psb.createuserId, psb.BatchAttributeInfoNo, psb.location_id as locationId,
        psb.location_name as locationName, psb.locationCategory, psb.subcategory,
        ps.ProductSpecification_Id as productSpecificationId,
        ps.owner_id as ownerId,ps.secOwner_Id as secOwnerId, psb.batchProperty, psb.businessType
        from productstorebatch psb
        inner join productstore ps on ps.Id = psb.productstore_id
        inner join location loc on loc.Id = psb.location_id AND loc.subcategory in (61,26,53,21,63)
        where ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="ownerId!=null and ownerId!=''">
            AND ps.Owner_Id = #{ownerId}
        </if>
        and psb.totalcount_minunit != 0
    </select>

    <delete id="deleteBatchInventoryByIds">
        delete from productstorebatch
        where id in
        <foreach collection="productStoreBatchIds" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="findStockAgeProductInventory"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO">
        select psb.BatchAttributeInfoNo, psb.id as storeBatchId, psb.productstore_id as productStoreId,
        sku.ProductSku_id as productSkuId, sku.Name as productSkuName, ps.Owner_Id as ownerId,
        psb.totalcount_minunit as storeTotalCount, ps.OwnerType, sku.specificationName, sku.packageName,
        sku.unitName, sku.packageQuantity, sku.Source, sku.secOwner_Id as secOwnerId,
        psb.batchtime as batchTime, skuconfig.ProductFeature, pif.StatisticsClassName as firstCategoryName,
        skuconfig.storageType,
        datediff(now(),psb.batchtime) + 1 as stockAge,
        <trim prefix="case" suffix="end as overdue,">
            <foreach collection="so" item="query" index="index">
                when sku.ProductSku_Id in
                <foreach collection="query.skuIds" item="skuId" open="(" separator="," close=")">
                    #{skuId,jdbcType=BIGINT}
                </foreach>
                then #{query.maxStockAge} - (datediff(now(),psb.batchtime) + 1)
            </foreach>
        </trim>
        ifnull(o.OwnerName, '易久批') as ownerName,
        psb.productiondate as productionDate,
        sku.MonthOfShelfLife as monthOfShelfLife,
        sku.ShelfLifeUnit as shelfLifeUnit, psb.batchProperty, psb.businessType
        from productstorebatch psb
        inner join productstore ps on ps.Id = psb.productstore_id
        inner join productsku sku on sku.ProductSpecification_Id = ps.ProductSpecification_Id
        and ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        and sku.City_Id = ps.City_Id
        left join productskuconfig skuconfig on skuconfig.Warehouse_id = ps.Warehouse_id
        and skuconfig.ProductSku_id = sku.ProductSku_id
        left join productinfocategory pif on pif.id = sku.ProductInfoCategory_Id
        left join owner o on o.id = sku.Company_Id
        where
        ps.Warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and psb.totalcount_minunit != 0
        and
        <foreach collection="so" item="query" separator="or" open="(" close=")">
            (
            sku.ProductSku_id in
            <foreach collection="query.skuIds" item="skuId" open="(" separator="," close=")">
                #{skuId,jdbcType=BIGINT}
            </foreach>
            <if test="query.stockAgeRange != null ">
                and datediff(now(),ifnull(psb.batchtime,now())) >= #{query.stockAgeRange}
            </if>
            <if test="query.overdueRange != null ">
                and datediff(now(),ifnull(psb.batchtime,now())) >= #{query.overdueRange}
            </if>
            )
        </foreach>
    </select>

    <select id="findStockAgeProductInventoryBySku"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO">
        select psb.BatchAttributeInfoNo, psb.id as storeBatchId, psb.productstore_id as productStoreId,
        sku.ProductSku_id as productSkuId, sku.Name as productSkuName, ps.Owner_Id as ownerId,
        psb.totalcount_minunit as storeTotalCount, ps.OwnerType, sku.specificationName, sku.packageName,
        sku.unitName, sku.packageQuantity, sku.Source, sku.secOwner_Id as secOwnerId,
        psb.batchtime as batchTime, skuconfig.ProductFeature, pif.StatisticsClassName as firstCategoryName,
        skuconfig.storageType,
        o.OwnerName as ownerName,
        psb.productiondate as productionDate,
        sku.MonthOfShelfLife as monthOfShelfLife,
        sku.ShelfLifeUnit as shelfLifeUnit, psb.batchProperty, psb.businessType
        from productstorebatch psb
        inner join productstore ps on ps.Id = psb.productstore_id
        inner join productsku sku on sku.ProductSpecification_Id = ps.ProductSpecification_Id
        and ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        and sku.City_Id = ps.City_Id
        left join productskuconfig skuconfig on skuconfig.Warehouse_id = ps.Warehouse_id
        and skuconfig.ProductSku_id = sku.ProductSku_id
        left join productinfocategory pif on pif.id = sku.ProductInfoCategory_Id
        left join owner o on o.id = sku.Company_Id
        where
        ps.Warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and psb.totalcount_minunit != 0
        and sku.ProductSku_id in
        <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
            #{skuId,jdbcType=BIGINT}
        </foreach>
    </select>

    <update id="updateBatchInventoryZeroByStoreIds">
        update productstorebatch
        set totalcount_minunit = 0
        where productstore_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="excludeStoreBatchIdList!=null and excludeStoreBatchIdList.size()>0">
            AND id not in
            <foreach collection="excludeStoreBatchIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="findBatchStoreBySpec"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO">
        select psb.id, psb.productstore_id as productStoreId, psb.totalcount_minunit as totalCount,
        psb.productiondate as productionDate, psb.expiretime as expireTime, psb.batchtime as batchTime,
        psb.BatchAttributeInfoNo as batchAttributeInfoNo, psb.location_id as locationId, psb.location_name as
        locationName,
        psb.locationCategory as locationCategory, psb.subcategory as subcategory,
        ps.ProductSpecification_Id as productSpecificationId, ps.Owner_Id as ownerId, psb.batchProperty,
        psb.businessType
        from productstorebatch psb
        inner join productstore ps on ps.Id = psb.productstore_id
        where ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and psb.totalcount_minunit > 0
        and
        <foreach collection="specList" item="item" open="(" close=")" separator="or">
            (
            ps.ProductSpecification_Id = #{item.productSpecificationId,jdbcType=BIGINT}
            <if test="item.ownerId == null">
                AND ps.Owner_Id is null
            </if>
            <if test="item.ownerId != null">
                AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}
            </if>
            )
        </foreach>
    </select>

    <select id="findProductionDate"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDateDTO">
        select
        ps.ProductSpecification_Id as productSpecificationId, ps.Owner_Id as ownerId, ps.SecOwner_Id as secOwnerId,
        psb.productiondate as productionDate,
        ps.Warehouse_Id as warehouseId, ps.City_Id as orgId, psb.totalcount_minunit as batchStoreCount
        from productstorebatch psb
        inner join productstore ps on ps.Id = psb.productstore_id
        where
        psb.totalcount_minunit > 0
        and psb.productiondate is not null
        and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and
        <foreach collection="productBasicList" item="item" open="(" close=")" separator="or">
            (
            ps.ProductSpecification_Id = #{item.productSpecificationId,jdbcType=BIGINT}
            <if test="item.ownerId == null">
                AND ps.Owner_Id is null
            </if>
            <if test="item.ownerId != null">
                AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}
            </if>
            )
        </foreach>
    </select>

    <select id="findProductionDateList"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDateDTO">
        select
        ps.ProductSpecification_Id as productSpecificationId, ps.Owner_Id as ownerId, ps.SecOwner_Id as secOwnerId,
        psb.productiondate as productionDate,
        ps.Warehouse_Id as warehouseId, ps.City_Id as orgId, psb.totalcount_minunit as batchStoreCount,psb.subcategory
        as locationSubcategory
        from productstorebatch psb
        inner join productstore ps on ps.Id = psb.productstore_id
        where
        psb.totalcount_minunit > 0
        and psb.productiondate is not null
        and
        <foreach collection="productBasicList" item="item" open="(" close=")" separator="or">
            (
            ps.Warehouse_Id = #{item.warehouseId,jdbcType=INTEGER}
            and ps.ProductSpecification_Id = #{item.productSpecificationId,jdbcType=BIGINT}
            <if test="item.ownerId == null">
                AND ps.Owner_Id is null
            </if>
            <if test="item.ownerId != null">
                AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}
            </if>
            )
        </foreach>
    </select>

    <!--查询批次库存信息(根据货位或产品名称获取批次库存，支持skuIds)-->
    <select id="findStoreAgeRefBatchInventoryList"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        SELECT
        psb.BatchAttributeinfoNo as batchAttributeInfoNo,
        psku.ProductSku_Id as productSkuId,
        psb.batchtime as batchTime,
        psb.expiretime as expireTime,
        psb.productiondate as productionDate,
        max(psb.id) as storeBatchId
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id =ps.id
        LEFT JOIN Location loc on loc.id= psb.location_id and loc.warehouse_id = ps.warehouse_id
        LEFT JOIN owner own on ps.SecOwner_Id is not null and own.id = ps.SecOwner_Id
        LEFT JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id
        and ps.Warehouse_Id = pc.Warehouse_Id
        where ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        <if test="so.productSkuId!=null and so.productSkuId!=''">
            AND psku.ProductSku_Id = #{so.productSkuId}
        </if>
        <if test="so.skuIds!=null and so.skuIds.size()>0">
            and psku.ProductSku_Id in
            <foreach collection="so.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.warehouseId!=null and so.warehouseId!=''">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.locationName!=null and so.locationName!=''">
            AND psb.location_name like concat('%',#{so.locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.locationFullName!=null and so.locationFullName!=''">
            AND psb.location_name = #{so.locationFullName,jdbcType=VARCHAR}
        </if>
        <if test="so.subCategory !=null and so.subCategory !=''">
            AND psb.subCategory = #{so.subCategory,jdbcType=INTEGER}
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.specIdAndOwnerIdList!=null and so.specIdAndOwnerIdList.size()>0">
            AND
            <foreach collection="so.specIdAndOwnerIdList" item="item" index="index" separator="or" open="(" close=")">
                (
                ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
                <if test="item.ownerId == null">
                    and ps.Owner_Id is null
                </if>
                <if test="item.ownerId != null">
                    and ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}
                </if>
                )
            </foreach>
        </if>
        group by psku.ProductSku_Id, psb.BatchAttributeinfoNo, psb.batchtime, psb.productiondate, psb.expiretime
        order by psb.productionDate desc
    </select>

    <update id="updateProductStoreBatchNoAndDate">
        update productstorebatch
        <trim prefix="set" suffixOverrides=",">
            lastupdatetime = NOW(),
            <trim prefix="BatchAttributeInfoNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.storeBatchId} then #{item.batchAttributeInfoNo}
                </foreach>
            </trim>
            <trim prefix="productiondate =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.storeBatchId} then #{item.productionDate}
                </foreach>
            </trim>
            <trim prefix="expireTime =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.storeBatchId} then #{item.expireTime}
                </foreach>
            </trim>
            <trim prefix="batchtime =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.storeBatchId} then #{item.batchTime}
                </foreach>
            </trim>
            <trim prefix="lastupdateuserId =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.storeBatchId} then #{item.lastUpdateUserId}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.storeBatchId}
        </foreach>
    </update>

    <select id="listWarehouseByInventoryNegative"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryNegativeDTO">
        select DISTINCT o.Province as province,
                        o.OrgName  as city,
                        o.id       as cityId,
                        w.Name     as warehouse,
                        w.id       as warehouseId
        from productstorebatch psb
                     inner join productstore ps
                     inner join org o
                     inner join warehouse w
                on ps.Id = psb.productstore_id and ps.City_Id = o.Id and ps.Warehouse_Id = w.Id
        where <![CDATA[ psb.totalcount_minunit < 0 ]]>
        group by o.id, w.id
    </select>

    <insert id="batchAdd">
        INSERT INTO productstorebatch (id,productstore_id,totalcount_minunit,batchtime,
        createtime,lastupdatetime,expiretime,productiondate,BatchAttributeInfoNo,
        location_id,location_name,subcategory,locationCategory)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},#{item.productStoreId},#{item.totalCount},#{item.batchTime},NOW(),NOW(),#{item.expireTime},#{item.productionDate},
            #{item.batchAttributeInfoNo},
            #{item.locationId},#{item.locationName},#{item.subcategory},#{item.locationCategory,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <select id="findProductionDateFromStoreBatch"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.product.BatchProductionDateDTO">
        SELECT
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        MAX(psku.Name) as productName,
        ps.ProductSpecification_Id as productSpecificationId,
        ps.Owner_Id as ownerId,
        ps.SecOwner_Id as secOwnerId,
        ps.OwnerType as ownerType,
        psb.productiondate as productionDate
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id = ps.id
        where ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND
        <foreach collection="specList" item="item" open="(" close=")" separator="or">
            (
            ps.ProductSpecification_Id = #{item.productSpecificationId,jdbcType=BIGINT}
            <if test="item.ownerId == null">
                AND ps.Owner_Id is null
            </if>
            <if test="item.ownerId != null">
                AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}
            </if>
            <if test="item.secOwnerId == null">
                AND ps.SecOwner_Id is null
            </if>
            <if test="item.secOwnerId != null">
                AND ps.SecOwner_Id = #{item.secOwnerId,jdbcType=BIGINT}
            </if>
            )
        </foreach>
        and psb.productiondate is not NULL
        <if test="cityId != null">
            and ps.City_Id = #{cityId,jdbcType=INTEGER}
        </if>
        GROUP BY ps.City_Id, ps.Warehouse_Id, ps.ProductSpecification_Id,
        ps.Owner_Id, ps.SecOwner_Id, ps.OwnerType, psb.productiondate
    </select>

    <!--根据货位、批次信息查询产品SKU基础信息-->
    <select id="findProductBaseInfoFromBatchInventory"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        SELECT
        psku.ProductSku_Id as productSkuId,
        CONCAT(case psku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, psku.NAME) AS
        productSkuName,
        psku.specificationName,
        psku.unitName,
        psku.packageName,
        psku.packageQuantity,
        psku.Source as source,
        psku.SaleModel as saleModel,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.ownerName,
        psku.Company_Id as ownerId,
        psku.secOwner_Id as secOwnerId,
        psku.productBrand,
        psku.ProductState as productState,
        IFNULL(info.MonthOfShelfLife, psku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(info.ShelfLifeUnit, psku.ShelfLifeUnit) as shelfLifeUnit,
        info.ShelfLifeLongTime
        FROM productsku psku
        INNER JOIN productinfo info on psku.ProductInfo_Id = info.Id
        INNER JOIN
        (
        SELECT
        sku.City_Id, sku.ProductSku_Id, SUM(ps.TotalCount_MinUnit) as TotalCount_MinUnit
        FROM
        productstore ps
        INNER JOIN productsku sku ON ps.ProductSpecification_Id = sku.ProductSpecification_Id
        AND ps.City_Id = sku.City_Id
        AND ((sku.Company_Id IS NULL AND ps.Owner_Id IS NULL) OR (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id IS NULL) OR (sku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb ON psb.productstore_id = ps.id
        LEFT JOIN Location loc ON loc.id = psb.location_id AND loc.warehouse_id = ps.warehouse_id
        <if test="so.limitSku == null or so.limitSku != 1">
            <if test="so.warehouseAllocationType == null or so.warehouseAllocationType == ''">
                LEFT JOIN productskuconfig pc on pc.ProductSku_Id = sku.ProductSku_Id and ps.Warehouse_Id =
                pc.Warehouse_Id
            </if>

            <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
                inner JOIN productskuconfig pc on pc.ProductSku_Id = sku.ProductSku_Id and ps.Warehouse_Id =
                pc.Warehouse_Id
                and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or
                pc.StorageAttribute is null)
            </if>
        </if>
        <if test="so.limitSku != null and so.limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = sku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
            <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
                and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or
                pc.StorageAttribute is null)
            </if>
        </if>
        WHERE ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        <if test="so.productSkuId!=null and so.productSkuId!=''">
            AND sku.ProductSku_Id = #{so.productSkuId}
        </if>
        <if test="so.warehouseId!=null and so.warehouseId!=''">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.cityId!=null and so.cityId!=''">
            AND ps.City_Id = #{so.cityId}
        </if>
        <if test="so.locationName!=null and so.locationName!=''">
            AND psb.location_name like concat(#{so.locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.locationFullName!=null and so.locationFullName!=''">
            AND psb.location_name = #{so.locationFullName,jdbcType=VARCHAR}
        </if>
        <if test="so.locationIds != null and so.locationIds.size() > 0">
            AND psb.location_id in
            <foreach collection="so.locationIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND sku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.source != null">
            AND sku.Source = #{so.source,jdbcType=INTEGER}
        </if>
        <if test="so.subCategoryList!=null and so.subCategoryList.size()>0">
            AND loc.subcategory in
            <foreach collection="so.subCategoryList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="so.skuIds!=null and so.skuIds.size()>0">
            and sku.productsku_id in
            <foreach collection="so.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.storeType != null and so.storeType == 1">
            and ps.TotalCount_MinUnit > 0
        </if>
        <if test="so.storeType != null and so.storeType == 2">
            and ps.TotalCount_MinUnit = 0
        </if>
        <if test="so.ownerType != null">
            and ps.OwnerType = #{so.ownerType,jdbcType=INTEGER}
        </if>
        <if test="so.deleted != null and so.deleted.size > 0">
            and sku.IsDelete in
            <foreach item="item" collection="so.deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        GROUP BY sku.City_Id, sku.ProductSku_Id
        ) tmp ON psku.City_Id = tmp.City_Id AND psku.ProductSku_Id = tmp.ProductSku_Id
        ORDER BY tmp.TotalCount_MinUnit DESC, psku.ProductSku_Id
    </select>

    <select id="findProductionDatePriceBySkuIds"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDatePriceDTO">
        select
        ps.City_Id as orgId, ps.Warehouse_Id as warehouseId, psku.ProductSku_Id as skuId,
        psb.productiondate as productionDate, pskuc.CostPrice as costPrice, psku.unitName as priceUnit,
        psb.totalcount_minunit as batchStoreCount
        from productstorebatch psb
        inner join productstore ps on ps.Id = psb.productstore_id
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        left join productskuconfig pskuc on pskuc.ProductSku_Id = psku.ProductSku_Id and pskuc.Warehouse_Id =
        ps.Warehouse_Id
        where
        psb.totalcount_minunit > 0
        and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and psku.ProductSku_Id in
        <foreach collection="skuIds" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listProductStoreBatch" resultMap="BaseResultMap">
        SELECT
        psb.id,
        psb.productstore_id,psb.totalcount_minunit,psb.productiondate,psb.expiretime,psb.batchtime,psb.BatchAttributeInfoNo,
        psb.location_id,psb.location_name,psb.locationCategory,psb.subcategory,ps.OwnerType, psb.batchProperty,
        psb.businessType
        from productstorebatch psb inner join productstore ps on psb.productstore_id = ps.id
        where psb.totalcount_minunit &gt; 0
        <if test="so.startProductionDate != null">
            and psb.productiondate > #{so.startProductionDate,jdbcType=TIMESTAMP}
        </if>
        <if test="so.endProductionDate != null">
            and psb.productiondate <![CDATA[ < ]]> #{so.endProductionDate,jdbcType=TIMESTAMP}
        </if>
        <if test="so.warehouseId != null">
            and ps.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
        </if>
    </select>

    <!--    <select id="listProductStoreBatchBySku" resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO">-->
    <!--        SELECT-->
    <!--                psku.ProductSku_Id as productSkuId,-->
    <!--                psb.productiondate as productionDate,-->
    <!--                psb.location_id as locationId,-->
    <!--                psb.location_name as locationName,-->
    <!--                psb.totalcount_minunit as totalCount-->
    <!--        from productstorebatch psb-->
    <!--        inner join productstore ps on psb.productstore_id =ps.id-->
    <!--        inner join productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id AND ps.City_Id=psku.City_Id-->
    <!--            AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))-->
    <!--            AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))-->
    <!--        WHERE psb.totalcount_minunit > 0-->
    <!--            AND psb.productiondate is not null-->
    <!--            AND ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}-->
    <!--        <if test="dto.productSkuIdList != null">-->
    <!--            AND psku.ProductSku_Id in-->
    <!--            <foreach collection="dto.productSkuIdList" open="(" item="skuId" separator="," close=")">-->
    <!--                #{skuId,jdbcType=BIGINT}-->
    <!--            </foreach>-->
    <!--        </if>-->
    <!--        <if test="dto.locationId!=null">AND psb.location_id = #{dto.locationId,jdbcType=BIGINT}</if>-->
    <!--        <if test="dto.channel!=null">AND ps.channel = #{dto.channel,jdbcType=TINYINT}</if>-->
    <!--        <if test="dto.source!=null">AND psku.Source = #{dto.source,jdbcType=TINYINT}</if>-->
    <!--        <if test="dto.productName!=null and dto.productName!=''">-->
    <!--            AND psku.Name like concat('%',#{dto.productName,jdbcType=VARCHAR},'%')-->
    <!--        </if>-->
    <!--    </select>-->

    <sql id="findBatchInventoryInfoSql">
        SELECT
        psb.BatchAttributeinfoNo as batchAttributeInfoNo,
        ps.Id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        CONCAT(case psku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, psku.NAME) AS
        productSkuName,
        ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId,
        ps.OwnerType as ownerType,
        psb.totalcount_minunit as storeTotalCount,
        ps.Channel as channel,
        psku.specificationName,
        psku.unitName,
        psku.packageName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id as secOwnerId,
        loc.id as locationId,
        loc.name as locationName,
        loc.category as locationCategory,
        loc.subcategory as locationSubcategory,
        loc.area as area,
        loc.sequence AS locationSequence,
        area.subcategory as areaSubcategory,
        psb.batchtime as batchTime,
        psb.expiretime as expireTime,
        psb.productiondate as productionDate,
        psb.id as storeBatchId,
        psb.createtime as createTime,
        psb.InStockOrderNo as inStockOrderNo,
        psku.SaleModel as saleModel,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.OwnerName,
        own.OwnerName as secOwnerName,
        pc.ProductFeature,
        pc.MaxReplenishment,
        pc.MinReplenishment,
        pc.storageAttribute,

        psku.productBrand,
        IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit) as shelfLifeUnit,
        psku.ProductState as productState,
        ps.TotalCount_MinUnit as unitTotolCount,
        DATEDIFF(now(), psb.createtime) as stockAge,
        <![CDATA[
        case when psb.productionDate is null or (IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) is null or IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) <= 0) then '正常'
        when DATEDIFF(now(), psb.productionDate) <= (case IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit) when 1 then
        IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife)*365
        when 2 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife)*30
        when 0 then IFNULL(pi.MonthOfShelfLife, psku.MonthOfShelfLife)* 30
        else IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife)
        end * 2/3) then '正常'
        when DATEDIFF(now(), psb.productionDate) <= (case IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit) when 1 then
        IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife)*365
        when 2 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife)*30
        when 0 then IFNULL(pi.MonthOfShelfLife, psku.MonthOfShelfLife)* 30
        else IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife)
        end)
        and DATEDIFF(now(), psb.productionDate) > (case IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit) when 1 then
        IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife)*365
        when 2 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife)*30
        when 0 then IFNULL(pi.MonthOfShelfLife, psku.MonthOfShelfLife)* 30
        else IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife)
        end * 2/3) then '临期'
        else '过期'
        end
        ]]> AS shelfLifeTypeName
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id =ps.id
        LEFT JOIN productinfo pi on pi.id = psku.ProductInfo_Id
        LEFT JOIN Location loc on loc.id= psb.location_id and loc.warehouse_id = ps.warehouse_id
        <if test="so.warehouseId!=null">
            AND loc.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.cityId!=null">
            AND loc.City_Id = #{so.cityId}
        </if>
        LEFT JOIN Location area on loc.area_id = area.id
        LEFT JOIN owner own on ps.SecOwner_Id is not null and own.id = ps.SecOwner_Id

        <if test="so.limitSku == null or so.limitSku != 1">
            <if test="so.warehouseAllocationType == null or so.warehouseAllocationType == ''">
                LEFT JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
            </if>

            <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
                INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
                and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or pc.StorageAttribute is null)
            </if>
        </if>

        <if test="so.limitSku != null and so.limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
            <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
                and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or pc.StorageAttribute is null)
            </if>
        </if>
        where 1=1
    </sql>
    <sql id="findBatchInventoryInfoGroupByProductionDateSql">
        SELECT
        psb.BatchAttributeinfoNo as batchAttributeInfoNo,
        MAX(ps.Id) as productStoreId,
        psku.ProductSku_Id as productSkuId,
        CONCAT(case MAX(psku.ProductState) when 0 then '[下架]' when 1 then '[作废]' else '' end, MAX(psku.NAME)) AS
        productSkuName,
        ps.Warehouse_Id as warehouseId,
        MAX(ps.Owner_Id) as ownerId,
        MAX(ps.OwnerType) as ownerType,
        SUM(psb.totalcount_minunit) as storeTotalCount,
        MAX(ps.Channel) as channel,
        MAX(psku.specificationName) as specificationName,
        MAX(psku.unitName) as unitName,
        MAX(psku.packageName) as packageName,
        MAX(psku.packageQuantity) as packageQuantity,
        MAX(psku.Source) as Source,
        MAX(ps.SecOwner_Id) as secOwnerId,
        MAX(loc.id) as locationId,
        MAX(loc.name) as locationName,
        MAX(loc.category) as locationCategory,
        MAX(loc.subcategory) as locationSubcategory,
        MAX(loc.area) as area,
        MAX(loc.sequence) AS locationSequence,
        MAX(area.subcategory) as areaSubcategory,
        MAX(psb.expiretime) as expireTime,
        MIN(psb.productiondate) as productionDate,
        MAX(psb.id) as storeBatchId,
        MAX(psku.SaleModel) as saleModel,
        MAX(psku.ProductSpecification_Id) as productSpecificationId,
        MAX(psku.OwnerName) as OwnerName,
        MAX(own.OwnerName) as secOwnerName,
        MAX(pc.ProductFeature) as ProductFeature,
        MAX(pc.storageAttribute) as storageAttribute,

        MAX(psku.productBrand) as productBrand,
        IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife)) as monthOfShelfLife,
        IFNULL(MIN(pi.ShelfLifeUnit),MIN(psku.ShelfLifeUnit)) as shelfLifeUnit,
        MAX(psku.ProductState) as productState,
        SUM(ps.TotalCount_MinUnit) as unitTotolCount,
        <![CDATA[
        case  when MIN(psb.productiondate) is null or IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife)) is null then '正常'
        when DATEDIFF(now(), MIN(psb.productionDate)) <= (case IFNULL(MIN(pi.ShelfLifeUnit),MIN(psku.ShelfLifeUnit)) when 1 then IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))*365
        when 2 then IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))*30
        when 0 then IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))*30
        else IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))
        end * 2/3) then '正常'
        when DATEDIFF(now(), MIN(psb.productionDate)) <= (case IFNULL(MIN(pi.ShelfLifeUnit),MIN(psku.ShelfLifeUnit)) when 1 then IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))*365
        when 2 then IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))*30
        when 0 then IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))*30
        else IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))
        end)
        and DATEDIFF(now(), MIN(psb.productionDate)) > (case IFNULL(MIN(pi.ShelfLifeUnit),MIN(psku.ShelfLifeUnit)) when 1 then IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))*365
        when 2 then IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))*30
        when 0 then IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))*30
        else IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife))
        end * 2/3) then '临期'
        else '过期'
        end
        ]]> AS shelfLifeTypeName
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id =ps.id
        LEFT JOIN productinfo pi on pi.id = psku.ProductInfo_Id
        LEFT JOIN Location loc on loc.id= psb.location_id and loc.warehouse_id = ps.warehouse_id
        <if test="so.warehouseId!=null">
            AND loc.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.cityId!=null">
            AND loc.City_Id = #{so.cityId}
        </if>
        LEFT JOIN Location area on loc.area_id = area.id
        LEFT JOIN owner own on ps.SecOwner_Id is not null and own.id = ps.SecOwner_Id

        <if test="so.limitSku == null or so.limitSku != 1">
            <if test="so.warehouseAllocationType == null or so.warehouseAllocationType == ''">
                LEFT JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
            </if>

            <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
                INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
                and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or pc.StorageAttribute is null)
            </if>
        </if>

        <if test="so.limitSku != null and so.limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
            <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
                and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or pc.StorageAttribute is null)
            </if>
        </if>
        where 1=1
    </sql>
    <sql id="findBatchInventoryInfoGroupByInStorageDateSql">
        SELECT
        psb.BatchAttributeinfoNo as batchAttributeInfoNo,
        MAX(ps.Id) as productStoreId,
        psku.ProductSku_Id as productSkuId,
        CONCAT(case MAX(psku.ProductState) when 0 then '[下架]' when 1 then '[作废]' else '' end, MAX(psku.NAME)) AS
        productSkuName,
        ps.Warehouse_Id as warehouseId,
        MAX(ps.Owner_Id) as ownerId,
        MAX(ps.OwnerType) as ownerType,
        SUM(psb.totalcount_minunit) as storeTotalCount,
        MAX(ps.Channel) as channel,
        MAX(psku.specificationName) as specificationName,
        MAX(psku.unitName) as unitName,
        MAX(psku.packageName) as packageName,
        MAX(psku.packageQuantity) as packageQuantity,
        MAX(psku.Source) as Source,
        MAX(ps.SecOwner_Id) as secOwnerId,
        MAX(loc.id) as locationId,
        MAX(loc.name) as locationName,
        MAX(loc.category) as locationCategory,
        MAX(loc.subcategory) as locationSubcategory,
        MAX(loc.area) as area,
        MAX(loc.sequence) AS locationSequence,
        MAX(area.subcategory) as areaSubcategory,
        MIN(psb.batchtime) as batchTime,
        MAX(psb.expiretime) as expireTime,
        MIN(psb.productiondate) as productionDate,
        MAX(psb.id) as storeBatchId,
        psb.createtime as createTime,
        MAX(psb.InStockOrderNo) as inStockOrderNo,
        MAX(psku.SaleModel) as saleModel,
        MAX(psku.ProductSpecification_Id) as productSpecificationId,
        MAX(psku.OwnerName) as OwnerName,
        MAX(own.OwnerName) as secOwnerName,
        MAX(pc.ProductFeature) as ProductFeature,
        MAX(pc.storageAttribute) as storageAttribute,

        MAX(psku.productBrand) as productBrand,
        IFNULL(MIN(pi.MonthOfShelfLife),MIN(psku.MonthOfShelfLife)) as monthOfShelfLife,
        IFNULL(MIN(pi.ShelfLifeUnit),MIN(psku.ShelfLifeUnit)) as shelfLifeUnit,
        MAX(psku.ProductState) as productState,
        SUM(ps.TotalCount_MinUnit) as unitTotolCount,
        DATEDIFF(now(), psb.createtime) as stockAge
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id =ps.id
        LEFT JOIN productinfo pi on pi.id = psku.ProductInfo_Id
        LEFT JOIN Location loc on loc.id= psb.location_id and loc.warehouse_id = ps.warehouse_id
        <if test="so.warehouseId!=null">
            AND loc.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.cityId!=null">
            AND loc.City_Id = #{so.cityId}
        </if>
        LEFT JOIN Location area on loc.area_id = area.id
        LEFT JOIN owner own on ps.SecOwner_Id is not null and own.id = ps.SecOwner_Id

        <if test="so.limitSku == null or so.limitSku != 1">
            <if test="so.warehouseAllocationType == null or so.warehouseAllocationType == ''">
                LEFT JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
            </if>

            <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
                INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
                and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or pc.StorageAttribute is null)
            </if>
        </if>

        <if test="so.limitSku != null and so.limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
            <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
                and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or pc.StorageAttribute is null)
            </if>
        </if>
        where 1=1
    </sql>

    <!--查询批次库存信息-->
    <select id="findBatchInventoryInfo"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        <if test="so.queryType == null or so.queryType == 0">
            <include refid="findBatchInventoryInfoSql">
            </include>
        </if>
        <if test="so.queryType != null and so.queryType == 1">
            <include refid="findBatchInventoryInfoGroupByProductionDateSql">
            </include>
        </if>
        <if test="so.queryType != null and so.queryType == 2">
            <include refid="findBatchInventoryInfoGroupByInStorageDateSql">
            </include>
        </if>
        <if test="so.showAll == null">
            AND ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        </if>
        <if test="so.showAll != null and so.showAll == -1">
            AND <![CDATA[ psb.totalcount_minunit < 0 ]]>
        </if>
        <if test="so.channel!=null">
            AND ps.Channel=#{so.channel,jdbcType=INTEGER}
        </if>
        <if test="so.source!=null">
            AND psku.Source=#{so.source,jdbcType=INTEGER}
        </if>
        <if test="so.productSkuId!=null">
            AND psku.ProductSku_Id=#{so.productSkuId,jdbcType=BIGINT}
        </if>
        <if test="so.secOwnerId!=null">
            AND ps.SecOwner_Id=#{so.secOwnerId,jdbcType=BIGINT}
        </if>

        <if test="so.warehouseId!=null">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.ownerType!=null">
            AND ps.OwnerType=#{so.ownerType,jdbcType=INTEGER}
        </if>
        <if test="so.locationId!=null">
            AND psb.location_id=#{so.locationId,jdbcType=BIGINT}
        </if>

        <if test="so.locationName!=null and so.locationName!=''">
            AND psb.location_name like
            concat(#{so.locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.locationCategory!=null">
            AND loc.category = #{so.locationCategory}
        </if>
        <if test="so.subCategory!=null">
            AND loc.subcategory = #{so.subCategory}
        </if>
        <if test="so.subCategoryList!=null and so.subCategoryList.size()>0">
            AND loc.subcategory in
            <foreach collection="so.subCategoryList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="so.supplierId!=null">
            and ps.Owner_Id=#{so.supplierId,jdbcType=BIGINT}
            and ps.OwnerType = 1
        </if>
        <if test="so.agencyId!=null">
            and ps.Owner_Id=#{so.agencyId,jdbcType=BIGINT}
            and ps.OwnerType = 2
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.batchAttributeInfoNo!=null and so.batchAttributeInfoNo!=''">
            AND psb.BatchAttributeinfoNo = #{so.batchAttributeInfoNo,jdbcType=VARCHAR}
        </if>
        <if test="so.deleted != null and so.deleted.size > 0">
            and psku.IsDelete in
            <foreach item="item" collection="so.deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="so.skuIds!=null and so.skuIds.size()>0">
            and psku.productsku_id in
            <foreach collection="so.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.productStoreId != null">
            and psb.productstore_id = #{so.productStoreId,jdbcType=VARCHAR}
        </if>

        <if test="so.startTime != null">
            and <![CDATA[ psb.createtime >= #{so.startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="so.endTime != null">
            and <![CDATA[ psb.createtime <= #{so.endTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="so.startStockAgeTime != null">
            and <![CDATA[ psb.createtime <= #{so.startStockAgeTime,jdbcType=TIMESTAMP} ]]>
            <!--            and psb.createtime &lt;=date_format(#{so.startStockAgeTime},'%Y-%m-%d %H:%i:%S')-->
        </if>
        <if test="so.endStockAgeTime != null">
            and <![CDATA[ psb.createtime >= #{so.endStockAgeTime,jdbcType=TIMESTAMP} ]]>
            <!--            and psb.createtime &gt;=date_format(#{so.endStockAgeTime},'%Y-%m-%d %H:%i:%S')-->
        </if>
        <choose>
            <when test="so.shelfLifeType != null and so.shelfLifeType == 0">
                and <![CDATA[
                case when psb.productionDate is not null and IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) is not null
                and IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) > 0
                then DATEDIFF(now(), psb.productionDate) <= case IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit)
                when 1 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 365
                when 2 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 30
                when 0 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 30
                else IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) end * 2 / 3
                else 1=1
                end ]]>
            </when>
            <when test="so.shelfLifeType != null and so.shelfLifeType == 1">
                and <![CDATA[
                case when psb.productionDate is not null and IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) is not null
                and IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) > 0
                then DATEDIFF(now(), IFNULL(psb.productionDate,now())) <=
                case IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit)
                when 1 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 365
                when 2 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 30
                when 0 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 30
                else IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) end
                and DATEDIFF(now(), IFNULL(psb.productionDate,now())) >
                case IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit)
                when 1 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 365
                when 2 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 30
                when 0 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 30
                else IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) end * 2/3
                else 1 != 1
                end ]]>
            </when>
            <when test="so.shelfLifeType != null and so.shelfLifeType == 2">
                and <![CDATA[
                case when psb.productionDate is not null and IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) is not null
                and IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) > 0
                then DATEDIFF(now(), IFNULL(psb.productionDate,now())) >
                case IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit)
                when 1 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 365
                when 2 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 30
                when 0 then IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) * 30
                else IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) end
                else 1 != 1
                end ]]>
            </when>
        </choose>
        <if test="so.queryType == null or so.queryType == 0">
            <choose>
                <when test="so.orderByBatchTime != null and so.orderByBatchTime == true">
                    order by psb.batchTime desc
                </when>
                <otherwise>
                    order by loc.subcategory asc,psb.productiondate desc,psb.totalcount_minunit desc
                </otherwise>
            </choose>
        </if>
        <if test="so.queryType != null and so.queryType == 1">
            group by ps.Warehouse_Id,psb.BatchAttributeinfoNo,psku.ProductSku_Id,psb.productiondate
            order by psb.productiondate desc
        </if>
        <if test="so.queryType != null and so.queryType == 2">
            group by ps.Warehouse_Id,psb.BatchAttributeinfoNo,psku.ProductSku_Id,psb.createtime
            order by psb.createtime desc
        </if>
    </select>
    <select id="pageListProductStoreBatch"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO">
        SELECT
        psb.id,
        psb.productstore_id as productStoreId,
        psb.totalcount_minunit as totalCount,
        psb.batchtime as batchTime, -- 批次入库日期
        psb.productiondate as productionDate, -- 生产日期
        psb.expiretime as expireTime, -- 过期日期
        psb.BatchAttributeInfoNo as batchAttributeInfoNo,
        psb.createuserId as createUserId,
        loc.id as locationId,
        loc.name as locationName,
        loc.category as locationCategory,
        loc.subcategory as subcategory,
        psku.packageQuantity as packageQuantity,
        psku.ProductSku_Id as productSkuId,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.Company_Id as ownerId,
        psku.Name as productName,
        psku.specificationName as specificationName,
        ps.warehouse_id as warehouseId, psb.batchProperty, psb.businessType
        from productstorebatch psb
        inner join productstore ps on psb.productstore_id =ps.id
        inner join productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id AND
        ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        left join location loc on loc.id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where psb.totalcount_minunit != 0 and psb.productstore_id IN
        <foreach collection="storeBatchQueryDTO.storeIdList" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findProductLocationBatchInventoryList"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        SELECT
        psb.BatchAttributeinfoNo as batchAttributeInfoNo,
        ps.Id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        CONCAT(case psku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, psku.NAME) AS
        productSkuName,
        ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId,
        ps.OwnerType as ownerType,
        psb.totalcount_minunit as storeTotalCount,
        ps.Channel as channel,
        psku.specificationName,
        psku.unitName,
        psku.packageName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id as secOwnerId,
        loc.id as locationId,
        loc.name as locationName,
        loc.category as locationCategory,
        loc.subcategory as locationSubcategory,
        loc.area as area,
        loc.sequence AS locationSequence,
        psb.batchtime as batchTime,
        psb.expiretime as expireTime,
        psb.productiondate as productionDate,
        psb.id as storeBatchId,
        psku.SaleModel as saleModel,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.OwnerName,
        psku.productBrand,
        IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit) as shelfLifeUnit,
        psku.ProductState as productState,
        ps.TotalCount_MinUnit as unitTotolCount
        from sc_product.location loc
        inner join sc_product.productlocation p on loc.Id = p.Location_Id
        inner join sc_product.productsku psku on psku.ProductSku_Id = p.ProductSku_Id
        inner join sc_product.productstore ps on ps.ProductSpecification_Id = psku.ProductSpecification_Id
        and ps.City_Id = psku.City_Id
        and ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        and ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        <if test="so.warehouseId!=null">
            AND loc.Warehouse_Id = #{so.warehouseId}
        </if>
        inner join sc_product.productinfo pi on pi.id = psku.ProductInfo_Id
        <if test="so.cityId!=null">
            AND loc.City_Id = #{so.cityId}
        </if>
        inner join sc_product.productstorebatch psb on ps.Id = psb.productstore_id
        where 1=1
        AND ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        <if test="so.productSkuId!=null and so.productSkuId!=''">
            AND psku.ProductSku_Id = #{so.productSkuId}
        </if>
        <if test="so.warehouseId!=null and so.warehouseId!=''">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.cityId!=null and so.cityId!=''">
            AND ps.City_Id = #{so.cityId}
        </if>
        <if test="so.locationName!=null and so.locationName!=''">
            AND loc.name like concat(#{so.locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.locationFullName!=null and so.locationFullName!=''">
            AND psb.location_name = #{so.locationFullName,jdbcType=VARCHAR}
        </if>
        <if test="so.locationId !=null and so.locationId!=''">
            AND psb.location_id=#{so.locationId,jdbcType=BIGINT}
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.skuIds!=null and so.skuIds.size()>0">
            and psku.productsku_id in
            <foreach collection="so.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        order by psb.productiondate desc,psb.totalcount_minunit desc
    </select>

    <select id="findProductionDateByProductStoreIds" resultMap="BaseResultMap">
        SELECT productstore_id, MIN(productiondate) as productiondate
        from productstorebatch
        where productstore_id IN
        <foreach collection="productStoreIds" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="exlist != null and exlist.size() > 0">
            and locationCategory not in
            <foreach collection="exlist" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        and productiondate is not null and productiondate > '0001-01-01 00:00:00'
        and totalcount_minunit &gt; 0
        group by productstore_id
    </select>

    <update id="clearBatchInventoryProductDate">
        update productstorebatch set productiondate = null
        where id in
        <foreach collection="ids" index="index" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </update>


    <select id="queryZeroBatchInventory"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        <include refid="findBatchInventorySql">
        </include>
        AND ps.totalcount_minunit = 0
        <if test="so.productSkuId!=null and so.productSkuId!=''">
            AND psku.ProductSku_Id = #{so.productSkuId}
        </if>
        <if test="so.warehouseId!=null and so.warehouseId!=''">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.cityId!=null and so.cityId!=''">
            AND ps.City_Id = #{so.cityId}
        </if>
        <if test="so.locationName!=null and so.locationName!=''">
            AND loc.name like concat(#{so.locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.locationFullName!=null and so.locationFullName!=''">
            AND psb.location_name = #{so.locationFullName,jdbcType=VARCHAR}
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.source != null">
            AND psku.Source = #{so.source,jdbcType=INTEGER}
        </if>
        <if test="so.subCategoryList!=null and so.subCategoryList.size()>0">
            AND loc.subcategory in
            <foreach collection="so.subCategoryList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="so.skuIds!=null and so.skuIds.size()>0">
            and psku.productsku_id in
            <foreach collection="so.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.locationIds != null and so.locationIds.size() > 0">
            AND psb.location_id in
            <foreach collection="so.locationIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by psb.productiondate desc limit 0,1
    </select>

    <select id="findProductStoreBatchByCondition"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO">
        select DISTINCT psb.id, psb.productstore_id as productStoreId, psb.totalcount_minunit as totalCount,
        psb.productiondate
        as productionDate,
        psb.expiretime as expireTime, psb.batchtime as batchTime,
        psb.createuserId, psb.BatchAttributeInfoNo, psb.location_id as locationId,
        psb.location_name as locationName, psb.locationCategory, psb.subcategory,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.ProductSku_Id as productSkuId,ps.owner_id as ownerId,ps.secOwner_Id as secOwnerId, psb.batchProperty,
        psb.businessType
        , psb.batchProperty, psb.businessType
        from productstorebatch psb
        inner join productstore ps on ps.Id = psb.productstore_id
        inner join productsku sku on sku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        and sku.City_Id = ps.City_Id
        inner join location loc on loc.Id = psb.location_id AND loc.Warehouse_Id = ps.warehouse_id
        where ps.Warehouse_Id = #{queryDTO.warehouseId,jdbcType=INTEGER}
        <if test="queryDTO.ownerId != null and queryDTO.ownerId != ''">
            and ps.Owner_Id = #{queryDTO.ownerId}
        </if>
        <if test="queryDTO.locationAreaList != null and queryDTO.locationAreaList.size() > 0">
            and (
            loc.Area in
            <foreach collection="queryDTO.locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            or
            loc.`Name` in
            <foreach collection="queryDTO.locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="queryDTO.roadWayList != null and queryDTO.roadWayList.size() > 0">
            and loc.RoadWay in
            <foreach collection="queryDTO.roadWayList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="queryDTO.deleted != null and queryDTO.deleted.size > 0">
            and sku.IsDelete in
            <foreach item="item" collection="queryDTO.deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="queryDTO.locationTypeList != null and queryDTO.locationTypeList.size() > 0">
            and loc.subcategory in
            <foreach collection="queryDTO.locationTypeList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="queryDTO.skuIdList != null and queryDTO.skuIdList.size() > 0">
            and sku.productsku_id in
            <foreach collection="queryDTO.skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        and psb.totalcount_minunit != 0
    </select>


    <select id="listStoreBatchWithPromotion"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO">
        SELECT
        distinct psb.Id as storeBatchId,
        ps.Warehouse_Id as warehouseId,
        ps.ProductSpecification_Id as productSpecificationId,
        ps.Owner_Id as ownerId,
        ps.SecOwner_Id as secOwnerId,
        ps.Channel as channel,
        psku.ProductSku_Id as productSkuId,
        psku.Name as productSkuName,
        psku.specificationName,
        psku.unitName,
        psku.packageName,
        psku.packageQuantity,
        psku.Source,
        psb.productiondate as productionDate,
        psb.totalcount_minunit as storeTotalCount,
        psb.BatchAttributeInfoNo,
        psb.location_id as locationId,
        psb.location_name as locationName,
        loc.subcategory as locationSubcategory,
        loc.BusinessType as locationBusinessType,
        own.OwnerName as ownerName,
        ppsb.Id as promotionBatchId
        FROM sc_product.productstorebatch psb
        INNER JOIN sc_product.productstore ps on psb.productstore_id = ps.id
        INNER JOIN sc_product.productsku psku ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER join location loc on loc.Id = psb.location_id AND loc.Warehouse_Id = ps.warehouse_id
        LEFT JOIN owner own on ps.Owner_Id is not null and own.id = ps.Owner_Id
        LEFT JOIN sc_product.productpromotionstorebatch ppsb ON ppsb.WarehouseId = ps.Warehouse_Id
        AND ppsb.BatchAttributeInfoNo = psb.BatchAttributeInfoNo AND ppsb.IsDelete = 0
        WHERE
        ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        <if test="so.warehouseId != null">
            and ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.subCategoryList != null and so.subCategoryList.size() > 0">
            AND psb.subcategory in
            <foreach collection="so.subCategoryList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="so.deleted != null and so.deleted.size > 0">
            and psku.IsDelete in
            <foreach item="item" collection="so.deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="so.skuIds != null and so.skuIds.size() > 0">
            and psku.productsku_id in
            <foreach collection="so.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.specIdAndOwnerIdList != null and so.specIdAndOwnerIdList.size() > 0">
            AND
            <foreach collection="so.specIdAndOwnerIdList" item="item" index="index" separator="or" open="(" close=")">
                (
                ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
                <if test="item.ownerId == null">
                    and ps.Owner_Id is null
                </if>
                <if test="item.ownerId != null">
                    and ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}
                </if>
                )
            </foreach>
        </if>
        <if test="so.storeBatchIds != null and so.storeBatchIds.size() > 0">
            and psb.Id in
            <foreach collection="so.storeBatchIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
</mapper>