package com.yijiupi.himalaya.supplychain.search;

import java.io.Serializable;
import java.util.List;

import com.yijiupi.himalaya.base.search.PageCondition;

/**
 * 订单库存变更查询
 *
 * <AUTHOR>
 * @date 11/27/20 9:19 PM
 */
public class ProductStoreChangeRecordByOrderSO extends PageCondition implements Serializable {
    private static final long serialVersionUID = 1726236926651035728L;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 产品名称.
     */
    private String productName;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单号集合
     */
    private List<String> orderNoList;

    /**
     * 酒批事件类型
     */
    private List<Integer> jiupiEventTypeList;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public List<Integer> getJiupiEventTypeList() {
        return jiupiEventTypeList;
    }

    public void setJiupiEventTypeList(List<Integer> jiupiEventTypeList) {
        this.jiupiEventTypeList = jiupiEventTypeList;
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }
}
