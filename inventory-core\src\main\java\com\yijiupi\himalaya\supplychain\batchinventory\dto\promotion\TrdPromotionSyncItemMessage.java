package com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved. </br>
 * 交易大日期限时惠发布
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
public class TrdPromotionSyncItemMessage implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 产品SKU id
     */
    private Long productSkuId;

    /**
     * 货主Id
     */
    private Long ownerId;

    /**
     * 仓库Id集合
     */
    private Set<Integer> warehouseIds;

    /**
     * 产品信息规格Id
     */
    private Integer productInfoSpecId;

    /**
     * 临期开始日期
     */
    private Date closeExpirationBeginDate;

    /**
     * 临期结束日期
     */
    private Date closeExpirationEndDate;

    /**
     * 查询类目时期配置类型： 1：禁止销售期，2: 临期， 3：关注期， 4:绝对滞销期
     */
    private List<Byte> configTypes;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Set<Integer> getWarehouseIds() {
        return warehouseIds;
    }

    public void setWarehouseIds(Set<Integer> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    public Integer getProductInfoSpecId() {
        return productInfoSpecId;
    }

    public void setProductInfoSpecId(Integer productInfoSpecId) {
        this.productInfoSpecId = productInfoSpecId;
    }

    public Date getCloseExpirationBeginDate() {
        return closeExpirationBeginDate;
    }

    public void setCloseExpirationBeginDate(Date closeExpirationBeginDate) {
        this.closeExpirationBeginDate = closeExpirationBeginDate;
    }

    public Date getCloseExpirationEndDate() {
        return closeExpirationEndDate;
    }

    public void setCloseExpirationEndDate(Date closeExpirationEndDate) {
        this.closeExpirationEndDate = closeExpirationEndDate;
    }

    public List<Byte> getConfigTypes() {
        return configTypes;
    }

    public void setConfigTypes(List<Byte> configTypes) {
        this.configTypes = configTypes;
    }
}
