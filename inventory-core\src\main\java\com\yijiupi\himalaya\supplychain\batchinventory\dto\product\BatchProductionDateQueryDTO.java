package com.yijiupi.himalaya.supplychain.batchinventory.dto.product;

import java.io.Serializable;
import java.util.List;

public class BatchProductionDateQueryDTO implements Serializable {

    private static final long serialVersionUID = -2654874164778421117L;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 规格集合
     */
    private List<BatchProductionDateSpecQueryDTO> specList;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<BatchProductionDateSpecQueryDTO> getSpecList() {
        return specList;
    }

    public void setSpecList(List<BatchProductionDateSpecQueryDTO> specList) {
        this.specList = specList;
    }
}
