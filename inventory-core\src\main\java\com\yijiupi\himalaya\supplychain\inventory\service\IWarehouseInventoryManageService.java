package com.yijiupi.himalaya.supplychain.inventory.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockOperateDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.WJStockOperateDTO;

/**
 * 仓库库存管理服务.
 *
 * <AUTHOR>
 */
public interface IWarehouseInventoryManageService {

    /**
     * 修改仓库库存数（根据库存对账记录变更，可以自定义库存变更明细）
     */
    void modWarehouseInventoryByCheckRecord(WarehouseInventoryCheckRecordDTO warehouseInventoryCheckRecordDTO);

    /**
     * SAAS用库存修改接口.
     *
     * @param warehouseInventoryModDTO 设置仓库库存数量DTO
     */
    void modWarehouseInventoryBySaas(WarehouseInventoryModDTO warehouseInventoryModDTO);

    /**
     * SAAS用库存修改接口.
     */
    void modWarehouseInventoryBatchBySaas(List<WarehouseInventoryModDTO> lstModDTO);

    /**
     * 修改仓库库存数.
     *
     * @param warehouseInventoryModDTO 设置仓库库存数量DTO
     * @param opUserId 操作用户ID
     */
    void modWarehouseInventory(WarehouseInventoryModDTO warehouseInventoryModDTO, Integer opUserId);

    /**
     * 批量修改仓库库存数.
     *
     * @param lstModDTO 设置仓库库存数量DTO
     * @param opUserId 操作用户ID
     */
    void modWarehouseInventoryBatch(List<WarehouseInventoryModDTO> lstModDTO, Integer opUserId);

    /**
     * 修改经销商仓库库存数
     *
     * @param warehouseInventoryModDTO 设置仓库库存数量DTO
     * @param opUserId 操作用户ID
     */
    void modShopWarehouseInventory(WarehouseInventoryModDTO warehouseInventoryModDTO, Integer opUserId);

    /**
     * 仓库库存转移
     */
    void modTransfersWarehouseInventory(WarehouseInventoryTransfersDTO warehouseInventoryTransfersDTO,
        Integer opUserId);

    /**
     * 批量仓库库存转移
     */
    void batchModTransfersWarehouseInventory(List<WarehouseInventoryTransfersDTO> warehouseInventoryTransfersDTO,
        Integer opUserId);

    // /**
    // * 供应链调拨(第三方出库)发送供应链调拨销售库存变更消息
    // *
    // * @param list
    // */
    // @Deprecated
    // void sendShopWarehouseInventoryRecord(List<ShopWarehouseInventoryRecordDTO> list);

    /**
     * 供应链调拨(第三方出入库)
     */
    void agencyStockOperate(AgencyStockOperateDTO agencyStockOperateDTO);

    /**
     * 微酒(确认出入库)
     *
     * @param wjStockOperateDTO
     */
    void wjStockOperate(WJStockOperateDTO wjStockOperateDTO);

    /**
     * 根据仓库id获取 城市id
     *
     * @param warehouseId
     * @return
     */
    Integer getCityIdByWarehouseId(Integer warehouseId);

    /**
     * 库存操作
     */
    List<WarehouseInventoryChangeDTO> validateAndProcessProductStore(ProcessProductStoreDTO processProductStoreDTO);

    /**
     * 根据warehouseId和规格IDlist批量查询库存，并处理异常库存
     *
     * @return key：规格ID, value: 库存信息
     */
    List<ProductInventoryDTO> getLongProductInventoryPOMapAndCreateNoExits(ProductInventoryQueryDTO queryDTO);

    /**
     * 库存校验
     */
    Map<Long, BigDecimal> checkProductInventory(List<ProductInventoryCheckDTO> productInventoryCheckDTOS);
}
