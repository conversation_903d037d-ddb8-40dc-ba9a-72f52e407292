package com.yijiupi.himalaya.supplychain.dto.easysell;

import java.io.Serializable;

/**
 * 根据商品,skuid查询不同仓库库存参数DTO
 * 
 * @author: lidengfeng
 * @date: 2018/8/14 14:43
 */
public class ProductInfoStoreQueryDTO implements Serializable {

    /**
     * 商品规格id
     */
    private Long productSpecificationId;
    /**
     * 经销商id
     */
    private Long shopId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页数
     */
    private Integer pageSize;

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
