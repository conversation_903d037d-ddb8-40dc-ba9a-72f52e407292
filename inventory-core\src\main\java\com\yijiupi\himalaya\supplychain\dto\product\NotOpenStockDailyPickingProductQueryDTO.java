package com.yijiupi.himalaya.supplychain.dto.product;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 未开启货位库存每日拣货产品查询DTO
 */
public class NotOpenStockDailyPickingProductQueryDTO implements Serializable {

    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 城市Id
     */
    private Integer cityId;
    /**
     * skuId
     */
    private List<Long> skuIdList;

    /**
     * 动销时间-起(格式：yyyy-MM-dd HH:mm:ss)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dynamicStartTime;

    /**
     * 动销时间-止(格式：yyyy-MM-dd HH:mm:ss)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dynamicEndTime;

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public List<Long> getSkuIdList() {
        return skuIdList;
    }

    public void setSkuIdList(List<Long> skuIdList) {
        this.skuIdList = skuIdList;
    }

    public Date getDynamicStartTime() {
        return dynamicStartTime;
    }

    public void setDynamicStartTime(Date dynamicStartTime) {
        this.dynamicStartTime = dynamicStartTime;
    }

    public Date getDynamicEndTime() {
        return dynamicEndTime;
    }

    public void setDynamicEndTime(Date dynamicEndTime) {
        this.dynamicEndTime = dynamicEndTime;
    }
}
