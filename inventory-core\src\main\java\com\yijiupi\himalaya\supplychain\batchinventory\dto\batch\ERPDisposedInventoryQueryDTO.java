package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;

public class ERPDisposedInventoryQueryDTO implements Serializable {

    /**
     * 城市id
     */
    private String cityId;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 仓库id
     */
    private String storeHouseId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 库存类型:-1 全部，处理品 = 0, 陈列出库 = 1,
     */
    private Integer disposedType;

    /**
     * 是否仅查询未完成单据
     */
    private Boolean onlySearchUncompletedNote;

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getStoreHouseId() {
        return storeHouseId;
    }

    public void setStoreHouseId(String storeHouseId) {
        this.storeHouseId = storeHouseId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getDisposedType() {
        return disposedType;
    }

    public void setDisposedType(Integer disposedType) {
        this.disposedType = disposedType;
    }

    public Boolean getOnlySearchUncompletedNote() {
        return onlySearchUncompletedNote;
    }

    public void setOnlySearchUncompletedNote(Boolean onlySearchUncompletedNote) {
        this.onlySearchUncompletedNote = onlySearchUncompletedNote;
    }
}
