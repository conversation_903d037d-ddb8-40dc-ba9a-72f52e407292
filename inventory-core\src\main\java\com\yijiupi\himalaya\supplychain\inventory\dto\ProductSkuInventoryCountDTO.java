package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品库存数量DTO.
 *
 * <AUTHOR>
 */
public class ProductSkuInventoryCountDTO implements Serializable {

    /**
     * 产品SKUID.
     */
    private Long productSkuId;
    /**
     * 仓库ID.
     */
    private Integer warehouseId;
    /**
     * 库存数量.
     */
    private BigDecimal count;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主
     */
    private Long secOwnerId;

    /**
     * 获取 产品SKUID.
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    /**
     * 设置 产品SKUID.
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 仓库ID.
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库ID.
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 库存数量.
     */
    public BigDecimal getCount() {
        return count;
    }

    /**
     * 设置 库存数量.
     */
    public void setCount(BigDecimal count) {
        this.count = count;
    }

    /**
     * 获取 库存渠道0:酒批，1:大宗产品
     *
     * @return channel 库存渠道0:酒批，1:大宗产品
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 库存渠道0:酒批，1:大宗产品
     *
     * @param channel 库存渠道0:酒批，1:大宗产品
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 包装规格名称
     *
     * @return specName 包装规格名称
     */
    public String getSpecName() {
        return this.specificationName;
    }

    /**
     * 设置 包装规格名称
     *
     * @param specName 包装规格名称
     */
    public void setSpecName(String specName) {
        this.specificationName = specName;
    }

    /**
     * 获取 包装规格大单位
     *
     * @return packageName 包装规格大单位
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 包装规格大单位
     *
     * @param packageName 包装规格大单位
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 包装规格小单位
     *
     * @return unitName 包装规格小单位
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 包装规格小单位
     *
     * @param unitName 包装规格小单位
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 包装规格大小单位转换系数
     *
     * @return packageQuantity 包装规格大小单位转换系数
     */
    public BigDecimal getPackageQuantity() {
        return this.packageQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     *
     * @param packageQuantity 包装规格大小单位转换系数
     */
    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @return source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @param source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 包装规格名称
     *
     * @return specificationName 包装规格名称
     */
    public String getSpecificationName() {
        return this.specificationName;
    }

    /**
     * 设置 包装规格名称
     *
     * @param specificationName 包装规格名称
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    /**
     * 获取 二级货主
     *
     * @return secOwnerId 二级货主
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主
     *
     * @param secOwnerId 二级货主
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }
}
