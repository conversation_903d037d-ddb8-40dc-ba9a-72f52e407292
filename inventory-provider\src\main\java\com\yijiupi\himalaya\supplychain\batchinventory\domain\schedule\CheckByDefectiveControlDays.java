package com.yijiupi.himalaya.supplychain.batchinventory.domain.schedule;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.assignment.dto.push.MessagePushAdminUser;
import com.yijiupi.himalaya.assignment.dto.push.MessagePushParam;
import com.yijiupi.himalaya.assignment.service.IMessagePushService;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryQueryBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.apply.OutStockApplyDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.apply.OutStockItemApplyDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.apply.OutStockItemApplyDetailDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockApplyStateEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockApplyTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderBusinessType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockApplyManageService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.WarehouseClassEnum;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductCategoryService;
import com.yijiupi.supplychain.serviceutils.constant.AdminUserAuthRoleConstant;

/**
 * 临期滞销强制出库申请
 *
 * <AUTHOR>
 * @since 2025/5/23
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class CheckByDefectiveControlDays {

    private static final Logger logger = LoggerFactory.getLogger(CheckByDefectiveControlDays.class);

    @Autowired
    private BatchInventoryQueryBL batchInventoryQueryBL;
    @Reference
    private IOutStockApplyManageService iOutStockApplyManageService;
    @Reference
    private IProductCategoryService iProductCategoryService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private IVariableValueService iVariableValueService;

    @Reference
    private IAdminUserQueryService adminUserQueryService;
    @Reference
    private IOrgService orgService;
    @Reference
    private IMessagePushService iMessagePushService;

    /**
     * 系统管理员 用户 id
     */
    public static final int SYSTEM_ADMIN = 1;
    /**
     * 超期天数支持配置：残次品管控天数
     */
    private static final String DEFECTIVE_CONTROL_DAYS = "defectiveControlDays";
    /**
     * 申请原因
     */
    public static final String REASON = "产品滞留残次品区超%s天强制出库";

    private static final List<Integer> EXCLUDE_WAREHOUSE_ID_List =
        Arrays.asList(1411, 4741, 7421, 1341, 7461, 4401, 1391, 4461, 4591, 4331, 1421, 7431, 1531, 4481, 7481, 1471,
            7251, 5002, 1351, 7501, 1301, 1381, 1431, 1481, 4361, 7271, 4501, 1461, 7291, 1451, 1511, 1401, 7381, 9981,
            1331, 4551, 7471, 4161, 4071, 4311, 1371, 1541, 7411, 7491, 4371, 7311, 7401, 1281, 7321, 7301, 7281, 4541,
            7441, 1171, 7451, 1335, 4115, 1361, 7351, 4111, 1189, 4451, 4381, 1551, 1441, 1181, 1001, 1121, 1201, 1261,
            1611, 1651, 1681, 1721, 1741, 1851, 2091, 4031, 4091, 4601, 4811, 4871, 4971, 5031, 7001, 7041, 7061, 7161,
            9000602, 9000591, 9000601, 9000551, 9000483, 9000612, 9000598, 9000634, 9000755, 9000803);

    /**
     * 每天执行一次
     */
    @XxlJob("checkProductByDefectiveControlDays")
    public void checkProductByDefectiveControlDays() {
        logger.info("[临期滞销强制出库申请]开始");
        List<Warehouse> warehouseList = new ArrayList<>();
        String warehouseIdStr = XxlJobContext.getXxlJobContext().getJobParam();
        if (StringUtils.isNotEmpty(warehouseIdStr) && !Objects.equals(warehouseIdStr, "{}")) {
            List<Integer> warehouseIdList = Arrays.stream(warehouseIdStr.split("、"))
                .filter(p -> !StringUtils.isEmpty(p) && StringUtils.isNumeric(p)).map(p -> Integer.valueOf(p))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(warehouseIdList)) {
                warehouseList = iWarehouseQueryService.listWarehouseByIds(warehouseIdList);
            }
        } else {
            // 已启用仓库
            // 城市仓库((byte) 0)
            warehouseList = iWarehouseQueryService.listEnableWarehouseByTypes(Arrays.asList(0)).stream()
                .filter(p -> p != null && p.getCityId() != null && p.getCityId() >= 100 && p.getCityId() <= 900
                    && !Objects.equals(WarehouseClassEnum.虚仓.getType(), p.getWarehouseClass())
                    && !EXCLUDE_WAREHOUSE_ID_List.contains(p.getId()))
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(warehouseList)) {
            logger.info("[临期滞销强制出库申请]没有查询到任何仓库");
            return;
        }

        for (Warehouse warehouseElem : warehouseList) {
            try {
                checkByDefectiveControlDays(warehouseElem);
            } catch (Exception ex) {
                logger.error("[临期滞销强制出库申请]仓库名：{}，仓库ID：{}，获取仓库绝对临期滞销期产品数据异常", warehouseElem.getName(),
                    warehouseElem.getId(), ex);
            }
        }
        logger.info("[临期滞销强制出库申请]结束");
    }

    /**
     * 获取单个仓库的绝对临期滞销期产品数据
     */
    private void checkByDefectiveControlDays(Warehouse warehouseElem) {
        Integer warehouseId = warehouseElem.getId();
        Integer cityId = warehouseElem.getCityId();
        // 获取管控天数
        Integer defectiveControlDays = getDefectiveControlDays(warehouseId);
        // 1.查询仓库超过残次品管控天数的残次品库存
        BatchInventoryQueryDTO batchQueryDTO = new BatchInventoryQueryDTO();
        batchQueryDTO.setCityId(cityId);
        batchQueryDTO.setWarehouseId(warehouseId);
        batchQueryDTO.setSubCategoryList(Arrays.asList(LocationAreaEnum.残次品区.getType(), LocationEnum.残次品位.getType()));
        // 超过残次品管控天数
        batchQueryDTO.setEndTime(DateUtils.addDays(new Date(), -defectiveControlDays));
        // batchQueryDTO.setBatchProperty(ProductStoreBatchPropertyEnum.自动转入.getType());
        batchQueryDTO.setLimitSku((byte)1);
        batchQueryDTO.setPageSize(1000);
        List<BatchInventoryDTO> dtoList = new ArrayList<>();
        int pageCount = 1;
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
            batchQueryDTO.setPageNum(pageNum);

            // 查询批次库存
            PageList<BatchInventoryDTO> pageList = batchInventoryQueryBL.findBatchInventoryList(batchQueryDTO);
            if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
                continue;
            }

            if (pageNum == 1) {
                pageCount = pageList.getPager().getTotalPage();
            }

            dtoList.addAll(pageList.getDataList());
        }

        // 强制出库申请
        forceOutStockApply(warehouseElem, defectiveControlDays, dtoList);
    }

    private void forceOutStockApply(Warehouse warehouse, Integer defectiveControlDays,
        List<BatchInventoryDTO> batchInventoryDTOS) {
        if (CollectionUtils.isEmpty(batchInventoryDTOS)) {
            return;
        }

        Integer warehouseId = warehouse.getId();
        Integer cityId = warehouse.getCityId();
        List<Long> skuIds =
            batchInventoryDTOS.stream().map(BatchInventoryDTO::getProductSkuId).collect(Collectors.toList());
        Map<Long, ProductCategoryDTO> productCategoryMap = iProductCategoryService.findCategoryBySkuIds(skuIds);

        Map<Byte, MessagePushAdminUser> userMap = getUserIdByTaskProperty(warehouseId, batchInventoryDTOS);

        batchInventoryDTOS.stream().collect(Collectors.groupingBy(p -> p.getProductSkuId())).forEach((skuId, dtos) -> {
            BatchInventoryDTO dto = dtos.stream().findFirst().orElse(null);
            if (Objects.isNull(dto) || dto.getStoreTotalCount().compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }

            List<OutStockItemApplyDetailDTO> detailDTOS = new ArrayList<>();
            dtos.stream().forEach(item -> {
                OutStockItemApplyDetailDTO detailDTO = new OutStockItemApplyDetailDTO();
                detailDTO.setOrgId(cityId);
                detailDTO.setProductSpecificationId(item.getProductSpecificationId());
                detailDTO.setOwnerId(item.getOwnerId());
                detailDTO.setSecOwnerId(item.getSecOwnerId());
                detailDTO.setProductionDate(item.getProductionDate());
                detailDTO.setBatchAttributeInfoNo(item.getBatchAttributeInfoNo());
                detailDTO.setBatchTime(item.getBatchTime());
                detailDTO.setLocationId(item.getLocationId());
                detailDTO.setLocationName(item.getLocationName());
                detailDTO.setUnitTotalCount(item.getStoreTotalCount());
                detailDTOS.add(detailDTO);
            });

            List<OutStockItemApplyDTO> outStockItemApplyDTOS = new ArrayList<>();
            OutStockItemApplyDTO outStockItemApplyDTO = new OutStockItemApplyDTO();
            outStockItemApplyDTO.setOrgId(cityId);
            outStockItemApplyDTO.setWarehouseId(warehouseId);
            outStockItemApplyDTO.setProductSkuId(dto.getProductSkuId());
            outStockItemApplyDTO.setProductName(dto.getProductSkuName());
            outStockItemApplyDTO.setProductBrand(dto.getProductBrand());
            ProductCategoryDTO categoryDTO = productCategoryMap.get(dto.getProductSkuId());
            if (Objects.nonNull(categoryDTO)) {
                outStockItemApplyDTO.setCategoryName(categoryDTO.getStatisticsClassName());
            }
            outStockItemApplyDTO.setOwnerId(dto.getOwnerId());
            outStockItemApplyDTO.setSecOwnerId(dto.getSecOwnerId());
            // outStockItemApplyDTO.setSecOwnerName();
            outStockItemApplyDTO.setProductSpecificationId(dto.getProductSpecificationId());
            outStockItemApplyDTO.setSpecName(dto.getSpecificationName());
            outStockItemApplyDTO.setSpecQuantity(dto.getPackageQuantity());
            outStockItemApplyDTO.setPackageName(dto.getPackageName());
            outStockItemApplyDTO.setUnitName(dto.getUnitName());
            BigDecimal totalCount =
                dtos.stream().map(p -> p.getStoreTotalCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal[] count = totalCount.divideAndRemainder(dto.getPackageQuantity());
            outStockItemApplyDTO.setPackageCount(count[0]);
            outStockItemApplyDTO.setUnitCount(count[1]);
            outStockItemApplyDTO.setUnitTotalCount(totalCount);
            outStockItemApplyDTO.setOverUnitTotalCount(BigDecimal.ZERO);
            // outStockItemApplyDTO.setPrice();
            // outStockItemApplyDTO.setPriceUnit();
            // outStockItemApplyDTO.setTotalAmount();
            outStockItemApplyDTO.setSource(dto.getSource() != null ? dto.getSource().byteValue() : (byte)0);
            outStockItemApplyDTO.setChannel(dto.getChannel() != null ? dto.getChannel().byteValue() : (byte)0);
            outStockItemApplyDTO.setIsGift((byte)0);
            // 只能出残次品
            outStockItemApplyDTO.setIsDefective(true);
            // 填充具体货位生产日期
            outStockItemApplyDTO.setOutStockItemApplyDetailDTOS(detailDTOS);
            outStockItemApplyDTOS.add(outStockItemApplyDTO);

            MessagePushAdminUser user = userMap.get(dto.getStorageAttribute());
            Integer userId = user != null ? user.getId() : SYSTEM_ADMIN;
            String userName = user != null ? user.getUserName() : String.valueOf(SYSTEM_ADMIN);
            OutStockApplyDTO outStockApplyDTO = new OutStockApplyDTO();
            outStockApplyDTO.setOrgId(cityId);
            outStockApplyDTO.setWarehouseId(warehouseId);
            outStockApplyDTO.setOrderType(OutStockApplyTypeEnum.其他出库.getType());
            outStockApplyDTO.setBusinessType(OutStockOrderBusinessType.滞销强制出库.getType());
            outStockApplyDTO.setState(OutStockApplyStateEnum.待审核.getType());
            outStockApplyDTO.setOverPackageAmount(BigDecimal.ZERO);
            outStockApplyDTO.setOverUnitAmount(BigDecimal.ZERO);
            outStockApplyDTO.setApplyUserName(userName);
            outStockApplyDTO.setApplyUserId(userId);
            outStockApplyDTO.setUserName(userName);
            outStockApplyDTO.setUserId(userId);
            outStockApplyDTO.setRemark(String.format(REASON, defectiveControlDays));
            outStockApplyDTO.setOutStockItemApplyDTOS(outStockItemApplyDTOS);
            logger.info("[临期滞销强制出库申请]待申请数据结果:{}", JSON.toJSONString(outStockApplyDTO));
            iOutStockApplyManageService.createOutStockApplyByOA(outStockApplyDTO);
        });
    }

    /**
     * 残次品管控天数
     */
    private Integer getDefectiveControlDays(Integer warehouseId) {
        VariableValueQueryDTO configQuery = new VariableValueQueryDTO();
        configQuery.setVariableKey(DEFECTIVE_CONTROL_DAYS);
        configQuery.setWarehouseId(warehouseId);
        VariableDefAndValueDTO valueDTO = iVariableValueService.detailVariable(configQuery);
        Integer dateNum = 30;
        if (valueDTO == null || StringUtils.isEmpty(valueDTO.getVariableData())
            || !StringUtils.isNumeric(valueDTO.getVariableData())) {
            return dateNum;
        }

        dateNum = Integer.valueOf(valueDTO.getVariableData());
        return dateNum;
    }

    private AdminUser queryPunishPerson(Integer warehouseId) {
        OrgDTO org = orgService.findOrgByIds(Collections.singletonList(warehouseId)).get(warehouseId);
        AssertUtils.notNull(org, "组织机构不能为空 " + warehouseId);
        List<String> roleCodes = Arrays.asList(AdminUserAuthRoleConstant.ROLE_CODE_仓库管理员新流程);
        // 负责人先和组织机构上的联系人对比, 没有就随便拿一个
        List<AdminUser> users = adminUserQueryService.listAdminUser(roleCodes, warehouseId);
        if (users.isEmpty()) {
            String template = "定责任务失败, 找不到负责人, 入参 %s, %s";
            throw new BusinessException(String.format(template, roleCodes, warehouseId));
        }
        return users.stream().filter(it -> it.getMobileNo().equals(org.getContacterPhone())).findFirst()
            .orElseGet(() -> users.get(0));
    }

    private Map<Byte, MessagePushAdminUser> getUserIdByTaskProperty(Integer warehouseId,
        List<BatchInventoryDTO> batchInventoryDTOS) {
        if (CollectionUtils.isEmpty(batchInventoryDTOS)) {
            return Collections.emptyMap();
        }

        // 填充默认分仓属性
        fillDefaultStorageAttribute(batchInventoryDTOS);
        List<Integer> storageAttributes = batchInventoryDTOS.stream().map(p -> Integer.valueOf(p.getStorageAttribute()))
            .distinct().collect(Collectors.toList());
        Map<Byte, MessagePushAdminUser> userMap = new HashMap<>();
        storageAttributes.stream().forEach(taskProperty -> {
            MessagePushParam param = new MessagePushParam();
            param.setWarehouseId(warehouseId);
            param.setTaskProperty(taskProperty);
            MessagePushAdminUser adminUser = iMessagePushService.getAdminUserIdByTaskProperty(param);
            if (Objects.isNull(adminUser)) {
                throw new BusinessValidateException("未查询到仓管负责人，仓库id: " + warehouseId + "分仓属性: " + taskProperty);
            }

            userMap.put(taskProperty.byteValue(), adminUser);
        });

        return userMap;
    }

    private void fillDefaultStorageAttribute(List<BatchInventoryDTO> batchInventoryDTOS) {
        if (CollectionUtils.isEmpty(batchInventoryDTOS)) {
            return;
        }

        batchInventoryDTOS.stream().filter(p -> p.getStorageAttribute() == null).forEach(p -> {
            p.setStorageAttribute((byte)0);
        });
    }
}
