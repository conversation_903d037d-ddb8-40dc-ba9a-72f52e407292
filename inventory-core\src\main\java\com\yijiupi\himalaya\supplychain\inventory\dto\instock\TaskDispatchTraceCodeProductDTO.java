package com.yijiupi.himalaya.supplychain.inventory.dto.instock;

import java.io.Serializable;
import java.util.List;

/**
 * 溯源码信息
 *
 * <AUTHOR>
 */
public class TaskDispatchTraceCodeProductDTO implements Serializable {
    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * 溯源码信息
     */
    private List<TaskDispatchTraceCodeInfoProductDTO> productTraceCodeList;

    public List<TaskDispatchTraceCodeInfoProductDTO> getProductTraceCodeList() {
        return productTraceCodeList;
    }

    public void setProductTraceCodeList(List<TaskDispatchTraceCodeInfoProductDTO> productTraceCodeList) {
        this.productTraceCodeList = productTraceCodeList;
    }

    public static class TaskDispatchTraceCodeInfoProductDTO implements Serializable {
        private static final long serialVersionUID = 7902306672147686991L;
        /**
         * 条码id
         */
        private Long codeId;
        /**
         * 条码值
         */
        private String code;

        public Long getCodeId() {
            return codeId;
        }

        public void setCodeId(Long codeId) {
            this.codeId = codeId;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }
}
