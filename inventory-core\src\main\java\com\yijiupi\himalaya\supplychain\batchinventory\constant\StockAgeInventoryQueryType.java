package com.yijiupi.himalaya.supplychain.batchinventory.constant;

public enum StockAgeInventoryQueryType {
    /**
     * 全部
     */
    全部((byte)0),
    /**
     * 临期
     */
    临期((byte)1),
    /**
     * 超期
     */
    超期((byte)2);

    private Byte type;

    StockAgeInventoryQueryType(Byte type) {
        this.type = type;
    }

    public Byte getType() {
        return type;
    }

    @Override
    public String toString() {
        return String.valueOf(this.type);
    }

    public static StockAgeInventoryQueryType getEnum(Byte type) {
        StockAgeInventoryQueryType e = null;

        if (type != null) {
            for (StockAgeInventoryQueryType o : values()) {
                if (o.getType().equals(type)) {
                    e = o;
                }
            }
        }
        return e;
    }
}
