package com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-14 11:44
 **/
public class ProductionDateAuditBySkuDTO implements Serializable {

    /**
     * 主键 id
     */
    private Long id;

    /**
     * 仓库 id
     */
    private Integer warehouseId;

    /**
     * 仓库名称, 需要手动设置, 给前端展示用
     */
    private String warehouseName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品 skuId
     */
    private Long skuId;

    /**
     * 包装规格名称
     */
    private String packageSpecName;

    /**
     * 批次库存小件总数
     */
    private BigDecimal unitTotalCount;

    /**
     * 保质期
     */
    private Integer shelfLife;

    /**
     * 保质期单位, 1=年 2=月 3=日
     */
    private Integer shelfLifeUnit;

    private List<ProductionDateAuditDTO> lstProductionDate;

    public List<ProductionDateAuditDTO> getLstProductionDate() {
        return lstProductionDate;
    }

    public void setLstProductionDate(List<ProductionDateAuditDTO> lstProductionDate) {
        this.lstProductionDate = lstProductionDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getPackageSpecName() {
        return packageSpecName;
    }

    public void setPackageSpecName(String packageSpecName) {
        this.packageSpecName = packageSpecName;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public Integer getShelfLife() {
        return shelfLife;
    }

    public void setShelfLife(Integer shelfLife) {
        this.shelfLife = shelfLife;
    }

    public Integer getShelfLifeUnit() {
        return shelfLifeUnit;
    }

    public void setShelfLifeUnit(Integer shelfLifeUnit) {
        this.shelfLifeUnit = shelfLifeUnit;
    }

    @Override
    public String toString() {
        return "ProductionDateAuditDTO{" +
               "id=" + id +
               ", warehouseId=" + warehouseId +
               ", warehouseName='" + warehouseName + '\'' +
               ", productName='" + productName + '\'' +
               ", skuId=" + skuId +
               ", packageSpecName='" + packageSpecName + '\'' +
               ", unitTotalCount=" + unitTotalCount +
               ", shelfLife=" + shelfLife +
               ", shelfLifeUnit=" + shelfLifeUnit +
               '}';
    }
}
