package com.yijiupi.himalaya.supplychain.batchinventory.utils;

import java.util.Comparator;
import java.util.Date;
import java.util.Objects;

import com.yijiupi.himalaya.supplychain.batchinventory.constant.BatchInventoryComparatorType;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;

public class BatchInventoryComparator implements Comparator<BatchInventoryDTO> {

    public BatchInventoryComparator(int type) {
        this.type = type;
    }

    public BatchInventoryComparator(int type, Date productionDate) {
        this.type = type;
        this.productionDate = productionDate;
    }

    /**
     * 比较类型
     */
    private int type;

    /**
     * 指定生产日期
     */
    private Date productionDate;

    public void setType(int type) {
        this.type = type;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    @Override
    public int compare(BatchInventoryDTO o1, BatchInventoryDTO o2) {
        int result = 0;
        if (productionDate == null) {
            switch (type) {
                case BatchInventoryComparatorType.PRODUCTIONDATE:
                    result = productionDateCompare(o1, o2);
                    break;
                case BatchInventoryComparatorType.UNITTOTALCOUNT:
                    result = o1.getStoreTotalCount().compareTo(o2.getStoreTotalCount());
                    break;
                case BatchInventoryComparatorType.DATE_AND_COUNT:
                    result = dateAndCountCompare(o1, o2);
                    break;
                default:
                    break;
            }
        } else {
            result = assignProductionDateCompare(o1, o2, productionDate);
        }
        return result;
    }

    private int productionDateCompare(BatchInventoryDTO o1, BatchInventoryDTO o2) {
        if (o1.getProductionDate() == null && o2.getProductionDate() != null) {
            return -1;
        } else if (o1.getProductionDate() != null && o2.getProductionDate() == null) {
            return 1;
        } else if (o1.getProductionDate() != null && o2.getProductionDate() != null) {
            if (o1.getProductionDate().equals(o2.getProductionDate())) {
                return o2.getStoreTotalCount().compareTo(o1.getStoreTotalCount());
            } else {
                return o1.getProductionDate().compareTo(o2.getProductionDate());
            }
        } else if (o1.getBatchTime() == null && o2.getBatchTime() != null) {
            return 1;
        } else if (o1.getBatchTime() != null && o2.getBatchTime() == null) {
            return -1;
        } else if (o1.getBatchTime() != null && o2.getBatchTime() != null) {
            if (o1.getBatchTime().equals(o2.getBatchTime())) {
                return o2.getStoreTotalCount().compareTo(o1.getStoreTotalCount());
            } else {
                return o1.getBatchTime().compareTo(o2.getBatchTime());
            }
        } else {
            return o2.getStoreTotalCount().compareTo(o1.getStoreTotalCount());
        }
    }

    private int assignProductionDateCompare(BatchInventoryDTO o1, BatchInventoryDTO o2, Date productionDate) {
        if (Objects.equals(productionDate, o1.getProductionDate())
            && !Objects.equals(productionDate, o2.getProductionDate())) {
            return 1;
        } else if (!Objects.equals(productionDate, o1.getProductionDate())
            && Objects.equals(productionDate, o2.getProductionDate())) {
            return -1;
        } else if (Objects.equals(productionDate, o1.getProductionDate())
            && Objects.equals(productionDate, o2.getProductionDate())) {
            return o2.getStoreTotalCount().compareTo(o1.getStoreTotalCount());
        } else {
            return productionDateCompare(o1, o2);
        }
    }

    private int dateAndCountCompare(BatchInventoryDTO o1, BatchInventoryDTO o2) {
        // 生产日期排序，空值在前，非空正序
        Comparator<BatchInventoryDTO> productionDateComparator = Comparator
            .comparing(BatchInventoryDTO::getProductionDate, Comparator.nullsFirst(Comparator.naturalOrder()));

        // 批次时间排序，当生产日期都为空时，按批次时间空值在后，非空正序，加数量正序
        Comparator<BatchInventoryDTO> batchTimeComparator =
            Comparator.comparing(BatchInventoryDTO::getBatchTime, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(BatchInventoryDTO::getStoreTotalCount);

        // 数量正序
        Comparator<BatchInventoryDTO> countComparator = Comparator.comparing(BatchInventoryDTO::getStoreTotalCount);

        return productionDateComparator.thenComparing((a, b) -> {
            // 生产日期都为空时，使用批次时间组合排序
            if (a.getProductionDate() == null && b.getProductionDate() == null) {
                return batchTimeComparator.compare(a, b);
            }
            // 生产日期相同按数量正序
            return countComparator.compare(a, b);
        }).compare(o1, o2);
    }
}
