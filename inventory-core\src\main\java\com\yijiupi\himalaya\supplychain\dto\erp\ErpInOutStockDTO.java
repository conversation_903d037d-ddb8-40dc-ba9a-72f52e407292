package com.yijiupi.himalaya.supplychain.dto.erp;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-18 10:32
 **/
public class ErpInOutStockDTO implements Serializable {

    /**
     * 车次编号-入库仓库 id
     */
    private String batchNo;

    /**
     * 时间
     */
    private String businessTime;

    /**
     * 出库城市 id
     */
    private Integer fromCityId;

    /**
     * 出库仓库 id
     */
    private Integer fromStoreHouseId;

    /**
     * 订单明细
     */
    private List<ErpInOutStockItem> items;

    /**
     * 车次 id-入库仓库 id
     */
    private String noteNo;

    /**
     * 车次订单 id
     */
    private List<Long> orderIds;

    /**
     * 车次订单号
     */
    private List<String> orderNos;

    /**
     * 备注
     */
    private String remark;

    /**
     * 入库城市 id
     */
    private Integer toCityId;

    /**
     * 入库仓库 id
     */
    private Integer toStoreHouseId;

    /**
     * 1、内配 / 内配退
     */
    private String orderSource;

    /**
     * 类型、1: 出库, 2: 入库<br/>
     *
     * @see ErpInOutStockTypeEnum
     */
    private Byte type;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getBusinessTime() {
        return businessTime;
    }

    public void setBusinessTime(String businessTime) {
        this.businessTime = businessTime;
    }

    public Integer getFromCityId() {
        return fromCityId;
    }

    public void setFromCityId(Integer fromCityId) {
        this.fromCityId = fromCityId;
    }

    public Integer getFromStoreHouseId() {
        return fromStoreHouseId;
    }

    public void setFromStoreHouseId(Integer fromStoreHouseId) {
        this.fromStoreHouseId = fromStoreHouseId;
    }

    public List<ErpInOutStockItem> getItems() {
        return items;
    }

    public void setItems(List<ErpInOutStockItem> items) {
        this.items = items;
    }

    public String getNoteNo() {
        return noteNo;
    }

    public void setNoteNo(String noteNo) {
        this.noteNo = noteNo;
    }

    public List<Long> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(List<Long> orderIds) {
        this.orderIds = orderIds;
    }

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getToCityId() {
        return toCityId;
    }

    public void setToCityId(Integer toCityId) {
        this.toCityId = toCityId;
    }

    public Integer getToStoreHouseId() {
        return toStoreHouseId;
    }

    public void setToStoreHouseId(Integer toStoreHouseId) {
        this.toStoreHouseId = toStoreHouseId;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }
}
