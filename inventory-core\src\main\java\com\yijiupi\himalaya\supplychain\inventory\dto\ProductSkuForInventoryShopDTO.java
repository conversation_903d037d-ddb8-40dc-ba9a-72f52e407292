package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.util.List;

import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

/**
 * 经销商库存查询DTO
 *
 * <AUTHOR> 2018/3/8
 */
public class ProductSkuForInventoryShopDTO extends PagerCondition {
    /**
     * 搜索条件
     */
    private String searchKey;
    /**
     * skuId
     */
    private List<Long> productSkuIdList;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 渠道
     */
    private Integer channel = ProductChannelType.JIUPI;
    /**
     * 来源
     */
    private Integer source = ProductSourceType.易酒批;
    /**
     * 一级货主
     */
    private Long ownerId;
    /**
     * 货主类型
     */
    private Integer ownerType;

    /**
     * 获取 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 渠道
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 渠道
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 来源
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 来源
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 一级货主
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置 一级货主
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 货主类型
     */
    public Integer getOwnerType() {
        return this.ownerType;
    }

    /**
     * 设置 货主类型
     */
    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    /**
     * 获取 skuId
     */
    public List<Long> getProductSkuIdList() {
        return this.productSkuIdList;
    }

    /**
     * 设置 skuId
     */
    public void setProductSkuIdList(List<Long> productSkuIdList) {
        this.productSkuIdList = productSkuIdList;
    }

    /**
     * 获取 搜索条件
     */
    public String getSearchKey() {
        return this.searchKey;
    }

    /**
     * 设置 搜索条件
     */
    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }
}
