package com.yijiupi.himalaya.supplychain.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 校正库存类型: 0. 销售库存 1.仓库库存
 *
 * <AUTHOR>
 * @date 2018/1/10 11:50
 */
public enum StoreTypeEnum {
    /**
     * 销售库存
     */
    销售库存((byte)0),
    /**
     * 仓库库存
     */
    仓库库存((byte)1);

    private byte type;

    StoreTypeEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    public void setType(byte type) {
        this.type = type;
    }

    /**
     * 根据枚举值获取枚举对象名称
     * 
     * @param value
     * @return
     */
    public static String getEnmuName(Byte value) {
        String name = null;
        if (value != null) {
            name = cache.get(value);
        }
        return name;
    }

    private static Map<Byte, String> cache =
        EnumSet.allOf(StoreTypeEnum.class).stream().collect(Collectors.toMap(p -> p.type, p -> p.name()));

}
