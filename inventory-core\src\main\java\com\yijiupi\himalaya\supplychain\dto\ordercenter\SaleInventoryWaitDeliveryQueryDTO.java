package com.yijiupi.himalaya.supplychain.dto.ordercenter;

import java.io.Serializable;
import java.util.List;

/**
 * 查询待发货参数
 *
 */
public class SaleInventoryWaitDeliveryQueryDTO implements Serializable {

    private static final long serialVersionUID = -7513877566434113705L;

    /**
     * 主键id集合
     */
    private List<Long> ids;

    /**
     * 销售库存ID集合
     */
    private List<Long> inventoryIds;

    /**
     * 销售库存ID
     */
    private Long inventoryId;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 规格ID集合
     */
    private List<Long> productSpecIds;

    /**
     * 规格ID
     */
    private Long productSpecId;

    /**
     * 一级货主ID
     */
    private Long ownerId;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    /**
     * 订单ID集合
     */
    private List<Long> sourceIds;

    /**
     * 变更来源ID（可以是oms系统id，或其他异构系统内部id）
     */
    private Long sourceId;

    /**
     * 变更来源编号（可以是oms系统编号，或其他异构系统内部编号）
     */
    private String sourceNo;

    /**
     * 变更来源项ID（可以是oms系统itemId，或其他异构系统内部itemId）
     */
    private Long sourceItemId;

    /**
     * 单据来源类型（配合orderId使用，防止异构系统orderId重复）
     */
    private String orderSource;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 当前页
     */
    protected long current = 1;

    /**
     * 每页显示条数，默认 10
     */
    protected long size = 10;

    /**
     * ownerId为空处理规则，true:ownerId为空只查ownerId是null的数据,fasle:查全部，默认为true
     */
    private Boolean queryWhenOwnerIsNull = Boolean.TRUE;

    /**
     * secownerId为空处理规则，true:只查secownerId是null的数据,fasle:查全部，默认为true
     */
    private Boolean queryWhenSecOwnerIsNull = Boolean.TRUE;

    /**
     * 是否清除
     */
    private Boolean clear = Boolean.FALSE;

    /**
     * 是否回滚
     */
    private Boolean restore = Boolean.FALSE;

    /**
     * 备注
     */
    private String desc;

    /**
     * 最后更新开始时间
     */
    private String lastUpdateTimeStart;

    /**
     * 最后更新结束时间
     */
    private String lastUpdateTimeEnd;

    /**
     * 单号模糊查询
     */
    private String sourceNoLike;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否逆序
     */
    private Boolean orderByDesc;

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public List<Long> getInventoryIds() {
        return inventoryIds;
    }

    public void setInventoryIds(List<Long> inventoryIds) {
        this.inventoryIds = inventoryIds;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<Long> getProductSpecIds() {
        return productSpecIds;
    }

    public void setProductSpecIds(List<Long> productSpecIds) {
        this.productSpecIds = productSpecIds;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceNo() {
        return sourceNo;
    }

    public void setSourceNo(String sourceNo) {
        this.sourceNo = sourceNo;
    }

    public Long getSourceItemId() {
        return sourceItemId;
    }

    public void setSourceItemId(Long sourceItemId) {
        this.sourceItemId = sourceItemId;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public List<Long> getSourceIds() {
        return sourceIds;
    }

    public void setSourceIds(List<Long> sourceIds) {
        this.sourceIds = sourceIds;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public long getCurrent() {
        return current;
    }

    public void setCurrent(long current) {
        this.current = current;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Boolean getQueryWhenOwnerIsNull() {
        return queryWhenOwnerIsNull;
    }

    public void setQueryWhenOwnerIsNull(Boolean queryWhenOwnerIsNull) {
        this.queryWhenOwnerIsNull = queryWhenOwnerIsNull;
    }

    public Boolean getQueryWhenSecOwnerIsNull() {
        return queryWhenSecOwnerIsNull;
    }

    public void setQueryWhenSecOwnerIsNull(Boolean queryWhenSecOwnerIsNull) {
        this.queryWhenSecOwnerIsNull = queryWhenSecOwnerIsNull;
    }

    public Boolean getClear() {
        return clear;
    }

    public void setClear(Boolean clear) {
        this.clear = clear;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Boolean getRestore() {
        return restore;
    }

    public void setRestore(Boolean restore) {
        this.restore = restore;
    }

    public String getLastUpdateTimeStart() {
        return lastUpdateTimeStart;
    }

    public void setLastUpdateTimeStart(String lastUpdateTimeStart) {
        this.lastUpdateTimeStart = lastUpdateTimeStart;
    }

    public String getLastUpdateTimeEnd() {
        return lastUpdateTimeEnd;
    }

    public void setLastUpdateTimeEnd(String lastUpdateTimeEnd) {
        this.lastUpdateTimeEnd = lastUpdateTimeEnd;
    }

    public String getSourceNoLike() {
        return sourceNoLike;
    }

    public void setSourceNoLike(String sourceNoLike) {
        this.sourceNoLike = sourceNoLike;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Boolean getOrderByDesc() {
        return orderByDesc;
    }

    public void setOrderByDesc(Boolean orderByDesc) {
        this.orderByDesc = orderByDesc;
    }
}
