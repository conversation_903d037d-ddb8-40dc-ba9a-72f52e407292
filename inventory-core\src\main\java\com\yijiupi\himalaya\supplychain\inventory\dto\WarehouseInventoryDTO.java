package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 仓库库存DTO.
 *
 * <AUTHOR>
 */
public class WarehouseInventoryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 产品skuId
     */
    private Long productSkuId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 规格名称
     */
    private String specificationName;
    /**
     * 库存小单位
     */
    private String unitName;
    /**
     * 库存大单位
     */
    private String packageName;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 发货中数量
     */
    private BigDecimal deliveryedCount;
    /**
     * 已退货入库未完成
     */
    private BigDecimal unReturnCount;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 所属人ID
     */
    private Long ownerId;
    /**
     * 所属人名称
     */
    private String ownerName;
    /**
     * 所属人类型
     */
    private Integer ownerType;
    /**
     * ID.
     */
    private String id;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 产品信息规格id
     */
    private Long productSpecId;
    /**
     * 仓库库存按照小单位换算的数量(文本)
     */
    private BigDecimal warehouseTotalCount;
    /**
     * 渠道
     */
    private Integer channel;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;

    /**
     * 库存差异总数量
     */
    private BigDecimal diffTotalCount;
    
    /**
     * ERP原始的二级货主ID
     */
    private String erpSecOwnerId;

    public BigDecimal getUnReturnCount() {
        return unReturnCount;
    }

    public void setUnReturnCount(BigDecimal unReturnCount) {
        this.unReturnCount = unReturnCount;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public BigDecimal getDeliveryedCount() {
        return deliveryedCount;
    }

    public void setDeliveryedCount(BigDecimal deliveryedCount) {
        this.deliveryedCount = deliveryedCount;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public BigDecimal getWarehouseTotalCount() {
        return warehouseTotalCount;
    }

    public void setWarehouseTotalCount(BigDecimal warehouseTotalCount) {
        this.warehouseTotalCount = warehouseTotalCount;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public BigDecimal getDiffTotalCount() {
        return diffTotalCount;
    }

    public void setDiffTotalCount(BigDecimal diffTotalCount) {
        this.diffTotalCount = diffTotalCount;
    }

    public String getErpSecOwnerId() {
        return erpSecOwnerId;
    }

    public void setErpSecOwnerId(String erpSecOwnerId) {
        this.erpSecOwnerId = erpSecOwnerId;
    }
}
