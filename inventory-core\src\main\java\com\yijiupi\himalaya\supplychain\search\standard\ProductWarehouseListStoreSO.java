package com.yijiupi.himalaya.supplychain.search.standard;

import java.io.Serializable;
import java.util.List;

/**
 * 获取产品对应仓库
 * 
 * @author: tangkun
 * @date: 2017年4月14日 上午10:22:41
 */
public class ProductWarehouseListStoreSO implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<ProductWarehouseListStoreItemSO> data;

    public List<ProductWarehouseListStoreItemSO> getData() {
        return data;
    }

    public void setData(List<ProductWarehouseListStoreItemSO> data) {
        this.data = data;
    }

}
