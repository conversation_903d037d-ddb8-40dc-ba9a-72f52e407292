package com.yijiupi.himalaya.supplychain.enums;

/**
 * <AUTHOR>
 * @Title: himalaya-supplychain-microservice-inventory
 * @Package com.yijiupi.himalaya.supplychain.enums
 * @Description:
 * @date 2018/6/2 17:35
 */
public enum ChargeConfigStatusEnum {
    /**
     * STOP_USE
     */
    STOP_USE((byte)0),
    /**
     * START_USE
     */
    START_USE((byte)1);

    private Byte type;

    ChargeConfigStatusEnum(byte type) {
        this.type = type;
    }

    public byte getType() {
        return type;
    }

    /**
     * 返回枚举
     *
     * @param type
     * @return
     */
    public static ChargeConfigStatusEnum getEnum(Byte type) {
        ChargeConfigStatusEnum e = null;

        if (type != null) {
            for (ChargeConfigStatusEnum o : values()) {
                if (o.getType() == type) {
                    e = o;
                }
            }
        }
        return e;
    }
}
