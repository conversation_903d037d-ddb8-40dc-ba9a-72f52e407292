package com.yijiupi.himalaya.supplychain.batchinventory.dto.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description
 * <AUTHOR>
 * @Date 2022/1/6 13:57
 */
public class CcpPriceMessageDTO implements Serializable {
    private static final long serialVersionUID = 1604408297678910068L;
    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 批次库存（含批次库存ID、小单位数量、skuId）
     */
    private List<ProductStoreBatchDTO> storeBatchList;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<ProductStoreBatchDTO> getStoreBatchList() {
        return storeBatchList;
    }

    public void setStoreBatchList(List<ProductStoreBatchDTO> storeBatchList) {
        this.storeBatchList = storeBatchList;
    }
}
