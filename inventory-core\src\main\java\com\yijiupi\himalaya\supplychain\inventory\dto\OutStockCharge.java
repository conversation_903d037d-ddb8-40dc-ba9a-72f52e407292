package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 出库单费用
 * 
 * @author: lidengfeng
 * @date 2018/10/9 20:46
 */
public class OutStockCharge implements Serializable {

    /**
     * 配送费
     */
    private BigDecimal deliveryfee;

    /**
     * 托管费
     */
    private BigDecimal depositfee;

    /**
     * 装车费
     */
    private BigDecimal loadingCharge;

    /**
     * 卸货费
     */
    private BigDecimal landingCharge;

    /**
     * 仓库使用费
     */
    private BigDecimal warehouseCharge;

    public BigDecimal getDeliveryfee() {
        return deliveryfee;
    }

    public void setDeliveryfee(BigDecimal deliveryfee) {
        this.deliveryfee = deliveryfee;
    }

    public BigDecimal getDepositfee() {
        return depositfee;
    }

    public void setDepositfee(BigDecimal depositfee) {
        this.depositfee = depositfee;
    }

    public BigDecimal getLoadingCharge() {
        return loadingCharge;
    }

    public void setLoadingCharge(BigDecimal loadingCharge) {
        this.loadingCharge = loadingCharge;
    }

    public BigDecimal getLandingCharge() {
        return landingCharge;
    }

    public void setLandingCharge(BigDecimal landingCharge) {
        this.landingCharge = landingCharge;
    }

    public BigDecimal getWarehouseCharge() {
        return warehouseCharge;
    }

    public void setWarehouseCharge(BigDecimal warehouseCharge) {
        this.warehouseCharge = warehouseCharge;
    }

}
