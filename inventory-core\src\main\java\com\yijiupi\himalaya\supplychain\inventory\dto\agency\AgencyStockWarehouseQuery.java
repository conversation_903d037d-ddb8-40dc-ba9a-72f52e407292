package com.yijiupi.himalaya.supplychain.inventory.dto.agency;

import java.io.Serializable;

/**
 *
 * @author: lidengfeng
 * @date 2018/9/29 11:09
 */
public class AgencyStockWarehouseQuery implements Serializable {

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 获取 城市id
     * 
     * @return
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市id
     * 
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    /**
     * 获取 城市名称
     * 
     * @return
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置 城市名称
     * 
     * @param city
     */
    public void setCity(String city) {
        this.city = city;
    }
}
