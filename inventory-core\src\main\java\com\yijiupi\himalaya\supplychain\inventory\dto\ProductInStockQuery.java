package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 经销商仓配 申请入库产品查询参数
 * 
 * @author: lidengfeng
 * @date 2018/9/29 14:43
 */
public class ProductInStockQuery implements Serializable {

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 服务商id集合
     */
    private List<Long> facilitatorIdList;

    /**
     * 经销商id集合
     */
    private List<String> dealerIdList;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 品牌
     */
    private String productBrand;

    /**
     * 经销商名称
     */
    private String dealerName;

    /**
     * 城市
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 经销商手机号
     */
    private String mobileNo;

    /**
     * 是否收取仓配费用
     */
    private Byte isGetWarehouseCharge;

    /**
     * 业务类型 0=正常业务 1=贷款业务
     */
    private Byte businessType;

    /**
     * 0=待设置产品费用，1=已设置产品费用
     */
    private Byte status;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页数
     */
    private Integer pageSize;

    /**
     * 获取 服务商id
     * 
     * @return
     */
    public Long getFacilitatorId() {
        return facilitatorId;
    }

    /**
     * 设置 服务商id
     * 
     * @param facilitatorId
     */
    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    /**
     * 获取 商品名称
     * 
     * @return
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置 商品名称
     * 
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 产品品牌
     * 
     * @return
     */
    public String getProductBrand() {
        return productBrand;
    }

    /**
     * 设置 产品品牌
     * 
     * @param productBrand
     */
    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    /**
     * 获取 城市id
     * 
     * @return
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市id
     * 
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库id
     * 
     * @return
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库id
     * 
     * @param warehouseId
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 手机号
     * 
     * @return
     */
    public String getMobileNo() {
        return mobileNo;
    }

    /**
     * 设置 手机号
     * 
     * @param mobileNo
     */
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    /**
     * 获取 是否收取仓配费用
     * 
     * @return
     */
    public Byte getIsGetWarehouseCharge() {
        return isGetWarehouseCharge;
    }

    /**
     * 设置是否收取仓配费用
     */
    public void setIsGetWarehouseCharge(Byte isGetWarehouseCharge) {
        this.isGetWarehouseCharge = isGetWarehouseCharge;
    }

    /**
     * 获取 业务类型
     * 
     * @return
     */
    public Byte getBusinessType() {
        return businessType;
    }

    /**
     * 设置 业务类型
     * 
     * @param businessType
     */
    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取是否 设置产品费用
     * 
     * @return
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置 是否设置产品费用
     * 
     * @param status
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取 开始时间
     * 
     * @return
     */
    public String getStartTime() {
        return startTime;
    }

    /**
     * 设置 开始时间
     * 
     * @param startTime
     */
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    /**
     * 获取 结束时间
     * 
     * @return
     */
    public String getEndTime() {
        return endTime;
    }

    /**
     * 设置 结束时间
     * 
     * @param endTime
     */
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    /**
     * 获取 页码
     * 
     * @return
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 设置 页码
     * 
     * @param pageNum
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 获取 每页的数量
     * 
     * @return
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 设置 每页的数量
     * 
     * @param pageSize
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 获取 经销商id集合
     * 
     * @return
     */
    public List<String> getDealerIdList() {
        return dealerIdList;
    }

    /**
     * 设置 经销商id集合
     * 
     * @param dealerIdList
     */
    public void setDealerIdList(List<String> dealerIdList) {
        this.dealerIdList = dealerIdList;
    }

    /**
     * 获取 城市名称
     * 
     * @return
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置 城市名称
     * 
     * @param city
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 获取 经销商名称
     * 
     * @return
     */
    public String getDealerName() {
        return dealerName;
    }

    /**
     * 设置 经销商名称
     * 
     * @param dealerName
     */
    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    /**
     * 获取 服务商id集合
     * 
     * @return
     */
    public List<Long> getFacilitatorIdList() {
        return facilitatorIdList;
    }

    /**
     * 设置 服务商id集合
     * 
     * @param facilitatorIdList
     */
    public void setFacilitatorIdList(List<Long> facilitatorIdList) {
        this.facilitatorIdList = facilitatorIdList;
    }
}
