package com.yijiupi.himalaya.supplychain.inventory.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 经销商产品库存信息返回DTO
 *
 * <AUTHOR> 2018/3/8
 */
public class ProductShopStoreReturnDTO implements Serializable {

    /**
     * productstore主键id
     */
    private String id;
    /**
     * 产品名称
     */
    private String productSkuName;
    /**
     * 库存数量
     */
    private BigDecimal totalCountMinUnit;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 获取 productstore主键id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 productstore主键id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 库存数量
     */
    public BigDecimal getTotalCountMinUnit() {
        return this.totalCountMinUnit;
    }

    /**
     * 设置 库存数量
     */
    public void setTotalCountMinUnit(BigDecimal totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }

    /**
     * 获取 包装规格名称
     */
    public String getSpecificationName() {
        return this.specificationName;
    }

    /**
     * 设置 包装规格名称
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    /**
     * 获取 包装规格大单位
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 包装规格大单位
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 包装规格小单位
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 包装规格小单位
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 包装规格大小单位转换系数
     */
    public BigDecimal getPackageQuantity() {
        return this.packageQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     */
    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 产品名称
     */
    public String getProductSkuName() {
        return this.productSkuName;
    }

    /**
     * 设置 产品名称
     */
    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }
}
