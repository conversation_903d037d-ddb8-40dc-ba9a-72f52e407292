package com.yijiupi.himalaya.supplychain.enums;

public enum JiupiEventType {
    /**
     * 下单扣城市库存
     */
    下单扣城市库存(0),
    /**
     * 预售审核扣城市库存
     */
    预售审核扣城市库存(1),
    /**
     * 下单失败返城市库存
     */
    下单失败返城市库存(2),
    /**
     * 订单取消返还城市库存
     */
    订单取消返还城市库存(3),
    /**
     * 订单审核不通过返还城市库
     */
    订单审核不通过返还城市库(4),
    /**
     * 仓库发货扣仓库库存
     */
    仓库发货扣仓库库存(5),
    /**
     * 部分配送返城市库存
     */
    部分配送返城市库存(6),
    /**
     * 部分配送返仓库库存
     */
    部分配送返仓库库存(7),
    /**
     * 配送失败返城市库存
     */
    配送失败返城市库存(8),
    /**
     * 配送失败返仓库库存
     */
    配送失败返仓库库存(9),
    /**
     * 退货单返城市库存
     */
    退货单返城市库存(10),
    /**
     * 退货单返仓库库存
     */
    退货单返仓库库存(11),
    /**
     * 分配仓库修改待发货库存
     */
    分配仓库修改待发货库存(12),
    /**
     * 下单扣销售库存
     */
    下单扣销售库存(30),
    /**
     * 预售审核扣销售库存
     */
    预售审核扣销售库存(31),
    /**
     * 下单失败返销售库存
     */
    下单失败返销售库存(32),
    /**
     * 订单取消返还销售库存
     */
    订单取消返还销售库存(33),
    /**
     * 订单审核不通过返还销售库存
     */
    订单审核不通过返还销售库存(34),
    /**
     * 配送失败返销售库存
     */
    配送失败返销售库存(35),
    /**
     * 退货单返销售库存
     */
    退货单返销售库存(36),
    /**
     * 部分配送返销售库存
     */
    部分配送返销售库存(37),
    /**
     * 初始化活动库存
     */
    初始化活动库存(50),
    /**
     * 扣除活动库存
     */
    扣除活动库存(51),
    /**
     * 返还活动库存
     */
    返还活动库存(52),
    /**
     * 初始化预售库存
     */
    初始化预售库存(53),
    /**
     * 扣除预售库存
     */
    扣除预售库存(54),
    /**
     * 返还预售库存
     */
    返还预售库存(55),
    /**
     * 内配单入库加库存
     */
    内配单入库加库存(56),
    /**
     * 内配单出库减库存
     */
    内配单出库减库存(57),
    /**
     * 加工单入库加库存
     */
    加工单入库加库存(58),
    /**
     * 加工单出库减库存
     */
    加工单出库减库存(59),
    /**
     * 加工破损出库减库存
     */
    加工破损出库减库存(60),
    /**
     * 供应链库存同步
     */
    供应链库存同步(96),
    /**
     * erp库存同步
     */
    erp库存同步(97),
    /**
     * 手动修改
     */
    手动修改(98),
    /**
     * 自动校正
     */
    自动校正(99),
    /**
     * 智能预售
     */
    智能预售(100),
    /**
     * 供应链调拨
     */
    供应链调拨(101),
    /**
     * 订单延迟配送
     */
    订单延迟配送(102),
    /**
     * 订单召回
     */
    订单召回(103),
    /**
     * 自提订单出库
     */
    自提订单出库(104),
    /**
     * 仓管确认入库
     */
    仓管确认入库(105),
    /**
     * 财务收款确认入库
     */
    财务收款确认入库(106),
    /**
     * 易款便利线下单扣仓库库存
     */
    易款便利线下单扣仓库库存(107),
    /**
     * 易款便利线下退货单返仓库库存
     */
    易款便利线下退货单返仓库库存(108),
    /**
     * 易款便利线下单扣销售库存
     */
    易款便利线下单扣销售库存(109),
    /**
     * 易款便利线下退货单返销售库存
     */
    易款便利线下退货单返销售库存(110),
    /**
     * 注册有礼订单扣仓库库存
     */
    注册有礼订单扣仓库库存(111),
    /**
     * 注册有礼订单扣销售库存
     */
    注册有礼订单扣销售库存(112);

    private Integer type;

    JiupiEventType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    /**
     * 返回枚举
     *
     * @param type
     * @return
     */
    public static JiupiEventType getEnum(Integer type) {
        JiupiEventType e = null;

        if (type != null) {
            for (JiupiEventType o : values()) {
                if (o.getType().equals(type)) {
                    e = o;
                }
            }
        }
        return e;
    }

    @Override
    public String toString() {
        return String.valueOf(this.type);
    }
}
