package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import java.io.Serializable;

public class BatchAttributeValueSaveItemDetailDTO implements Serializable {

    private static final long serialVersionUID = -4793031590771271616L;
    /**
     * 字典表主键
     */
    private Long dicId;
    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 是否参与批属性计算,不参与(0),参与计算(1)
     */
    private Byte isCalculation;

    /**
     * 是否参与盘点,不参与(0),参与(1)
     */
    private Byte isStoreCheck;

    /**
     * 用户录入批属性ID batchattributeinfo 表中 AttributeValue_Id
     */
    private Long attributeValueId = 0L;

    /**
     * 用户录入属性名称 batchattributeinfo 表中 AttributeValueName 如果是日期则格式化: yyyy-MM-dd HH:mm:ss
     */
    private String attributeValueName;

    public Long getDicId() {
        return dicId;
    }

    public void setDicId(Long dicId) {
        this.dicId = dicId;
    }

    public String getAttributeName() {
        return attributeName;
    }

    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    public Byte getIsCalculation() {
        return isCalculation;
    }

    public void setIsCalculation(Byte isCalculation) {
        this.isCalculation = isCalculation;
    }

    public Byte getIsStoreCheck() {
        return isStoreCheck;
    }

    public void setIsStoreCheck(Byte isStoreCheck) {
        this.isStoreCheck = isStoreCheck;
    }

    public Long getAttributeValueId() {
        return attributeValueId;
    }

    public void setAttributeValueId(Long attributeValueId) {
        this.attributeValueId = attributeValueId;
    }

    public String getAttributeValueName() {
        return attributeValueName;
    }

    public void setAttributeValueName(String attributeValueName) {
        this.attributeValueName = attributeValueName;
    }
}
