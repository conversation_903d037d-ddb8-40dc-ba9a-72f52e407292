package com.yijiupi.himalaya.supplychain.inventory.service;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;

/**
 * 经销商费用管理
 * 
 * @author: lidengfeng
 * @date 2018/9/27 9:03
 */
public interface IDealerChargeConfigService {

    /**
     * 新增或修改经销商费用
     * 
     * @param dto
     * @return: void
     */
    void saveOrUpdateDealerChargeConfig(DealerChargeConfigDTO dto);

    /**
     * 得到经销商是否存在
     * 
     * @param
     * @return
     */
    Boolean selectCountByDealerId(DealerCountQuery dealerCountQuery);

    /**
     * 根据服务商id查询经销商费用配置
     * 
     * @param
     * @return
     */
    PageList<DealerChargeConfigDTO> selectDealerChargeList(DealerChargeConfigQuery dealerChargeConfigQuery);

    /**
     * 根据服务商id查询经销商费用配置
     * 
     * @param
     * @return
     */
    PageList<DealerChargeConfigDTO> selectDealerList(DealerChargeConfigQuery dealerChargeConfigQuery);

    /**
     * 启用停用经销商费用配置
     *
     * @param dto
     * @return
     */
    void updateDealerConfigStatus(DealerChargeConfigDTO dto);

    /**
     * 根据经销商id，城市id查询仓库信息 仓配服务信息
     * 
     * @param dealerWarehouseQuery
     * @return
     */
    PageList<DealerWarehouseDTO> selectDealerWarehouseList(DealerWarehouseQuery dealerWarehouseQuery);

    /**
     * 经销商费用配置明细查询
     * 
     * @param
     * @return
     */
    DealerChargeConfigDTO selectDealerChargeConfigById(DealerCountQuery dealerCountQuery);

}
