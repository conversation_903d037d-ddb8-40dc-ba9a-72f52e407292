package com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class BatchAttributeValueSaveItemDTO implements Serializable {

    private static final long serialVersionUID = -4073549081737146107L;

    /**
     * 关联信息ID
     */
    private Long refInfoId;

    /**
     * 产品skuId
     */
    private Long productSkuId;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * 货主ID
     */
    private Long ownerId;

    /**
     * 二级货主ID/供应商ID
     */
    private Long secOwnerId;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 产品批次时间
     */
    private Date batchTime;

    /**
     * 批属性值集合
     */
    private List<BatchAttributeValueSaveItemDetailDTO> itemDetailList;

    public Long getRefInfoId() {
        return refInfoId;
    }

    public void setRefInfoId(Long refInfoId) {
        this.refInfoId = refInfoId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public List<BatchAttributeValueSaveItemDetailDTO> getItemDetailList() {
        return itemDetailList;
    }

    public void setItemDetailList(List<BatchAttributeValueSaveItemDetailDTO> itemDetailList) {
        this.itemDetailList = itemDetailList;
    }
}
