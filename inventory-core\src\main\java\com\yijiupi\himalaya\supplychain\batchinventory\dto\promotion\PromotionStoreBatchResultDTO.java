package com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion;

import java.io.Serializable;
import java.util.Date;

/**
 * 促销批次库存查询dto
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
public class PromotionStoreBatchResultDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 仓库
     */
    private Integer warehouseId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 产品规格id
     */
    private Long productSpecId;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 是否混合批次库存 0-否 1-是
     */
    private Byte isMixedBatch;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    /**
     * 批属性编号
     */
    private String batchAttributeInfoNo;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 是否有促销库存 0-否 1-是
     */
    private Byte hasPromotionBatch;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getProductSpecId() {
        return productSpecId;
    }

    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Byte getIsMixedBatch() {
        return isMixedBatch;
    }

    public void setIsMixedBatch(Byte isMixedBatch) {
        this.isMixedBatch = isMixedBatch;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public String getBatchAttributeInfoNo() {
        return batchAttributeInfoNo;
    }

    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Byte getHasPromotionBatch() {
        return hasPromotionBatch;
    }

    public void setHasPromotionBatch(Byte hasPromotionBatch) {
        this.hasPromotionBatch = hasPromotionBatch;
    }
}
